module.exports = {

"[project]/.next-internal/server/app/api/bills/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@opentelemetry/api", () => require("@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/validation/billing-schemas.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Comprehensive validation schemas for billing forms
// Provides robust client-side validation with detailed error messages in Chinese
__turbopack_context__.s({
    "billFilterSchema": (()=>billFilterSchema),
    "billFormSchema": (()=>billFormSchema),
    "billItemSchema": (()=>billItemSchema),
    "billStatusUpdateSchema": (()=>billStatusUpdateSchema),
    "formatValidationErrors": (()=>formatValidationErrors),
    "patientFormSchema": (()=>patientFormSchema),
    "paymentFormSchema": (()=>paymentFormSchema),
    "validateBillFilter": (()=>validateBillFilter),
    "validateBillForm": (()=>validateBillForm),
    "validateBillItem": (()=>validateBillItem),
    "validateBillStatusUpdate": (()=>validateBillStatusUpdate),
    "validatePatientForm": (()=>validatePatientForm),
    "validatePaymentForm": (()=>validatePaymentForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.24.2/node_modules/zod/lib/index.mjs [app-route] (ecmascript)");
;
// Common validation patterns
const positiveNumber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '金额不能为负数');
const requiredString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().min(1, '此字段为必填项');
const optionalString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])();
const phoneRegex = /^1[3-9]\d{9}$/;
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
// Custom validation functions
const validateCurrency = (value)=>{
    if (value < 0) return false;
    // Check for reasonable decimal places (max 2)
    const decimalPlaces = (value.toString().split('.')[1] || '').length;
    return decimalPlaces <= 2;
};
const validateDateNotInPast = (date)=>{
    const inputDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return inputDate >= today;
};
const validateDateNotTooFarInFuture = (date)=>{
    const inputDate = new Date(date);
    const maxDate = new Date();
    maxDate.setFullYear(maxDate.getFullYear() + 2); // Max 2 years in future
    return inputDate <= maxDate;
};
const billItemSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    itemType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'treatment',
        'consultation',
        'material',
        'service'
    ], {
        required_error: '请选择项目类型',
        invalid_type_error: '无效的项目类型'
    }),
    itemName: requiredString.max(100, '项目名称不能超过100个字符'),
    description: optionalString.max(500, '描述不能超过500个字符').optional(),
    quantity: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0.01, '数量必须大于0').max(9999, '数量不能超过9999').refine((val)=>{
        const decimalPlaces = (val.toString().split('.')[1] || '').length;
        return decimalPlaces <= 3;
    }, '数量最多支持3位小数'),
    unitPrice: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '单价不能为负数').max(999999.99, '单价不能超过999,999.99').refine(validateCurrency, '单价格式无效，最多支持2位小数'),
    discountRate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '折扣率不能为负数').max(100, '折扣率不能超过100%').optional()
}).refine((data)=>{
    // Validate that discount rate makes sense
    if (data.discountRate && data.discountRate > 0 && data.unitPrice === 0) {
        return false;
    }
    return true;
}, {
    message: '单价为0时不能设置折扣',
    path: [
        'discountRate'
    ]
});
const billFormSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    patient: requiredString.uuid('请选择有效的患者'),
    appointment: optionalString.uuid('请选择有效的预约').optional().or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["literal"])('')),
    treatment: optionalString.uuid('请选择有效的治疗项目').optional().or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["literal"])('')),
    billType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'treatment',
        'consultation',
        'deposit',
        'additional'
    ], {
        required_error: '请选择账单类型',
        invalid_type_error: '无效的账单类型'
    }),
    description: requiredString.min(2, '账单描述至少需要2个字符').max(200, '账单描述不能超过200个字符'),
    notes: optionalString.max(1000, '备注不能超过1000个字符').optional(),
    dueDate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().min(1, '请选择到期日期').refine((date)=>{
        try {
            new Date(date);
            return true;
        } catch  {
            return false;
        }
    }, '请输入有效的日期').refine(validateDateNotInPast, '到期日期不能是过去的日期').refine(validateDateNotTooFarInFuture, '到期日期不能超过2年'),
    discountAmount: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '折扣金额不能为负数').max(999999.99, '折扣金额不能超过999,999.99').refine(validateCurrency, '折扣金额格式无效').optional(),
    taxAmount: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '税费金额不能为负数').max(999999.99, '税费金额不能超过999,999.99').refine(validateCurrency, '税费金额格式无效').optional(),
    items: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["array"])(billItemSchema).min(1, '至少需要一个账单项目').max(50, '账单项目不能超过50个')
}).refine((data)=>{
    // Validate that bill has reasonable total
    const itemsTotal = data.items.reduce((sum, item)=>{
        const itemTotal = item.quantity * item.unitPrice;
        const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);
        return sum + (itemTotal - itemDiscount);
    }, 0);
    const discountAmount = data.discountAmount || 0;
    const taxAmount = data.taxAmount || 0;
    const finalTotal = itemsTotal + taxAmount - discountAmount;
    return finalTotal >= 0;
}, {
    message: '账单总金额不能为负数',
    path: [
        'discountAmount'
    ]
}).refine((data)=>{
    // Validate discount doesn't exceed subtotal
    const itemsTotal = data.items.reduce((sum, item)=>{
        const itemTotal = item.quantity * item.unitPrice;
        const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);
        return sum + (itemTotal - itemDiscount);
    }, 0);
    const discountAmount = data.discountAmount || 0;
    return discountAmount <= itemsTotal;
}, {
    message: '折扣金额不能超过项目小计',
    path: [
        'discountAmount'
    ]
});
const paymentFormSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    amount: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0.01, '支付金额必须大于0').max(999999.99, '支付金额不能超过999,999.99').refine(validateCurrency, '支付金额格式无效，最多支持2位小数'),
    paymentMethod: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'cash',
        'card',
        'wechat',
        'alipay',
        'transfer',
        'installment'
    ], {
        required_error: '请选择支付方式',
        invalid_type_error: '无效的支付方式'
    }),
    transactionId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().max(100, '交易ID不能超过100个字符').optional().or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["literal"])('')),
    notes: optionalString.max(500, '备注不能超过500个字符').optional()
}).refine((data)=>{
    // Require transaction ID for certain payment methods
    const methodsRequiringTransactionId = [
        'card',
        'wechat',
        'alipay',
        'transfer'
    ];
    if (methodsRequiringTransactionId.includes(data.paymentMethod)) {
        return data.transactionId && data.transactionId.trim().length > 0;
    }
    return true;
}, {
    message: '此支付方式需要提供交易ID',
    path: [
        'transactionId'
    ]
});
const patientFormSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    fullName: requiredString.min(2, '姓名至少需要2个字符').max(50, '姓名不能超过50个字符').regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/, '姓名只能包含中文、英文和空格'),
    phone: requiredString.regex(phoneRegex, '请输入有效的手机号码'),
    email: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().email('请输入有效的邮箱地址').max(100, '邮箱地址不能超过100个字符').optional().or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["literal"])('')),
    medicalNotes: optionalString.max(2000, '医疗备注不能超过2000个字符').optional()
});
const billStatusUpdateSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'draft',
        'sent',
        'confirmed',
        'paid',
        'cancelled'
    ], {
        required_error: '请选择账单状态',
        invalid_type_error: '无效的账单状态'
    }),
    notes: optionalString.max(500, '状态更新备注不能超过500个字符').optional()
});
const billFilterSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    search: optionalString.max(100, '搜索关键词不能超过100个字符').optional(),
    status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'draft',
        'sent',
        'confirmed',
        'paid',
        'cancelled'
    ]).optional(),
    billType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'treatment',
        'consultation',
        'deposit',
        'additional'
    ]).optional(),
    patientId: optionalString.uuid('请选择有效的患者').optional().or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["literal"])('')),
    dateFrom: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().optional().refine((date)=>{
        if (!date) return true;
        try {
            new Date(date);
            return true;
        } catch  {
            return false;
        }
    }, '请输入有效的开始日期'),
    dateTo: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().optional().refine((date)=>{
        if (!date) return true;
        try {
            new Date(date);
            return true;
        } catch  {
            return false;
        }
    }, '请输入有效的结束日期'),
    amountMin: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '最小金额不能为负数').max(999999.99, '最小金额不能超过999,999.99').optional(),
    amountMax: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '最大金额不能为负数').max(999999.99, '最大金额不能超过999,999.99').optional()
}).refine((data)=>{
    // Validate date range
    if (data.dateFrom && data.dateTo) {
        const fromDate = new Date(data.dateFrom);
        const toDate = new Date(data.dateTo);
        return fromDate <= toDate;
    }
    return true;
}, {
    message: '开始日期不能晚于结束日期',
    path: [
        'dateTo'
    ]
}).refine((data)=>{
    // Validate amount range
    if (data.amountMin !== undefined && data.amountMax !== undefined) {
        return data.amountMin <= data.amountMax;
    }
    return true;
}, {
    message: '最小金额不能大于最大金额',
    path: [
        'amountMax'
    ]
});
const validateBillItem = (data)=>{
    return billItemSchema.safeParse(data);
};
const validateBillForm = (data)=>{
    return billFormSchema.safeParse(data);
};
const validatePaymentForm = (data)=>{
    return paymentFormSchema.safeParse(data);
};
const validatePatientForm = (data)=>{
    return patientFormSchema.safeParse(data);
};
const validateBillStatusUpdate = (data)=>{
    return billStatusUpdateSchema.safeParse(data);
};
const validateBillFilter = (data)=>{
    return billFilterSchema.safeParse(data);
};
const formatValidationErrors = (errors)=>{
    return errors.errors.map((error)=>({
            field: error.path.join('.'),
            message: error.message,
            code: error.code
        }));
};
}}),
"[project]/src/app/api/bills/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$12$2e$12_next$40$_f2abd70610af0db25eb1f4c2a7e1d3bb$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server/auth.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$billing$2d$schemas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/billing-schemas.ts [app-route] (ecmascript)");
;
;
;
;
const BACKEND_URL = ("TURBOPACK compile-time value", "http://localhost:8002") || 'http://localhost:8002';
// Enhanced error handling utility
class APIError extends Error {
    status;
    code;
    details;
    constructor(message, status = 500, code, details){
        super(message), this.status = status, this.code = code, this.details = details;
        this.name = 'APIError';
    }
}
// Utility to get user info from Clerk with error handling
async function getClerkUserInfo(userId) {
    try {
        const response = await fetch(`https://api.clerk.com/v1/users/${userId}`, {
            headers: {
                Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`
            }
        });
        if (!response.ok) {
            throw new APIError('Failed to fetch user information', 401, 'CLERK_USER_FETCH_ERROR');
        }
        return await response.json();
    } catch (error) {
        console.error('Error fetching Clerk user:', error);
        throw new APIError('Authentication service unavailable', 503, 'AUTH_SERVICE_ERROR');
    }
}
// Utility to make backend requests with comprehensive error handling
async function makeBackendRequest(url, options, userId, userEmail) {
    try {
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                'x-clerk-user-id': userId,
                'x-user-email': userEmail,
                ...options.headers
            }
        });
        const data = await response.json();
        if (!response.ok) {
            throw new APIError(data.error || `Backend request failed: ${response.status}`, response.status, data.code || 'BACKEND_ERROR', data);
        }
        return data;
    } catch (error) {
        if (error instanceof APIError) {
            throw error;
        }
        console.error('Backend request error:', error);
        throw new APIError('Backend service unavailable', 503, 'BACKEND_SERVICE_ERROR');
    }
}
async function GET(request) {
    try {
        const { userId } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$12$2e$12_next$40$_f2abd70610af0db25eb1f4c2a7e1d3bb$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auth"])();
        if (!userId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Authentication required',
                code: 'AUTH_REQUIRED',
                message: '请先登录以访问账单数据'
            }, {
                status: 401
            });
        }
        // Validate query parameters
        const url = new URL(request.url);
        const queryParams = Object.fromEntries(url.searchParams.entries());
        // Basic query parameter validation
        if (queryParams.limit && (isNaN(Number(queryParams.limit)) || Number(queryParams.limit) > 100)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid limit parameter',
                code: 'INVALID_LIMIT',
                message: '分页限制必须是1-100之间的数字'
            }, {
                status: 400
            });
        }
        if (queryParams.page && (isNaN(Number(queryParams.page)) || Number(queryParams.page) < 1)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid page parameter',
                code: 'INVALID_PAGE',
                message: '页码必须是大于0的数字'
            }, {
                status: 400
            });
        }
        // Get user info from Clerk with error handling
        const user = await getClerkUserInfo(userId);
        const userEmail = user.email_addresses[0]?.email_address || '';
        if (!userEmail) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'User email not found',
                code: 'USER_EMAIL_MISSING',
                message: '用户邮箱信息缺失，请联系管理员'
            }, {
                status: 400
            });
        }
        // Forward request to backend with authentication headers
        const backendUrl = `${BACKEND_URL}/api/bills${url.search}`;
        const data = await makeBackendRequest(backendUrl, {
            method: 'GET'
        }, userId, userEmail);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data);
    } catch (error) {
        if (error instanceof APIError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: error.message,
                code: error.code,
                message: error.message
            }, {
                status: error.status
            });
        }
        console.error('Unexpected error in GET /api/bills:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error',
            code: 'INTERNAL_ERROR',
            message: '服务器内部错误，请稍后重试'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const { userId } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$12$2e$12_next$40$_f2abd70610af0db25eb1f4c2a7e1d3bb$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auth"])();
        if (!userId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Authentication required',
                code: 'AUTH_REQUIRED',
                message: '请先登录以创建账单'
            }, {
                status: 401
            });
        }
        // Parse and validate request body
        let body;
        try {
            body = await request.json();
        } catch (error) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid JSON in request body',
                code: 'INVALID_JSON',
                message: '请求数据格式错误'
            }, {
                status: 400
            });
        }
        // Validate bill data using Zod schema
        const validationResult = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$billing$2d$schemas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["billFormSchema"].safeParse(body);
        if (!validationResult.success) {
            const formattedErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$billing$2d$schemas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatValidationErrors"])(validationResult.error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Validation failed',
                code: 'VALIDATION_ERROR',
                message: '账单数据验证失败',
                details: formattedErrors
            }, {
                status: 400
            });
        }
        // Get user info from Clerk with error handling
        const user = await getClerkUserInfo(userId);
        const userEmail = user.email_addresses[0]?.email_address || '';
        if (!userEmail) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'User email not found',
                code: 'USER_EMAIL_MISSING',
                message: '用户邮箱信息缺失，请联系管理员'
            }, {
                status: 400
            });
        }
        // Add additional server-side business logic validation
        const validatedData = validationResult.data;
        // Validate bill items total matches subtotal
        const itemsTotal = validatedData.items.reduce((sum, item)=>{
            const itemTotal = item.quantity * item.unitPrice;
            const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);
            return sum + (itemTotal - itemDiscount);
        }, 0);
        const calculatedSubtotal = Math.round(itemsTotal * 100) / 100;
        const providedSubtotal = Math.round((validatedData.subtotal || itemsTotal) * 100) / 100;
        if (Math.abs(calculatedSubtotal - providedSubtotal) > 0.01) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Subtotal mismatch',
                code: 'SUBTOTAL_MISMATCH',
                message: `计算的小计金额 (¥${calculatedSubtotal}) 与提供的小计金额 (¥${providedSubtotal}) 不匹配`
            }, {
                status: 400
            });
        }
        // Forward request to backend with authentication headers
        const backendUrl = `${BACKEND_URL}/api/bills`;
        const data = await makeBackendRequest(backendUrl, {
            method: 'POST',
            body: JSON.stringify(validatedData)
        }, userId, userEmail);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data, {
            status: 201
        });
    } catch (error) {
        if (error instanceof APIError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: error.message,
                code: error.code,
                message: error.message,
                details: error.details
            }, {
                status: error.status
            });
        }
        console.error('Unexpected error in POST /api/bills:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error',
            code: 'INTERNAL_ERROR',
            message: '创建账单时发生服务器错误，请稍后重试'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b65d65d3._.js.map