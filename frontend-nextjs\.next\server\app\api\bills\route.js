const CHUNK_PUBLIC_PATH = "server/app/api/bills/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_acfea7f7._.js");
runtime.loadChunk("server/chunks/c64f8_next_76d11764._.js");
runtime.loadChunk("server/chunks/a5167_@clerk_backend_dist_b67e932e._.js");
runtime.loadChunk("server/chunks/05b4d_zod_lib_index_mjs_17c65e86._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_cc146b36._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__b65d65d3._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/bills/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/bills/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/bills/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
