/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bson-objectid@2.0.4";
exports.ids = ["vendor-chunks/bson-objectid@2.0.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/bson-objectid@2.0.4/node_modules/bson-objectid/objectid.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/bson-objectid@2.0.4/node_modules/bson-objectid/objectid.js ***!
  \***************************************************************************************/
/***/ ((module) => {

eval("\nvar MACHINE_ID = Math.floor(Math.random() * 0xFFFFFF);\nvar index = ObjectID.index = parseInt(Math.random() * 0xFFFFFF, 10);\nvar pid = (typeof process === 'undefined' || typeof process.pid !== 'number' ? Math.floor(Math.random() * 100000) : process.pid) % 0xFFFF;\n// <https://github.com/williamkapke/bson-objectid/pull/51>\n// Attempt to fallback Buffer if _Buffer is undefined (e.g. for Node.js).\n// Worst case fallback to null and handle with null checking before using.\nvar BufferCtr = (() => { try { return _Buffer; }catch(_){ try{ return Buffer; }catch(_){ return null; } } })();\n\n/**\n * Determine if an object is Buffer\n *\n * Author:   Feross Aboukhadijeh <<EMAIL>> <http://feross.org>\n * License:  MIT\n *\n */\nvar isBuffer = function (obj) {\n  return !!(\n  obj != null &&\n  obj.constructor &&\n  typeof obj.constructor.isBuffer === 'function' &&\n  obj.constructor.isBuffer(obj)\n  )\n};\n\n// Precomputed hex table enables speedy hex string conversion\nvar hexTable = [];\nfor (var i = 0; i < 256; i++) {\n  hexTable[i] = (i <= 15 ? '0' : '') + i.toString(16);\n}\n\n// Regular expression that checks for hex value\nvar checkForHexRegExp = new RegExp('^[0-9a-fA-F]{24}$');\n\n// Lookup tables\nvar decodeLookup = [];\ni = 0;\nwhile (i < 10) decodeLookup[0x30 + i] = i++;\nwhile (i < 16) decodeLookup[0x41 - 10 + i] = decodeLookup[0x61 - 10 + i] = i++;\n\n/**\n * Create a new immutable ObjectID instance\n *\n * @class Represents the BSON ObjectID type\n * @param {String|Number} id Can be a 24 byte hex string, 12 byte binary string or a Number.\n * @return {Object} instance of ObjectID.\n */\nfunction ObjectID(id) {\n  if(!(this instanceof ObjectID)) return new ObjectID(id);\n  if(id && ((id instanceof ObjectID) || id._bsontype===\"ObjectID\"))\n    return id;\n\n  this._bsontype = 'ObjectID';\n\n  // The most common usecase (blank id, new objectId instance)\n  if (id == null || typeof id === 'number') {\n    // Generate a new id\n    this.id = this.generate(id);\n    // Return the object\n    return;\n  }\n\n  // Check if the passed in id is valid\n  var valid = ObjectID.isValid(id);\n\n  // Throw an error if it's not a valid setup\n  if (!valid && id != null) {\n    throw new Error(\n      'Argument passed in must be a single String of 12 bytes or a string of 24 hex characters'\n    );\n  } else if (valid && typeof id === 'string' && id.length === 24) {\n    return ObjectID.createFromHexString(id);\n  } else if (id != null && id.length === 12) {\n    // assume 12 byte string\n    this.id = id;\n  } else if (id != null && typeof id.toHexString === 'function') {\n    // Duck-typing to support ObjectId from different npm packages\n    return id;\n  } else {\n    throw new Error(\n      'Argument passed in must be a single String of 12 bytes or a string of 24 hex characters'\n    );\n  }\n}\nmodule.exports = ObjectID;\nObjectID.default = ObjectID;\n\n/**\n * Creates an ObjectID from a second based number, with the rest of the ObjectID zeroed out. Used for comparisons or sorting the ObjectID.\n *\n * @param {Number} time an integer number representing a number of seconds.\n * @return {ObjectID} return the created ObjectID\n * @api public\n */\nObjectID.createFromTime = function(time){\n  time = parseInt(time, 10) % 0xFFFFFFFF;\n  return new ObjectID(hex(8,time)+\"0000000000000000\");\n};\n\n/**\n * Creates an ObjectID from a hex string representation of an ObjectID.\n *\n * @param {String} hexString create a ObjectID from a passed in 24 byte hexstring.\n * @return {ObjectID} return the created ObjectID\n * @api public\n */\nObjectID.createFromHexString = function(hexString) {\n  // Throw an error if it's not a valid setup\n  if (typeof hexString === 'undefined' || (hexString != null && hexString.length !== 24)) {\n    throw new Error(\n      'Argument passed in must be a single String of 12 bytes or a string of 24 hex characters'\n    );\n  }\n\n  // Calculate lengths\n  var data = '';\n  var i = 0;\n\n  while (i < 24) {\n    data += String.fromCharCode((decodeLookup[hexString.charCodeAt(i++)] << 4) | decodeLookup[hexString.charCodeAt(i++)]);\n  }\n\n  return new ObjectID(data);\n};\n\n/**\n * Checks if a value is a valid bson ObjectId\n *\n * @param {String} objectid Can be a 24 byte hex string or an instance of ObjectID.\n * @return {Boolean} return true if the value is a valid bson ObjectID, return false otherwise.\n * @api public\n *\n * THE NATIVE DOCUMENTATION ISN'T CLEAR ON THIS GUY!\n * http://mongodb.github.io/node-mongodb-native/api-bson-generated/objectid.html#objectid-isvalid\n */\nObjectID.isValid = function(id) {\n  if (id == null) return false;\n\n  if (typeof id === 'number') {\n    return true;\n  }\n\n  if (typeof id === 'string') {\n    return id.length === 12 || (id.length === 24 && checkForHexRegExp.test(id));\n  }\n\n  if (id instanceof ObjectID) {\n    return true;\n  }\n\n  // <https://github.com/williamkapke/bson-objectid/issues/53>\n  if (isBuffer(id)) {\n    return ObjectID.isValid(id.toString('hex'));\n  }\n\n  // Duck-Typing detection of ObjectId like objects\n  // <https://github.com/williamkapke/bson-objectid/pull/51>\n  if (typeof id.toHexString === 'function') {\n    if(\n      BufferCtr &&\n      (id.id instanceof BufferCtr || typeof id.id === 'string')\n    ) {\n      return id.id.length === 12 || (id.id.length === 24 && checkForHexRegExp.test(id.id));\n    }\n  }\n\n  return false;\n};\n\nObjectID.prototype = {\n  constructor: ObjectID,\n\n  /**\n   * Return the ObjectID id as a 24 byte hex string representation\n   *\n   * @return {String} return the 24 byte hex string representation.\n   * @api public\n   */\n  toHexString: function() {\n    if (!this.id || !this.id.length) {\n      throw new Error(\n        'invalid ObjectId, ObjectId.id must be either a string or a Buffer, but is [' +\n          JSON.stringify(this.id) +\n          ']'\n      );\n    }\n\n    if (this.id.length === 24) {\n      return this.id;\n    }\n\n    if (isBuffer(this.id)) {\n      return this.id.toString('hex')\n    }\n\n    var hexString = '';\n    for (var i = 0; i < this.id.length; i++) {\n      hexString += hexTable[this.id.charCodeAt(i)];\n    }\n\n    return hexString;\n  },\n\n  /**\n   * Compares the equality of this ObjectID with `otherID`.\n   *\n   * @param {Object} otherId ObjectID instance to compare against.\n   * @return {Boolean} the result of comparing two ObjectID's\n   * @api public\n   */\n  equals: function (otherId){\n    if (otherId instanceof ObjectID) {\n      return this.toString() === otherId.toString();\n    } else if (\n      typeof otherId === 'string' &&\n      ObjectID.isValid(otherId) &&\n      otherId.length === 12 &&\n      isBuffer(this.id)\n    ) {\n      return otherId === this.id.toString('binary');\n    } else if (typeof otherId === 'string' && ObjectID.isValid(otherId) && otherId.length === 24) {\n      return otherId.toLowerCase() === this.toHexString();\n    } else if (typeof otherId === 'string' && ObjectID.isValid(otherId) && otherId.length === 12) {\n      return otherId === this.id;\n    } else if (otherId != null && (otherId instanceof ObjectID || otherId.toHexString)) {\n      return otherId.toHexString() === this.toHexString();\n    } else {\n      return false;\n    }\n  },\n\n  /**\n   * Returns the generation date (accurate up to the second) that this ID was generated.\n   *\n   * @return {Date} the generation date\n   * @api public\n   */\n  getTimestamp: function(){\n    var timestamp = new Date();\n    var time;\n    if (isBuffer(this.id)) {\n      time = this.id[3] | (this.id[2] << 8) | (this.id[1] << 16) | (this.id[0] << 24);\n    } else {\n      time = this.id.charCodeAt(3) | (this.id.charCodeAt(2) << 8) | (this.id.charCodeAt(1) << 16) | (this.id.charCodeAt(0) << 24);\n    }\n    timestamp.setTime(Math.floor(time) * 1000);\n    return timestamp;\n  },\n\n  /**\n  * Generate a 12 byte id buffer used in ObjectID's\n  *\n  * @method\n  * @param {number} [time] optional parameter allowing to pass in a second based timestamp.\n  * @return {string} return the 12 byte id buffer string.\n  */\n  generate: function (time) {\n    if ('number' !== typeof time) {\n      time = ~~(Date.now() / 1000);\n    }\n\n    //keep it in the ring!\n    time = parseInt(time, 10) % 0xFFFFFFFF;\n\n    var inc = next();\n\n    return String.fromCharCode(\n      ((time >> 24) & 0xFF),\n      ((time >> 16) & 0xFF),\n      ((time >> 8) & 0xFF),\n      (time & 0xFF),\n      ((MACHINE_ID >> 16) & 0xFF),\n      ((MACHINE_ID >> 8) & 0xFF),\n      (MACHINE_ID & 0xFF),\n      ((pid >> 8) & 0xFF),\n      (pid & 0xFF),\n      ((inc >> 16) & 0xFF),\n      ((inc >> 8) & 0xFF),\n      (inc & 0xFF)\n    )\n  },\n};\n\nfunction next() {\n  return index = (index+1) % 0xFFFFFF;\n}\n\nfunction hex(length, n) {\n  n = n.toString(16);\n  return (n.length===length)? n : \"00000000\".substring(n.length, length) + n;\n}\n\nfunction buffer(str) {\n  var i=0,out=[];\n\n  if(str.length===24)\n    for(;i<24; out.push(parseInt(str[i]+str[i+1], 16)),i+=2);\n\n  else if(str.length===12)\n    for(;i<12; out.push(str.charCodeAt(i)),i++);\n\n  return out;\n}\n\nvar inspect = (Symbol && Symbol.for && Symbol.for('nodejs.util.inspect.custom')) || 'inspect';\n\n/**\n * Converts to a string representation of this Id.\n *\n * @return {String} return the 24 byte hex string representation.\n * @api private\n */\nObjectID.prototype[inspect] = function() { return \"ObjectID(\"+this+\")\" };\nObjectID.prototype.toJSON = ObjectID.prototype.toHexString;\nObjectID.prototype.toString = ObjectID.prototype.toHexString;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/bson-objectid@2.0.4/node_modules/bson-objectid/objectid.js\n");

/***/ })

};
;