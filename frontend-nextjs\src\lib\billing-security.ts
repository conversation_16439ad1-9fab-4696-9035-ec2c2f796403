// Comprehensive security measures for billing system
// Handles data encryption, audit logging, and secure payment processing

import crypto from 'crypto';

// Security configuration
const SECURITY_CONFIG = {
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16,
    tagLength: 16,
  },
  audit: {
    maxLogSize: 10000, // Maximum number of audit log entries
    sensitiveFields: ['cardNumber', 'cvv', 'bankAccount', 'transactionId'],
  },
  rateLimit: {
    maxRequestsPerMinute: 60,
    maxPaymentRequestsPerHour: 10,
  },
};

// Encryption utilities for sensitive financial data
export class DataEncryption {
  private static getEncryptionKey(): string {
    const key = process.env.BILLING_ENCRYPTION_KEY;
    if (!key) {
      throw new Error('BILLING_ENCRYPTION_KEY environment variable is required');
    }
    return key;
  }

  /**
   * Encrypt sensitive data
   */
  static encrypt(data: string): { encrypted: string; iv: string; tag: string } {
    try {
      const key = Buffer.from(this.getEncryptionKey(), 'hex');
      const iv = crypto.randomBytes(SECURITY_CONFIG.encryption.ivLength);
      const cipher = crypto.createCipher(SECURITY_CONFIG.encryption.algorithm, key);
      cipher.setAAD(Buffer.from('billing-data'));

      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();

      return {
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex'),
      };
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt sensitive data');
    }
  }

  /**
   * Decrypt sensitive data
   */
  static decrypt(encryptedData: { encrypted: string; iv: string; tag: string }): string {
    try {
      const key = Buffer.from(this.getEncryptionKey(), 'hex');
      const iv = Buffer.from(encryptedData.iv, 'hex');
      const tag = Buffer.from(encryptedData.tag, 'hex');
      
      const decipher = crypto.createDecipher(SECURITY_CONFIG.encryption.algorithm, key);
      decipher.setAAD(Buffer.from('billing-data'));
      decipher.setAuthTag(tag);

      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt sensitive data');
    }
  }

  /**
   * Hash sensitive data for comparison (one-way)
   */
  static hash(data: string): string {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  /**
   * Generate secure random token
   */
  static generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }
}

// Audit logging for financial operations
export interface AuditLogEntry {
  id: string;
  timestamp: Date;
  userId: string;
  userEmail: string;
  action: string;
  resource: string;
  resourceId?: string;
  details: any;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
}

export class AuditLogger {
  private static instance: AuditLogger;
  private auditLog: AuditLogEntry[] = [];

  private constructor() {}

  static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  /**
   * Log financial operation
   */
  logFinancialOperation(
    userId: string,
    userEmail: string,
    action: string,
    resource: string,
    details: any,
    success: boolean = true,
    errorMessage?: string,
    request?: Request
  ): void {
    const entry: AuditLogEntry = {
      id: DataEncryption.generateSecureToken(16),
      timestamp: new Date(),
      userId,
      userEmail,
      action,
      resource,
      resourceId: details.id || details.billId || details.paymentId,
      details: this.sanitizeDetails(details),
      ipAddress: this.getClientIP(request),
      userAgent: request?.headers.get('user-agent') || undefined,
      success,
      errorMessage,
    };

    this.auditLog.push(entry);

    // Keep only the most recent entries
    if (this.auditLog.length > SECURITY_CONFIG.audit.maxLogSize) {
      this.auditLog.shift();
    }

    // Log to console for development (in production, this should go to a secure logging service)
    console.log(`[AUDIT] ${entry.timestamp.toISOString()} - ${entry.action} on ${entry.resource} by ${entry.userEmail} - ${entry.success ? 'SUCCESS' : 'FAILED'}`);
  }

  /**
   * Get audit log entries (filtered for security)
   */
  getAuditLog(userId?: string, limit: number = 100): AuditLogEntry[] {
    let filteredLog = this.auditLog;

    if (userId) {
      filteredLog = filteredLog.filter(entry => entry.userId === userId);
    }

    return filteredLog
      .slice(-limit)
      .map(entry => ({
        ...entry,
        details: this.sanitizeDetails(entry.details),
      }));
  }

  /**
   * Remove sensitive information from audit details
   */
  private sanitizeDetails(details: any): any {
    if (!details || typeof details !== 'object') {
      return details;
    }

    const sanitized = { ...details };
    
    SECURITY_CONFIG.audit.sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = this.maskSensitiveData(sanitized[field]);
      }
    });

    return sanitized;
  }

  /**
   * Mask sensitive data for logging
   */
  private maskSensitiveData(data: string): string {
    if (data.length <= 4) {
      return '****';
    }
    return data.substring(0, 2) + '*'.repeat(data.length - 4) + data.substring(data.length - 2);
  }

  /**
   * Extract client IP address from request
   */
  private getClientIP(request?: Request): string | undefined {
    if (!request) return undefined;

    const forwarded = request.headers.get('x-forwarded-for');
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }

    const realIP = request.headers.get('x-real-ip');
    if (realIP) {
      return realIP;
    }

    return 'unknown';
  }
}

// Rate limiting for API endpoints
export class RateLimiter {
  private static instance: RateLimiter;
  private requestCounts: Map<string, { count: number; resetTime: number }> = new Map();
  private paymentCounts: Map<string, { count: number; resetTime: number }> = new Map();

  private constructor() {}

  static getInstance(): RateLimiter {
    if (!RateLimiter.instance) {
      RateLimiter.instance = new RateLimiter();
    }
    return RateLimiter.instance;
  }

  /**
   * Check if request is within rate limit
   */
  checkRateLimit(userId: string, isPaymentRequest: boolean = false): { allowed: boolean; resetTime?: number } {
    const now = Date.now();
    const limits = isPaymentRequest 
      ? { max: SECURITY_CONFIG.rateLimit.maxPaymentRequestsPerHour, window: 60 * 60 * 1000 }
      : { max: SECURITY_CONFIG.rateLimit.maxRequestsPerMinute, window: 60 * 1000 };

    const counts = isPaymentRequest ? this.paymentCounts : this.requestCounts;
    const userCount = counts.get(userId);

    if (!userCount || now > userCount.resetTime) {
      // Reset or initialize counter
      counts.set(userId, { count: 1, resetTime: now + limits.window });
      return { allowed: true };
    }

    if (userCount.count >= limits.max) {
      return { allowed: false, resetTime: userCount.resetTime };
    }

    userCount.count++;
    return { allowed: true };
  }

  /**
   * Clear expired rate limit entries
   */
  cleanup(): void {
    const now = Date.now();
    
    for (const [userId, data] of this.requestCounts.entries()) {
      if (now > data.resetTime) {
        this.requestCounts.delete(userId);
      }
    }

    for (const [userId, data] of this.paymentCounts.entries()) {
      if (now > data.resetTime) {
        this.paymentCounts.delete(userId);
      }
    }
  }
}

// Input sanitization for financial data
export class InputSanitizer {
  /**
   * Sanitize and validate monetary amounts
   */
  static sanitizeAmount(amount: any): number {
    if (typeof amount === 'number') {
      if (!isFinite(amount) || amount < 0) {
        throw new Error('Invalid amount: must be a positive finite number');
      }
      return Math.round(amount * 100) / 100; // Round to 2 decimal places
    }

    if (typeof amount === 'string') {
      const parsed = parseFloat(amount.replace(/[^\d.-]/g, ''));
      if (isNaN(parsed) || parsed < 0) {
        throw new Error('Invalid amount: must be a positive number');
      }
      return Math.round(parsed * 100) / 100;
    }

    throw new Error('Invalid amount: must be a number or numeric string');
  }

  /**
   * Sanitize text input to prevent XSS and injection attacks
   */
  static sanitizeText(text: string, maxLength: number = 1000): string {
    if (typeof text !== 'string') {
      throw new Error('Input must be a string');
    }

    // Remove potentially dangerous characters and HTML tags
    const sanitized = text
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<[^>]*>/g, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();

    if (sanitized.length > maxLength) {
      throw new Error(`Input too long: maximum ${maxLength} characters allowed`);
    }

    return sanitized;
  }

  /**
   * Validate and sanitize payment method
   */
  static sanitizePaymentMethod(method: string): string {
    const validMethods = ['cash', 'card', 'wechat', 'alipay', 'transfer', 'installment'];
    
    if (!validMethods.includes(method)) {
      throw new Error('Invalid payment method');
    }

    return method;
  }

  /**
   * Sanitize transaction ID
   */
  static sanitizeTransactionId(transactionId: string): string {
    if (typeof transactionId !== 'string') {
      throw new Error('Transaction ID must be a string');
    }

    // Allow only alphanumeric characters, hyphens, and underscores
    const sanitized = transactionId.replace(/[^a-zA-Z0-9\-_]/g, '');
    
    if (sanitized.length < 3 || sanitized.length > 100) {
      throw new Error('Transaction ID must be between 3 and 100 characters');
    }

    return sanitized;
  }
}

// Export singleton instances
export const auditLogger = AuditLogger.getInstance();
export const rateLimiter = RateLimiter.getInstance();

// Cleanup function to be called periodically
export const cleanupSecurity = () => {
  rateLimiter.cleanup();
};
