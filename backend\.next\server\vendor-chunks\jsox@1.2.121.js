"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsox@1.2.121";
exports.ids = ["vendor-chunks/jsox@1.2.121"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/jsox@1.2.121/node_modules/jsox/lib/jsox.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/.pnpm/jsox@1.2.121/node_modules/jsox/lib/jsox.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JSOX: () => (/* binding */ JSOX)\n/* harmony export */ });\n//\"use strict\";\n// jsox.js\n// JSOX JavaScript Object eXchange. Inherits human features of comments\n// and extended formatting from JSON6; adds macros, big number and date\n// support.  See README.md for details.\n//\n// This file is based off of https://github.com/JSON6/  ./lib/json6.js\n// which is based off of https://github.com/d3x0r/sack  ./src/netlib/html5.websocket/json6_parser.c\n//\n\n//const util = require('util'); // debug inspect.\n//import util from 'util'; \n\nconst _JSON=JSON; // in case someone does something like JSON=JSOX; we still need a primitive _JSON for internal stringification\nif( \"undefined\" === typeof exports )\n\tvar exports = {};\nconst JSOX = exports || {};\nexports.JSOX = JSOX;\n\nJSOX.version = \"1.2.121\";\n\n//function privateizeEverything() {\n//const _DEBUG_LL = false;\n//const _DEBUG_PARSING = false;\n//const _DEBUG_STRINGIFY = false;\n//const _DEBUG_PARSING_STACK = false;\n//const _DEBUG_PARSING_NUMBERS = false;\n//const _DEBUG_PARSING_DETAILS = false;\n//const _DEBUG_PARSING_CONTEXT = false;\n//const _DEBUG_REFERENCES = false; // this tracks folling context stack when the components have not been completed.\n//const _DEBUG_WHITESPACE = false; \nconst hasBigInt = (typeof BigInt === \"function\");\nconst testNonIdentifierCharacters = false; // maybe an option to enable; references otherwise unused table.\nconst VALUE_UNDEFINED = -1\nconst VALUE_UNSET = 0\nconst VALUE_NULL = 1\nconst VALUE_TRUE = 2\nconst VALUE_FALSE = 3\nconst VALUE_STRING = 4\nconst VALUE_NUMBER = 5\nconst VALUE_OBJECT = 6\nconst VALUE_NEG_NAN = 7\nconst VALUE_NAN = 8\nconst VALUE_NEG_INFINITY = 9\nconst VALUE_INFINITY = 10\n//const VALUE_DATE = 11  // unused yet; this is actuall a subType of VALUE_NUMBER\nconst VALUE_EMPTY = 12 // [,] makes an array with 'empty item'\nconst VALUE_ARRAY = 13 //\n// internally arrayType = -1 is a normal array\n// arrayType = -2 is a reference array, which, which closed is resolved to\n//     the specified object.\n// arrayType = -3 is a normal array, that has already had this element pushed.\nconst knownArrayTypeNames = [\"ab\",\"u8\",\"cu8\",\"s8\",\"u16\",\"s16\",\"u32\",\"s32\",\"u64\",\"s64\",\"f32\",\"f64\"];\nlet arrayToJSOX = null;\nlet mapToJSOX = null;\nconst knownArrayTypes = [ArrayBuffer\n                        ,Uint8Array,Uint8ClampedArray,Int8Array\n                        ,Uint16Array,Int16Array\n                        ,Uint32Array,Int32Array\n                        ,null,null//,Uint64Array,Int64Array\n                        ,Float32Array,Float64Array];\n// somehow max isn't used... it would be the NEXT available VALUE_XXX value...\n//const VALUE_ARRAY_MAX = VALUE_ARRAY + knownArrayTypes.length + 1; // 1 type is not typed; just an array.\n\nconst WORD_POS_RESET = 0;\nconst WORD_POS_TRUE_1 = 1;\nconst WORD_POS_TRUE_2 = 2;\nconst WORD_POS_TRUE_3 = 3;\nconst WORD_POS_FALSE_1 = 5;\nconst WORD_POS_FALSE_2 = 6;\nconst WORD_POS_FALSE_3 = 7;\nconst WORD_POS_FALSE_4 = 8;\nconst WORD_POS_NULL_1 = 9;\nconst WORD_POS_NULL_2 = 10;\nconst WORD_POS_NULL_3 = 11;\nconst WORD_POS_UNDEFINED_1 = 12;\nconst WORD_POS_UNDEFINED_2 = 13;\nconst WORD_POS_UNDEFINED_3 = 14;\nconst WORD_POS_UNDEFINED_4 = 15;\nconst WORD_POS_UNDEFINED_5 = 16;\nconst WORD_POS_UNDEFINED_6 = 17;\nconst WORD_POS_UNDEFINED_7 = 18;\nconst WORD_POS_UNDEFINED_8 = 19;\nconst WORD_POS_NAN_1 = 20;\nconst WORD_POS_NAN_2 = 21;\nconst WORD_POS_INFINITY_1 = 22;\nconst WORD_POS_INFINITY_2 = 23;\nconst WORD_POS_INFINITY_3 = 24;\nconst WORD_POS_INFINITY_4 = 25;\nconst WORD_POS_INFINITY_5 = 26;\nconst WORD_POS_INFINITY_6 = 27;\nconst WORD_POS_INFINITY_7 = 28;\n\nconst WORD_POS_FIELD = 29;\nconst WORD_POS_AFTER_FIELD = 30;\nconst WORD_POS_END = 31;\nconst WORD_POS_AFTER_FIELD_VALUE = 32;\n//const WORD_POS_BINARY = 32;\n\nconst CONTEXT_UNKNOWN = 0\nconst CONTEXT_IN_ARRAY = 1\nconst CONTEXT_OBJECT_FIELD = 2\nconst CONTEXT_OBJECT_FIELD_VALUE = 3\nconst CONTEXT_CLASS_FIELD = 4\nconst CONTEXT_CLASS_VALUE = 5\nconst CONTEXT_CLASS_FIELD_VALUE = 6\nconst keywords = {\t[\"true\"]:true,[\"false\"]:false,[\"null\"]:null,[\"NaN\"]:NaN,[\"Infinity\"]:Infinity,[\"undefined\"]:undefined }\n\n/*\nExtend Date type with a nanosecond field.\n*/\nclass DateNS extends Date {\n\tconstructor(a,b ) {\n\t\tsuper(a);\n\t\tthis.ns = b||0;\n\t}\t\n}\n\nJSOX.DateNS = DateNS;\n\nconst contexts = [];\nfunction getContext() {\n\tlet ctx = contexts.pop();\n\tif( !ctx )\n\t\tctx = { context : CONTEXT_UNKNOWN\n\t\t      , current_proto : null\n\t\t      , current_class : null\n\t\t      , current_class_field : 0\n\t\t      , arrayType : -1\n\t\t      , valueType : VALUE_UNSET\n\t\t      , elements : null\n\t\t      };\n\treturn ctx;\n}\nfunction dropContext(ctx) { \n\tcontexts.push( ctx ) \n}\n\nJSOX.updateContext = function() {\n    //if( toProtoTypes.get( Map.prototype ) ) return;\n    //console.log( \"Do init protoypes for new context objects...\" );\n    //initPrototypes();\n}\n\nconst buffers = [];\nfunction getBuffer() { let buf = buffers.pop(); if( !buf ) buf = { buf:null, n:0 }; else buf.n = 0; return buf; }\nfunction dropBuffer(buf) { buffers.push( buf ); }\n\n/**\n * @param {string} string \n * @returns {string}\n */\nJSOX.escape = function(string) {\n\tlet n;\n\tlet output = '';\n\tif( !string ) return string;\n\tfor( n = 0; n < string.length; n++ ) {\n\t\tif( ( string[n] == '\"' ) || ( string[n] == '\\\\' ) || ( string[n] == '`' )|| ( string[n] == '\\'' )) {\n\t\t\toutput += '\\\\';\n\t\t}\n\t\toutput += string[n];\n\t}\n\treturn output;\n}\n\n\nlet toProtoTypes = new WeakMap();\nlet toObjectTypes = new Map();\nlet fromProtoTypes = new Map();\nlet commonClasses = [];\n\nJSOX.reset = resetJSOX;\n\nfunction resetJSOX() {\n\ttoProtoTypes = new WeakMap();\n\ttoObjectTypes = new Map();\n\tfromProtoTypes = new Map();\n\tcommonClasses = [];\t\n}\n\n/**\n * @param {(value:any)} [cb]\n * @param {(this: unknown, key: string, value: unknown) => any} [reviver] \n * @returns {none}\n*/\nJSOX.begin = function( cb, reviver ) {\n\n\tconst val = { name : null,\t  // name of this value (if it's contained in an object)\n\t\t\tvalue_type: VALUE_UNSET, // value from above indiciating the type of this value\n\t\t\tstring : '',   // the string value of this value (strings and number types only)\n\t\t\tcontains : null,\n\t\t\tclassName : null,\n\t\t};\n\t\n\tconst pos = { line:1, col:1 };\n\tlet\tn = 0;\n\tlet     str;\n\tlet\tlocalFromProtoTypes = new Map();\n\tlet\tword = WORD_POS_RESET,\n\t\tstatus = true,\n\t\tredefineClass = false,\n\t\tnegative = false,\n\t\tresult = null,\n\t\trootObject = null,\n\t\telements = undefined,\n\t\tcontext_stack = {\n\t\t\tfirst : null,\n\t\t\tlast : null,\n\t\t\tsaved : null,\n\t\t\tpush(node) {\n\t\t\t\t//_DEBUG_PARSING_CONTEXT && console.log( \"pushing context:\", node );\n\t\t\t\tlet recover = this.saved;\n\t\t\t\tif( recover ) { this.saved = recover.next; \n\t\t\t\t\trecover.node = node; \n\t\t\t\t\trecover.next = null; \n\t\t\t\t\trecover.prior = this.last; }\n\t\t\t\telse { recover = { node : node, next : null, prior : this.last }; }\n\t\t\t\tif( !this.last ) this.first = recover;\n\t\t\t\telse this.last.next = recover;\n\t\t\t\tthis.last = recover;\n\t\t\t\tthis.length++;\n\t\t\t},\n\t\t\tpop() {\n\t\t\t\tlet result = this.last;\n\t\t\t\t// through normal usage this line can never be used.\n\t\t\t\t//if( !result ) return null;\n\t\t\t\tif( !(this.last = result.prior ) ) this.first = null;\n\t\t\t\tresult.next = this.saved;\n\t\t\t\tif( this.last ) this.last.next = null;\n\t\t\t\tif( !result.next ) result.first = null;\n\t\t\t\tthis.saved = result;\n\t\t\t\tthis.length--;\n\t\t\t\t//_DEBUG_PARSING_CONTEXT && console.log( \"popping context:\", result.node );\n\t\t\t\treturn result.node;\n\t\t\t},\n\t\t\tlength : 0,\n\t\t\t/*dump() {  // //_DEBUG_CONTEXT_STACK\n\t\t\t\tconsole.log( \"STACK LENGTH:\", this.length );\n\t\t\t\tlet cur= this.first;\n\t\t\t\tlet level = 0;\n\t\t\t\twhile( cur ) {\n\t\t\t\t\tconsole.log( \"Context:\", level, cur.node );\n\t\t\t\t\tlevel++;\n\t\t\t\t\tcur = cur.next;\n\t\t\t\t}\n\t\t\t}*/\n\t\t},\n\t\tclasses = [],  // class templates that have been defined.\n\t\tprotoTypes = {},\n\t\tcurrent_proto = null,  // the current class being defined or being referenced.\n\t\tcurrent_class = null,  // the current class being defined or being referenced.\n\t\tcurrent_class_field = 0,\n\t\tarrayType = -1,  // the current class being defined or being referenced.\n\t\tparse_context = CONTEXT_UNKNOWN,\n\t\tcomment = 0,\n\t\tfromHex = false,\n\t\tdecimal = false,\n\t\texponent = false,\n\t\texponent_sign = false,\n\t\texponent_digit = false,\n\t\tinQueue = {\n\t\t\tfirst : null,\n\t\t\tlast : null,\n\t\t\tsaved : null,\n\t\t\tpush(node) {\n\t\t\t\tlet recover = this.saved;\n\t\t\t\tif( recover ) { this.saved = recover.next; recover.node = node; recover.next = null; recover.prior = this.last; }\n\t\t\t\telse { recover = { node : node, next : null, prior : this.last }; }\n\t\t\t\tif( !this.last ) this.first = recover;\n\t\t\t\telse this.last.next = recover;\n\t\t\t\tthis.last = recover;\n\t\t\t},\n\t\t\tshift() {\n\t\t\t\tlet result = this.first;\n\t\t\t\tif( !result ) return null;\n\t\t\t\tif( !(this.first = result.next ) ) this.last = null;\n\t\t\t\tresult.next = this.saved; this.saved = result;\n\t\t\t\treturn result.node;\n\t\t\t},\n\t\t\tunshift(node) {\n\t\t\t\tlet recover = this.saved;\n\t\t\t\t// this is always true in this usage.\n\t\t\t\t//if( recover ) { \n\t\t\t\t\tthis.saved = recover.next; recover.node = node; recover.next = this.first; recover.prior = null; \n\t\t\t\t//}\n\t\t\t\t//else { recover = { node : node, next : this.first, prior : null }; }\n\t\t\t\tif( !this.first ) this.last = recover;\n\t\t\t\tthis.first = recover;\n\t\t\t}\n\t\t},\n\t\tgatheringStringFirstChar = null,\n\t\tgatheringString = false,\n\t\tgatheringNumber = false,\n\t\tstringEscape = false,\n\t\tcr_escaped = false,\n\t\tunicodeWide = false,\n\t\tstringUnicode = false,\n\t\tstringHex = false,\n\t\thex_char = 0,\n\t\thex_char_len = 0,\n\t\tcompleted = false,\n\t\tdate_format = false,\n\t\tisBigInt = false\n\t\t;\n\n\tfunction throwEndError( leader ) {\n\t\tthrow new Error( `${leader} at ${n} [${pos.line}:${pos.col}]`);\n\t}\n\n\treturn {\n\t\t/**\n\t\t * Define a class that can be used to deserialize objects of this type.\n\t\t * @param {string} prototypeName \n\t\t * @param {type} o \n\t\t * @param {(any)=>any} f \n\t\t */\n\t\tfromJSOX( prototypeName, o, f ) {\n\t\t\tif( localFromProtoTypes.get(prototypeName) ) throw new Error( \"Existing fromJSOX has been registered for prototype\" );\n\t\t\tfunction privateProto() { }\n\t\t\tif( !o ) o = privateProto;\n\t\t\tif( o && !(\"constructor\" in o )){\n\t\t\t\tthrow new Error( \"Please pass a prototype like thing...\");\n\t\t\t}\n\t\t\tlocalFromProtoTypes.set( prototypeName, { protoCon:o.prototype.constructor, cb:f } );\n\t\t},\n\t\tregisterFromJSOX( prototypeName, o/*, f*/ ) {\n\t\t\tthrow new Error( \"registerFromJSOX is deprecated, please update to use fromJSOX instead:\" + prototypeName + o.toString() );\n\t\t},\n\t\tfinalError() {\n\t\t\tif( comment !== 0 ) { // most of the time everything's good.\n\t\t\t\tif( comment === 1 ) throwEndError( \"Comment began at end of document\" );\n\t\t\t\tif( comment === 2 ) /*console.log( \"Warning: '//' comment without end of line ended document\" )*/;\n\t\t\t\tif( comment === 3 ) throwEndError( \"Open comment '/*' is missing close at end of document\" );\n\t\t\t\tif( comment === 4 ) throwEndError( \"Incomplete '/* *' close at end of document\" );\n\t\t\t}\n\t\t\tif( gatheringString ) throwEndError( \"Incomplete string\" );\n\t\t},\n\t\tvalue() {\n\t\t\tthis.finalError();\n\t\t\tlet r = result;\n\t\t\tresult = undefined;\n\t\t\treturn r;\n\t\t},\n\t\t/**\n\t\t * Reset the parser to a blank state.\n\t\t */\n\t\treset() {\n\t\t\tword = WORD_POS_RESET;\n\t\t\tstatus = true;\n\t\t\tif( inQueue.last ) inQueue.last.next = inQueue.save;\n\t\t\tinQueue.save = inQueue.first;\n\t\t\tinQueue.first = inQueue.last = null;\n\t\t\tif( context_stack.last ) context_stack.last.next = context_stack.save;\n\t\t\tcontext_stack.length = 0;\n\t\t\tcontext_stack.save = inQueue.first;\n\t\t\tcontext_stack.first = context_stack.last = null;//= [];\n\t\t\telements = undefined;\n\t\t\tparse_context = CONTEXT_UNKNOWN;\n\t\t\tclasses = [];\n\t\t\tprotoTypes = {};\n\t\t\tcurrent_proto = null;\n\t\t\tcurrent_class = null;\n\t\t\tcurrent_class_field = 0;\n\t\t\tval.value_type = VALUE_UNSET;\n\t\t\tval.name = null;\n\t\t\tval.string = '';\n\t\t\tval.className = null;\n\t\t\tpos.line = 1;\n\t\t\tpos.col = 1;\n\t\t\tnegative = false;\n\t\t\tcomment = 0;\n\t\t\tcompleted = false;\n\t\t\tgatheringString = false;\n\t\t\tstringEscape = false;  // string stringEscape intro\n\t\t\tcr_escaped = false;   // carraige return escaped\n\t\t\tdate_format = false;\n\t\t\t//stringUnicode = false;  // reading \\u\n\t\t\t//unicodeWide = false;  // reading \\u{} in string\n\t\t\t//stringHex = false;  // reading \\x in string\n\t\t},\n\t\tusePrototype(className,protoType ) { protoTypes[className] = protoType; },\n\t\t/**\n\t\t * Add input to the parser to get parsed.\n\t\t * @param {string} msg \n\t\t */\n\t\twrite(msg) {\n\t\t\tlet retcode;\n\t\t\tif (typeof msg !== \"string\" && typeof msg !== \"undefined\") msg = String(msg);\n\t\t\tif( !status ) throw new Error( \"Parser is still in an error state, please reset before resuming\" );\n\t\t\tfor( retcode = this._write(msg,false); retcode > 0; retcode = this._write() ) {\n\t\t\t\tif( typeof reviver === 'function' ) (function walk(holder, key) {\n\t\t\t\t\tlet k, v, value = holder[key];\n\t\t\t\t\tif (value && typeof value === 'object') {\n\t\t\t\t\t\tfor (k in value) {\n\t\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(value, k)) {\n\t\t\t\t\t\t\t\tv = walk(value, k);\n\t\t\t\t\t\t\t\tif (v !== undefined) {\n\t\t\t\t\t\t\t\t\tvalue[k] = v;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tdelete value[k];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn reviver.call(holder, key, value);\n\t\t\t\t}({'': result}, ''));\n\t\t\t\tresult = cb( result );\n\n\t\t\t\tif( retcode < 2 )\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\t/**\n\t\t * Parse a string and return the result.\n\t\t * @param {string} msg\n\t\t * @param {(key:string,value:any)=>any} [reviver]\n\t\t * @returns {any}\n\t\t */\n\t\tparse(msg,reviver) {\n\t\t\tif (typeof msg !== \"string\") msg = String(msg);\n\t\t\tthis.reset();\n\t\t\tconst writeResult = this._write( msg, true );\n\t\t\tif( writeResult > 0 ) {\n\t\t\t\tif( writeResult > 1 ){\n\t\t\t\t\t// probably a carriage return.\n\t\t\t\t\t//console.log( \"Extra data at end of message\");\n\t\t\t\t}\n\t\t\t\tlet result = this.value();\n\t\t\t\tif( ( \"undefined\" === typeof result ) && writeResult > 1 ){\n\t\t\t\t\tthrow new Error( \"Pending value could not complete\");\n\t\t\t\t}\n\t                \n\t\t\t\tresult = typeof reviver === 'function' ? (function walk(holder, key) {\n\t\t\t\t\tlet k, v, value = holder[key];\n\t\t\t\t\tif (value && typeof value === 'object') {\n\t\t\t\t\t\tfor (k in value) {\n\t\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(value, k)) {\n\t\t\t\t\t\t\t\tv = walk(value, k);\n\t\t\t\t\t\t\t\tif (v !== undefined) {\n\t\t\t\t\t\t\t\t\tvalue[k] = v;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tdelete value[k];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn reviver.call(holder, key, value);\n\t\t\t\t}({'': result}, '')) : result;\n\t\t\t\treturn result;\n\t\t\t}\n\t\t\tthis.finalError();\n\t\t\treturn undefined;\n\n\t\t\t\n\t\t\treturn this.write(msg );\n\t\t},\n\t\t_write(msg,complete_at_end) {\n\t\t\tlet cInt;\n\t\t\tlet input;\n\t\t\tlet buf;\n\t\t\tlet retval = 0;\n\t\t\tfunction throwError( leader, c ) {\n\t\t\t\tthrow new Error( `${leader} '${String.fromCodePoint( c )}' unexpected at ${n} (near '${buf.substr(n>4?(n-4):0,n>4?3:(n-1))}[${String.fromCodePoint( c )}]${buf.substr(n, 10)}') [${pos.line}:${pos.col}]`);\n\t\t\t}\n\n\t\t\tfunction RESET_VAL()  {\n\t\t\t\tval.value_type = VALUE_UNSET;\n\t\t\t\tval.string = '';\n\t\t\t\tval.contains = null;\n\t\t\t\t//val.className = null;\n\t\t\t}\n\n\t\t\tfunction convertValue() {\n\t\t\t\tlet fp = null;\n\t\t\t\t//_DEBUG_PARSING && console.log( \"CONVERT VAL:\", val );\n\t\t\t\tswitch( val.value_type ){\n\t\t\t\tcase VALUE_NUMBER:\n\t\t\t\t\t//1502678337047\n\t\t\t\t\tif( ( ( val.string.length > 13 ) || ( val.string.length == 13 && val[0]>'2' ) )\n\t\t\t\t\t    && !date_format && !exponent_digit && !exponent_sign && !decimal ) {\n\t\t\t\t\t\tisBigInt = true;\n\t\t\t\t\t}\n\t\t\t\t\tif( isBigInt ) { if( hasBigInt ) return BigInt(val.string); else throw new Error( \"no builtin BigInt()\", 0 ) }\n\t\t\t\t\tif( date_format ) { \n\t\t\t\t\t\tconst r = val.string.match(/\\.(\\d\\d\\d\\d*)/ );\n\t\t\t\t\t\tconst frac = ( r )?( r )[1]:null;\n\t\t\t\t\t\tif( !frac || (frac.length < 4) ) {\n\t\t\t\t\t\t\tconst r = new Date( val.string ); \n\t\t\t\t\t\t\tif(isNaN(r.getTime())) throwError( \"Bad Date format\", cInt ); return r;  \n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlet ns = frac.substr( 3 );\n\t\t\t\t\t\t\twhile( ns.length < 6 ) ns = ns+'0';\n\t\t\t\t\t\t\tconst r = new DateNS( val.string, Number(ns ) ); \n\t\t\t\t\t\t\tif(isNaN(r.getTime())) throwError( \"Bad DateNS format\" + r+r.getTime(), cInt ); return r;  \n\t\t\t\t\t\t}\n\t\t\t\t\t\t//const r = new Date( val.string ); if(isNaN(r.getTime())) throwError( \"Bad number format\", cInt ); return r;  \n\t\t\t\t\t}\n\t\t\t\t\treturn  (negative?-1:1) * Number( val.string );\n\t\t\t\tcase VALUE_STRING:\n\t\t\t\t\tif( val.className ) {\n\t\t\t\t\t\tfp = localFromProtoTypes.get( val.className );\n\t\t\t\t\t\tif( !fp )\n\t\t\t\t\t\t\tfp = fromProtoTypes.get( val.className );\n\t\t\t\t\t\tif( fp && fp.cb ) {\n\t\t\t\t\t\t\tval.className = null;\n\t\t\t\t\t\t\treturn fp.cb.call( val.string );\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// '[object Object]' throws this error.\n\t\t\t\t\t\t\tthrow new Error( \"Double string error, no constructor for: new \" + val.className + \"(\"+val.string+\")\" )\n\t\t\t\t\t\t}\t\n\t\t\t\t\t}\n\t\t\t\t\treturn val.string;\n\t\t\t\tcase VALUE_TRUE:\n\t\t\t\t\treturn true;\n\t\t\t\tcase VALUE_FALSE:\n\t\t\t\t\treturn false;\n\t\t\t\tcase VALUE_NEG_NAN:\n\t\t\t\t\treturn -NaN;\n\t\t\t\tcase VALUE_NAN:\n\t\t\t\t\treturn NaN;\n\t\t\t\tcase VALUE_NEG_INFINITY:\n\t\t\t\t\treturn -Infinity;\n\t\t\t\tcase VALUE_INFINITY:\n\t\t\t\t\treturn Infinity;\n\t\t\t\tcase VALUE_NULL:\n\t\t\t\t\treturn null;\n\t\t\t\tcase VALUE_UNDEFINED:\n\t\t\t\t\treturn undefined;\n\t\t\t\tcase VALUE_EMPTY:\n\t\t\t\t\treturn undefined;\n\t\t\t\tcase VALUE_OBJECT:\n\t\t\t\t\tif( val.className ) { \n\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"class reviver\" );\n\t\t\t\t\t\tfp = localFromProtoTypes.get( val.className );\n\t\t\t\t\t\tif( !fp )\n\t\t\t\t\t\t\tfp = fromProtoTypes.get( val.className );\n\t\t\t\t\t\tval.className = null;\n\t\t\t\t\t\tif( fp && fp.cb ) return val.contains = fp.cb.call( val.contains ); \n\t\t\t\t\t}\n\t\t\t\t\treturn val.contains;\n\t\t\t\tcase VALUE_ARRAY:\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"Array conversion:\", arrayType, val.contains );\n\t\t\t\t\tif( arrayType >= 0 ) {\n\t\t\t\t\t\tlet ab;\n\t\t\t\t\t\tif( val.contains.length )\n\t\t\t\t\t\t\tab = DecodeBase64( val.contains[0] )\n\t\t\t\t\t\telse ab = DecodeBase64( val.string );\n\t\t\t\t\t\tif( arrayType === 0 ) {\n\t\t\t\t\t\t\tarrayType = -1;\n\t\t\t\t\t\t\treturn ab;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst newab = new knownArrayTypes[arrayType]( ab );\n\t\t\t\t\t\t\tarrayType = -1;\n\t\t\t\t\t\t\treturn newab;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if( arrayType === -2 ) {\n\t\t\t\t\t\tlet obj = rootObject;\n\t\t\t\t\t\t//let ctx = context_stack.first;\n\t\t\t\t\t\tlet lvl;\n\t\t\t\t\t\t//console.log( \"Resolving Reference...\", context_stack.length );\n\t\t\t\t\t\t//console.log( \"--elements and array\", elements );\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst pathlen = val.contains.length;\n\t\t\t\t\t\tfor( lvl = 0; lvl < pathlen; lvl++ ) {\n\t\t\t\t\t\t\tconst idx = val.contains[lvl];\n\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Looking up idx:\", idx, \"of\", val.contains, \"in\", obj );\n\t\t\t\t\t\t\tlet nextObj = obj[idx];\n\n\t\t\t\t\t\t\t//_DEBUG_REFERENCES  && console.log( \"Resolve path:\", lvl, idx,\"in\", obj, context_stack.length, val.contains.toString() );\n\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"NEXT OBJECT:\", nextObj );\n\t\t\t\t\t\t\tif( !nextObj ) {\n\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\tlet ctx = context_stack.first;\n\t\t\t\t\t\t\t\t\tlet p = 0;\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING_CONTEXT && context_stack.dump();\n\t\t\t\t\t\t\t\t\twhile( ctx && p < pathlen && p < context_stack.length ) {\n\t\t\t\t\t\t\t\t\t\tconst thisKey = val.contains[p];\n\t\t\t\t\t\t\t\t\t\tif( !ctx.next || thisKey !== ctx.next.node.name ) {\n\t\t\t\t\t\t\t\t\t\t\tbreak;  // can't follow context stack any further.... \n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Checking context:\", obj, \"p=\",p, \"key=\",thisKey, \"ctx(and .next)=\",util.inspect(ctx));\n\t\t\t\t\t\t\t\t\t\t//console.dir(ctx, { depth: null })\n\t\t\t\t\t\t\t\t\t\tif( ctx.next ) {\n\t\t\t\t\t\t\t\t\t\t\tif( \"number\" === typeof thisKey ) {\n\t\t\t\t\t\t\t\t\t\t\t\tconst actualObject = ctx.next.node.elements;\n\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Number in index... tracing stack...\", obj, actualObject, ctx && ctx.next && ctx.next.next && ctx.next.next.node );\n\n\t\t\t\t\t\t\t\t\t\t\t\tif( actualObject && thisKey >= actualObject.length ) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"AT \", p, actualObject.length, val.contains.length );\n\t\t\t\t\t\t\t\t\t\t\t\t\tif( p === (context_stack.length-1) ) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.log( \"This is actually at the current object so use that\", p, val.contains, elements );\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = elements;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tp++;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tctx = ctx.next;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"is next... \", thisKey, actualObject.length )\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif( ctx.next.next && thisKey === actualObject.length ) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"is next... \")\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = ctx.next.next.node.elements;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tctx = ctx.next;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tp++;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tobj = nextObj;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"FAILING HERE\", ctx.next, ctx.next.next, elements, obj );\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Nothing after, so this is just THIS?\" );\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = elements;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tp++; // make sure to exit.\n\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//obj = next\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"field AT index\", p,\"of\", val.contains.length );\n\t\t\t\t\t\t\t\t\t\t\t\tif( thisKey !== ctx.next.node.name ){\n\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Expect:\", thisKey, ctx.next.node.name, ctx.next.node.elements );\n\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = ( ctx.next.node.elements[thisKey] );\n\t\t\t\t\t\t\t\t\t\t\t\t\t//throw new Error( \"Unexpected path-context relationship\" );\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\tlvl = p;\n\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Updating next object(NEW) to\", ctx.next.node, elements, thisKey)\n\t\t\t\t\t\t\t\t\t\t\t\t\tif( ctx.next.next )\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = ctx.next.next.node.elements;\n\t\t\t\t\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Nothing after, so this is just THIS?\" );\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnextObj = elements;\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"using named element from\", ctx.next.node.elements, \"=\", nextObj )\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t//if( //_DEBUG_REFERENCES )  {\n\t\t\t\t\t\t\t\t\t\t\t//\tconst a = ctx.next.node.elements;\n\t\t\t\t\t\t\t\t\t\t\t//\tconsole.log( \"Stack Dump:\"\n\t\t\t\t\t\t\t\t\t\t\t//\t\t, a?a.length:a\n\t\t\t\t\t\t\t\t\t\t\t//\t\t, ctx.next.node.name\n\t\t\t\t\t\t\t\t\t\t\t//\t\t, thisKey\n\t\t\t\t\t\t\t\t\t\t\t//\t\t);\n\t\t\t\t\t\t\t\t\t\t\t//}\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\tnextObj = nextObj[thisKey];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Doing next context??\", p, context_stack.length, val.contains.length );\n\t\t\t\t\t\t\t\t\t\tctx = ctx.next;\n\t\t\t\t\t\t\t\t\t\tp++;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"Done with context stack...level\", lvl, \"p\", p );\n\t\t\t\t\t\t\t\t\tif( p < pathlen )\n\t\t\t\t\t\t\t\t\t\tlvl = p-1;\n\t\t\t\t\t\t\t\t\telse lvl = p;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t//_DEBUG_REFERENCES && console.log( \"End of processing level:\", lvl );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif( (\"object\" === typeof nextObj ) && !nextObj ) {\n\t\t\t\t\t\t\t\tthrow new Error( \"Path did not resolve properly:\" +  val.contains + \" at \" + idx + '(' + lvl + ')' );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tobj = nextObj;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"Resulting resolved object:\", obj );\n\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"SETTING MODE TO -3 (resolved -2)\" );\n\t\t\t\t\t\tarrayType = -3;\n\t\t\t\t\t\treturn obj;\n\t\t\t\t\t}\n\t\t\t\t\tif( val.className ) { \n\t\t\t\t\t\tfp = localFromProtoTypes.get( val.className );\n\t\t\t\t\t\tif( !fp )\n\t\t\t\t\t\t\tfp = fromProtoTypes.get( val.className );\n\t\t\t\t\t\tval.className = null; \n\t\t\t\t\t\tif( fp && fp.cb ) return fp.cb.call( val.contains ); \n\t\t\t\t\t}\n\t\t\t\t\treturn val.contains;\n\t\t\t\tdefault:\n\t\t\t\t\tconsole.log( \"Unhandled value conversion.\", val );\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction arrayPush() {\n\t\t\t\t//_DEBUG_PARSING && console.log( \"PUSH TO ARRAY:\", val );\n\t\t\t\tif( arrayType == -3 )  {\n\t\t\t\t\t//_DEBUG_PARSING && console.log(\" Array type -3?\", val.value_type, elements );\n\t\t\t\t\tif( val.value_type === VALUE_OBJECT ) {\n\t\t\t\t\t\telements.push( val.contains );\n\t\t\t\t\t}\n\t\t\t\t\tarrayType = -1; // next one should be allowed?\n\t\t\t\t\treturn;\n\t\t\t\t} //else\n\t\t\t\t//\tconsole.log( \"Finally a push that's not already pushed!\", );\n\t\t\t\tswitch( val.value_type ){\n\t\t\t\tcase VALUE_EMPTY:\n\t\t\t\t\telements.push( undefined );\n\t\t\t\t\tdelete elements[elements.length-1];\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\telements.push( convertValue() );\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tRESET_VAL();\n\t\t\t}\n\n\t\t\tfunction objectPush() {\n\t\t\t\tif( arrayType === -3 && val.value_type === VALUE_ARRAY ) {\n\t\t\t\t\t//console.log( \"Array has already been set in object.\" );\n\t\t\t\t\t//elements[val.name] = val.contains;\n\t\t\t\t\tRESET_VAL();\n\t\t\t\t\tarrayType = -1;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif( val.value_type === VALUE_EMPTY ) return;\n\t\t\t\tif( !val.name && current_class ) {\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"A Stepping current class field:\", current_class_field, val.name );\n\t\t\t\t\tval.name = current_class.fields[current_class_field++];\n\t\t\t\t}\n\t\t\t\tlet value = convertValue();\n\n\t\t\t\tif( current_proto && current_proto.protoDef && current_proto.protoDef.cb ) {\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"SOMETHING SHOULD AHVE BEEN REPLACED HERE??\", current_proto );\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"(need to do fromprototoypes here) object:\", val, value );\n\t\t\t\t\tvalue = current_proto.protoDef.cb.call( elements, val.name, value );\n\t\t\t\t\tif( value ) elements[val.name] = value;\n\t\t\t\t\t//elements = new current_proto.protoCon( elements );\n\t\t\t\t}else {\n\t\t\t\t        //_DEBUG_PARSING_DETAILS && console.log( \"Default no special class reviver\", val.name, value );\n\t\t\t\t\telements[val.name] = value;\n\t\t\t\t}\n\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"Updated value:\", current_class_field, val.name, elements[val.name] );\n\t\t\t\n\t\t\t\t//_DEBUG_PARSING && console.log( \"+++ Added object field:\", val.name, elements, elements[val.name], rootObject );\n\t\t\t\tRESET_VAL();\n\t\t\t}\n\n\t\t\tfunction recoverIdent(cInt) {\n\t\t\t\t//_DEBUG_PARSING&&console.log( \"Recover Ident char:\", cInt, val, String.fromCodePoint(cInt), \"word:\", word );\n\t\t\t\tif( word !== WORD_POS_RESET ) {\n\t\t\t\t\tif( negative ) { \n\t\t\t\t\t\t//val.string += \"-\"; negative = false; \n\t\t\t\t\t\tthrowError( \"Negative outside of quotes, being converted to a string (would lose count of leading '-' characters)\", cInt );\n\t\t\t\t\t}\n\t\t\t\t\tswitch( word ) {\n\t\t\t\t\tcase WORD_POS_END:\n\t\t\t\t\t\tswitch( val.value_type ) {\n\t\t\t\t\t\tcase VALUE_TRUE:  val.string += \"true\"; break\n\t\t\t\t\t\tcase VALUE_FALSE:  val.string += \"false\"; break\n\t\t\t\t\t\tcase VALUE_NULL:  val.string += \"null\"; break\n\t\t\t\t\t\tcase VALUE_INFINITY:  val.string += \"Infinity\"; break\n\t\t\t\t\t\tcase VALUE_NEG_INFINITY:  val.string += \"-Infinity\"; throwError( \"Negative outside of quotes, being converted to a string\", cInt ); break\n\t\t\t\t\t\tcase VALUE_NAN:  val.string += \"NaN\"; break\n\t\t\t\t\t\tcase VALUE_NEG_NAN:  val.string += \"-NaN\"; throwError( \"Negative outside of quotes, being converted to a string\", cInt ); break\n\t\t\t\t\t\tcase VALUE_UNDEFINED:  val.string += \"undefined\"; break\n\t\t\t\t\t\tcase VALUE_STRING: break;\n\t\t\t\t\t\tcase VALUE_UNSET: break;\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tconsole.log( \"Value of type \" + val.value_type + \" is not restored...\" );\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase WORD_POS_TRUE_1 :  val.string += \"t\"; break;\n\t\t\t\t\tcase WORD_POS_TRUE_2 :  val.string += \"tr\"; break;\n\t\t\t\t\tcase WORD_POS_TRUE_3 : val.string += \"tru\"; break;\n\t\t\t\t\tcase WORD_POS_FALSE_1 : val.string += \"f\"; break;\n\t\t\t\t\tcase WORD_POS_FALSE_2 : val.string += \"fa\"; break;\n\t\t\t\t\tcase WORD_POS_FALSE_3 : val.string += \"fal\"; break;\n\t\t\t\t\tcase WORD_POS_FALSE_4 : val.string += \"fals\"; break;\n\t\t\t\t\tcase WORD_POS_NULL_1 : val.string += \"n\"; break;\n\t\t\t\t\tcase WORD_POS_NULL_2 : val.string += \"nu\"; break;\n\t\t\t\t\tcase WORD_POS_NULL_3 : val.string += \"nul\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_1 : val.string += \"u\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_2 : val.string += \"un\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_3 : val.string += \"und\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_4 : val.string += \"unde\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_5 : val.string += \"undef\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_6 : val.string += \"undefi\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_7 : val.string += \"undefin\"; break;\n\t\t\t\t\tcase WORD_POS_UNDEFINED_8 : val.string += \"undefine\"; break;\n\t\t\t\t\tcase WORD_POS_NAN_1 : val.string += \"N\"; break;\n\t\t\t\t\tcase WORD_POS_NAN_2 : val.string += \"Na\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_1 : val.string += \"I\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_2 : val.string += \"In\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_3 : val.string += \"Inf\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_4 : val.string += \"Infi\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_5 : val.string += \"Infin\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_6 : val.string += \"Infini\"; break;\n\t\t\t\t\tcase WORD_POS_INFINITY_7 : val.string += \"Infinit\"; break;\n\t\t\t\t\tcase WORD_POS_RESET : break;\n\t\t\t\t\tcase WORD_POS_FIELD : break;\n\t\t\t\t\tcase WORD_POS_AFTER_FIELD:\n\t\t\t\t\t    //throwError( \"String-keyword recovery fail (after whitespace)\", cInt);\n\t\t\t\t\t    break;\n\t\t\t\t\tcase WORD_POS_AFTER_FIELD_VALUE:\n\t\t\t\t\t    throwError( \"String-keyword recovery fail (after whitespace)\", cInt );\n\t\t\t\t\t    break;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\t//console.log( \"Word context: \" + word + \" unhandled\" );\n\t\t\t\t\t}\n\t\t\t\t\tval.value_type = VALUE_STRING;\t\t\t\t\t\t\t\t\t\n\t\t\t\t\tif( word < WORD_POS_FIELD)\n\t\t\t\t\t    word = WORD_POS_END;\n\t\t\t\t} else {\n\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t//if( val.value_type === VALUE_UNSET && val.string.length )\n\t\t\t\t\t\tval.value_type = VALUE_STRING\n\t\t\t\t}\n\t\t\t\tif( cInt == 123/*'{'*/ )\n\t\t\t\t\topenObject();\n\t\t\t\telse if( cInt == 91/*'['*/ )\n\t\t\t\t\topenArray();\n\t\t\t\telse if( cInt == 44/*','*/ ) {\n\t\t\t\t\t// comma separates the string, it gets consumed.\n\t\t\t\t} else {\n\t\t\t\t\t// ignore white space.\n\t\t\t\t\tif( cInt == 32/*' '*/ || cInt == 13 || cInt == 10 || cInt == 9 || cInt == 0xFEFF || cInt == 0x2028 || cInt == 0x2029 ) {\n\t\t\t\t\t\t//_DEBUG_WHITESPACE && console.log( \"IGNORE WHITESPACE\" );\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif( cInt == 44/*','*/ || cInt == 125/*'}'*/ || cInt == 93/*']'*/ || cInt == 58/*':'*/ )\n\t\t\t\t\t\tthrowError( \"Invalid character near identifier\", cInt );\n\t\t\t\t\telse //if( typeof cInt === \"number\")\n\t\t\t\t\t\tval.string += str;\n\t\t\t\t}\n\t\t\t\t//console.log( \"VAL STRING IS:\", val.string, str );\n\t\t\t}\n\n\t\t\t// gather a string from an input stream; start_c is the opening quote to find a related close quote.\n\t\t\tfunction gatherString( start_c ) {\n\t\t\t\tlet retval = 0;\n\t\t\t\twhile( retval == 0 && ( n < buf.length ) ) {\n\t\t\t\t\tstr = buf.charAt(n);\n\t\t\t\t\tlet cInt = buf.codePointAt(n++);\n\t\t\t\t\tif( cInt >= 0x10000 ) { str += buf.charAt(n); n++; }\n\t\t\t\t\t//console.log( \"gathering....\", stringEscape, str, cInt, unicodeWide, stringHex, stringUnicode, hex_char_len );\n\t\t\t\t\tpos.col++;\n\t\t\t\t\tif( cInt == start_c ) { //( cInt == 34/*'\"'*/ ) || ( cInt == 39/*'\\''*/ ) || ( cInt == 96/*'`'*/ ) )\n\t\t\t\t\t\tif( stringEscape ) { \n\t\t\t\t\t\t\tif( stringHex )\n\t\t\t\t\t\t\t\tthrowError( \"Incomplete hexidecimal sequence\", cInt );\n\t\t\t\t\t\t\telse if( stringUnicode )\n\t\t\t\t\t\t\t\tthrowError( \"Incomplete long unicode sequence\", cInt );\n\t\t\t\t\t\t\telse if( unicodeWide )\n\t\t\t\t\t\t\t\tthrowError( \"Incomplete unicode sequence\", cInt );\n\t\t\t\t\t\t\tif( cr_escaped ) {\n\t\t\t\t\t\t\t\tcr_escaped = false;\n\t\t\t\t\t\t\t\tretval = 1; // complete string, escaped \\r\n\t\t\t\t\t\t\t} else val.string += str;\n\t\t\t\t\t\t\tstringEscape = false; }\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t// quote matches, and is not processing an escape sequence.\n\t\t\t\t\t\t\tretval = 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\telse if( stringEscape ) {\n\t\t\t\t\t\tif( unicodeWide ) {\n\t\t\t\t\t\t\tif( cInt == 125/*'}'*/ ) {\n\t\t\t\t\t\t\t\tval.string += String.fromCodePoint( hex_char );\n\t\t\t\t\t\t\t\tunicodeWide = false;\n\t\t\t\t\t\t\t\tstringUnicode = false;\n\t\t\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\thex_char *= 16;\n\t\t\t\t\t\t\tif( cInt >= 48/*'0'*/ && cInt <= 57/*'9'*/ )      hex_char += cInt - 0x30;\n\t\t\t\t\t\t\telse if( cInt >= 65/*'A'*/ && cInt <= 70/*'F'*/ ) hex_char += ( cInt - 65 ) + 10;\n\t\t\t\t\t\t\telse if( cInt >= 97/*'a'*/ && cInt <= 102/*'f'*/ ) hex_char += ( cInt - 97 ) + 10;\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tthrowError( \"(escaped character, parsing hex of \\\\u)\", cInt );\n\t\t\t\t\t\t\t\tretval = -1;\n\t\t\t\t\t\t\t\tunicodeWide = false;\n\t\t\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if( stringHex || stringUnicode ) {\n\t\t\t\t\t\t\tif( hex_char_len === 0 && cInt === 123/*'{'*/ ) {\n\t\t\t\t\t\t\t\tunicodeWide = true;\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif( hex_char_len < 2 || ( stringUnicode && hex_char_len < 4 ) ) {\n\t\t\t\t\t\t\t\thex_char *= 16;\n\t\t\t\t\t\t\t\tif( cInt >= 48/*'0'*/ && cInt <= 57/*'9'*/ )      hex_char += cInt - 0x30;\n\t\t\t\t\t\t\t\telse if( cInt >= 65/*'A'*/ && cInt <= 70/*'F'*/ ) hex_char += ( cInt - 65 ) + 10;\n\t\t\t\t\t\t\t\telse if( cInt >= 97/*'a'*/ && cInt <= 102/*'f'*/ ) hex_char += ( cInt - 97 ) + 10;\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tthrowError( stringUnicode?\"(escaped character, parsing hex of \\\\u)\":\"(escaped character, parsing hex of \\\\x)\", cInt );\n\t\t\t\t\t\t\t\t\tretval = -1;\n\t\t\t\t\t\t\t\t\tstringHex = false;\n\t\t\t\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\thex_char_len++;\n\t\t\t\t\t\t\t\tif( stringUnicode ) {\n\t\t\t\t\t\t\t\t\tif( hex_char_len == 4 ) {\n\t\t\t\t\t\t\t\t\t\tval.string += String.fromCodePoint( hex_char );\n\t\t\t\t\t\t\t\t\t\tstringUnicode = false;\n\t\t\t\t\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse if( hex_char_len == 2 ) {\n\t\t\t\t\t\t\t\t\tval.string += String.fromCodePoint( hex_char );\n\t\t\t\t\t\t\t\t\tstringHex = false;\n\t\t\t\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tswitch( cInt ) {\n\t\t\t\t\t\tcase 13/*'\\r'*/:\n\t\t\t\t\t\t\tcr_escaped = true;\n\t\t\t\t\t\t\tpos.col = 1;\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\tcase 0x2028: // LS (Line separator)\n\t\t\t\t\t\tcase 0x2029: // PS (paragraph separate)\n\t\t\t\t\t\t\tpos.col = 1;\n\t\t\t\t\t\t\t// falls through\n\t\t\t\t\t\tcase 10/*'\\n'*/:\n\t\t\t\t\t\t\tif( !cr_escaped ) { // \\\\ \\n\n\t\t\t\t\t\t\t\tpos.col = 1;\n\t\t\t\t\t\t\t} else { // \\\\ \\r \\n\n\t\t\t\t\t\t\t\tcr_escaped = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tpos.line++;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 116/*'t'*/:\n\t\t\t\t\t\t\tval.string += '\\t';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 98/*'b'*/:\n\t\t\t\t\t\t\tval.string += '\\b';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 110/*'n'*/:\n\t\t\t\t\t\t\tval.string += '\\n';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 114/*'r'*/:\n\t\t\t\t\t\t\tval.string += '\\r';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 102/*'f'*/:\n\t\t\t\t\t\t\tval.string += '\\f';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 118/*'v'*/:\n\t\t\t\t\t\t\tval.string += '\\v';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 48/*'0'*/: \n\t\t\t\t\t\t\tval.string += '\\0';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 120/*'x'*/:\n\t\t\t\t\t\t\tstringHex = true;\n\t\t\t\t\t\t\thex_char_len = 0;\n\t\t\t\t\t\t\thex_char = 0;\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\tcase 117/*'u'*/:\n\t\t\t\t\t\t\tstringUnicode = true;\n\t\t\t\t\t\t\thex_char_len = 0;\n\t\t\t\t\t\t\thex_char = 0;\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t//case 47/*'/'*/:\n\t\t\t\t\t\t//case 92/*'\\\\'*/:\n\t\t\t\t\t\t//case 34/*'\"'*/:\n\t\t\t\t\t\t//case 39/*\"'\"*/:\n\t\t\t\t\t\t//case 96/*'`'*/:\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t//console.log( \"other...\" );\n\t\t\t\t\t\tstringEscape = false;\n\t\t\t\t\t}\n\t\t\t\t\telse if( cInt === 92/*'\\\\'*/ ) {\n\t\t\t\t\t\tif( stringEscape ) {\n\t\t\t\t\t\t\tval.string += '\\\\';\n\t\t\t\t\t\t\tstringEscape = false\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tstringEscape = true;\n\t\t\t\t\t\t\thex_char = 0;\n\t\t\t\t\t\t\thex_char_len = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse { /* any other character */\n\t\t\t\t\t\tif( cr_escaped ) {\n\t\t\t\t\t\t\t// \\\\ \\r <any char>\n\t\t\t\t\t\t\tcr_escaped = false;\n\t\t\t\t\t\t\tpos.line++;\n\t\t\t\t\t\t\tpos.col = 2; // this character is pos 1; and increment to be after it.\n\t\t\t\t\t\t}\n\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn retval;\n\t\t\t}\n\n\t\t\t// gather a number from the input stream.\n\t\t\tfunction collectNumber() {\n\t\t\t\tlet _n;\n\t\t\t\twhile( (_n = n) < buf.length ) {\n\t\t\t\t\tstr = buf.charAt(_n);\n\t\t\t\t\tlet cInt = buf.codePointAt(n++);\n\t\t\t\t\tif( cInt >= 256 ) { \n\t\t\t\t\t\t\tpos.col -= n - _n;\n\t\t\t\t\t\t\tn = _n; // put character back in queue to process.\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t//_DEBUG_PARSING_NUMBERS  && console.log( \"in getting number:\", n, cInt, String.fromCodePoint(cInt) );\n\t\t\t\t\t\tif( cInt == 95 /*_*/ )\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\tpos.col++;\n\t\t\t\t\t\t// leading zeros should be forbidden.\n\t\t\t\t\t\tif( cInt >= 48/*'0'*/ && cInt <= 57/*'9'*/ ) {\n\t\t\t\t\t\t\tif( exponent ) {\n\t\t\t\t\t\t\t\texponent_digit = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t} else if( cInt == 45/*'-'*/ || cInt == 43/*'+'*/ ) {\n\t\t\t\t\t\t\tif( val.string.length == 0 || ( exponent && !exponent_sign && !exponent_digit ) ) {\n\t\t\t\t\t\t\t\tif( cInt == 45/*'-'*/ && !exponent ) negative = !negative;\n\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\texponent_sign = true;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tif( negative ) { val.string = '-' + val.string; negative = false; }\n\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\tdate_format = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( cInt == 78/*'N'*/ ) {\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) {\n\t\t\t\t\t\t\t\tgatheringNumber = false;\n\t\t\t\t\t\t\t\tword = WORD_POS_NAN_1;\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t} else if( cInt == 73/*'I'*/ ) {\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) {\n\t\t\t\t\t\t\t\tgatheringNumber = false;\n\t\t\t\t\t\t\t\tword = WORD_POS_INFINITY_1;\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t} else if( cInt == 58/*':'*/ && date_format ) {\n\t\t\t\t\t\t\tif( negative ) { val.string = '-' + val.string; negative = false; }\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\tdate_format = true;\n\t\t\t\t\t\t} else if( cInt == 84/*'T'*/ && date_format ) {\n\t\t\t\t\t\t\tif( negative ) { val.string = '-' + val.string; negative = false; }\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\tdate_format = true;\n\t\t\t\t\t\t} else if( cInt == 90/*'Z'*/ && date_format ) {\n\t\t\t\t\t\t\tif( negative ) { val.string = '-' + val.string; negative = false; }\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\tdate_format = true;\n\t\t\t\t\t\t} else if( cInt == 46/*'.'*/ ) {\n\t\t\t\t\t\t\tif( !decimal && !fromHex && !exponent ) {\n\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\tdecimal = true;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( cInt == 110/*'n'*/ ) {\n\t\t\t\t\t\t\tisBigInt = true;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t} else if( fromHex && ( ( ( cInt >= 95/*'a'*/ ) && ( cInt <= 102/*'f'*/ ) ) ||\n\t\t\t\t\t\t           ( ( cInt >= 65/*'A'*/ ) && ( cInt <= 70/*'F'*/ ) ) ) ) {\n\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t} else if( cInt == 120/*'x'*/ || cInt == 98/*'b'*/ || cInt == 111/*'o'*/\n\t\t\t\t\t\t\t\t|| cInt == 88/*'X'*/ || cInt == 66/*'B'*/ || cInt == 79/*'O'*/ ) {\n\t\t\t\t\t\t\t// hex conversion.\n\t\t\t\t\t\t\tif( !fromHex && val.string == '0' ) {\n\t\t\t\t\t\t\t\tfromHex = true;\n\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( ( cInt == 101/*'e'*/ ) || ( cInt == 69/*'E'*/ ) ) {\n\t\t\t\t\t\t\tif( !exponent ) {\n\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\texponent = true;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif( cInt == 32/*' '*/ || cInt == 13 || cInt == 10 || cInt == 9 || cInt == 47/*'/'*/ || cInt ==  35/*'#'*/\n\t\t\t\t\t\t\t || cInt == 44/*','*/ || cInt == 125/*'}'*/ || cInt == 93/*']'*/\n\t\t\t\t\t\t\t || cInt == 123/*'{'*/ || cInt == 91/*'['*/ || cInt == 34/*'\"'*/ || cInt == 39/*'''*/ || cInt == 96/*'`'*/\n\t\t\t\t\t\t\t || cInt == 58/*':'*/ ) {\n\t\t\t\t\t\t\t\tpos.col -= n - _n;\n\t\t\t\t\t\t\t\tn = _n; // put character back in queue to process.\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tif( complete_at_end ) {\n\t\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\t\tthrowError( \"fault while parsing number;\", cInt );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif( (!complete_at_end) && n == buf.length ) {\n\t\t\t\t\tgatheringNumber = true;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tgatheringNumber = false;\n\t\t\t\t\tval.value_type = VALUE_NUMBER;\n\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// begin parsing an object type\n\t\t\tfunction openObject() {\n\t\t\t\tlet nextMode = CONTEXT_OBJECT_FIELD;\n\t\t\t\tlet cls = null;\n\t\t\t\tlet tmpobj = {};\n\t\t\t\t//_DEBUG_PARSING && console.log( \"opening object:\", val.string, val.value_type, word, parse_context );\n\t\t\t\tif( word > WORD_POS_RESET && word < WORD_POS_FIELD )\n\t\t\t\t\trecoverIdent( 123 /* '{' */ );\n\t\t\t\tlet protoDef;\n\t\t\t\tprotoDef = getProto(); // lookup classname using val.string and get protodef(if any)\n\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\tif( word == WORD_POS_FIELD /*|| word == WORD_POS_AFTER_FIELD*/ \n\t\t\t\t\t   || word == WORD_POS_END\n\t\t\t\t\t     && ( protoDef || val.string.length ) ) {\n\t\t\t\t\t\t\tif( protoDef && protoDef.protoDef && protoDef.protoDef.protoCon ) {\n\t\t\t\t\t\t\t\ttmpobj = new protoDef.protoDef.protoCon();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\tif( !protoDef || !protoDef.protoDef && val.string ) // class creation is redundant...\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tcls = classes.find( cls=>cls.name===val.string );\n\t\t\t\t\t\t\t//console.log( \"Probably creating the Macro-Tag here?\", cls )\n\t\t\t\t\t\t\tif( !cls ) {\n\t\t\t\t\t\t\t\t/* eslint-disable no-inner-declarations */\n\t\t\t\t\t\t\t\tfunction privateProto() {} \n\t\t\t\t\t\t\t\t// this just uses the tmpobj {} container to store the values collected for this class...\n\t\t\t\t\t\t\t\t// this does not generate the instance of the class.\n\t\t\t\t\t\t\t\t// if this tag type is also a prototype, use that prototype, else create a unique proto\n\t\t\t\t\t\t\t\t// for this tagged class type.\n\t\t\t\t\t\t\t\tclasses.push( cls = { name : val.string\n\t\t\t\t\t\t\t\t, protoCon: (protoDef && protoDef.protoDef && protoDef.protoDef.protoCon) || privateProto.constructor\n\t\t\t\t\t\t\t\t , fields : [] } );\n\t\t\t\t\t\t\t\t nextMode = CONTEXT_CLASS_FIELD;\n\t\t\t\t\t\t\t} else if( redefineClass ) {\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"redefine class...\" );\n\t\t\t\t\t\t\t\t// redefine this class\n\t\t\t\t\t\t\t\tcls.fields.length = 0;\n\t\t\t\t\t\t\t\tnextMode = CONTEXT_CLASS_FIELD;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"found existing class, using it....\");\n\t\t\t\t\t\t\t\ttmpobj = new cls.protoCon();\n\t\t\t\t\t\t\t\t//tmpobj = Object.assign( tmpobj, cls.protoObject );\n\t\t\t\t\t\t\t\t//Object.setPrototypeOf( tmpobj, Object.getPrototypeOf( cls.protoObject ) );\n\t\t\t\t\t\t\t\tnextMode = CONTEXT_CLASS_VALUE;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tredefineClass = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcurrent_class = cls\n\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tword = WORD_POS_FIELD;\n\t\t\t\t\t}\n\t\t\t\t} else if( word == WORD_POS_FIELD /*|| word == WORD_POS_AFTER_FIELD*/ \n\t\t\t\t\t\t|| parse_context === CONTEXT_IN_ARRAY \n\t\t\t\t\t\t|| parse_context === CONTEXT_OBJECT_FIELD_VALUE \n\t\t\t\t\t\t|| parse_context == CONTEXT_CLASS_VALUE ) {\n\t\t\t\t\tif( word != WORD_POS_RESET || val.value_type == VALUE_STRING ) {\n\t\t\t\t\t\tif( protoDef && protoDef.protoDef ) {\n\t\t\t\t\t\t\t// need to collect the object,\n\t\t\t\t\t\t\ttmpobj = new protoDef.protoDef.protoCon();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// look for a class type (shorthand) to recover.\n\t\t\t\t\t\t\tcls = classes.find( cls=>cls.name === val.string );\n\t\t\t\t\t\t\tif( !cls )\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t/* eslint-disable no-inner-declarations */\n\t\t\t\t\t\t\t   function privateProto(){}\n\t\t\t\t\t\t\t\t//sconsole.log( \"privateProto has no proto?\", privateProto.prototype.constructor.name );\n\t\t\t\t\t\t\t\tlocalFromProtoTypes.set( val.string,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{ protoCon:privateProto.prototype.constructor\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t, cb: null }\n\t\t\t\t\t\t\t\t\t\t\t\t\t   );\n\t\t\t\t\t\t\t\ttmpobj = new privateProto();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tnextMode = CONTEXT_CLASS_VALUE;\n\t\t\t\t\t\t\t\ttmpobj = {};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t//nextMode = CONTEXT_CLASS_VALUE;\n\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t}\n\t\t\t\t} else if( ( parse_context == CONTEXT_OBJECT_FIELD && word == WORD_POS_RESET ) ) {\n\t\t\t\t\tthrowError( \"fault while parsing; getting field name unexpected \", cInt );\n\t\t\t\t\tstatus = false;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\t// common code to push into next context\n\t\t\t\tlet old_context = getContext();\n\t\t\t\t//_DEBUG_PARSING && console.log( \"Begin a new object; previously pushed into elements; but wait until trailing comma or close previously \", val.value_type, val.className );\n\n\t\t\t\tval.value_type = VALUE_OBJECT;\n\t\t\t\tif( parse_context === CONTEXT_UNKNOWN ){\n\t\t\t\t\telements = tmpobj;\n\t\t\t\t} else if( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\tif( arrayType == -1 ) {\n\t\t\t\t\t\t// this is pushed later... \n\t\t\t\t\t\t//console.log( \"PUSHING OPEN OBJECT INTO EXISTING ARRAY - THIS SHOULD BE RE-SET?\", JSOX.stringify(context_stack.first.node) );\n\t\t\t\t\t\t//elements.push( tmpobj );\n\t\t\t\t\t}\n\t\t\t\t\tval.name = elements.length;\n\t\t\t\t\t//else if( //_DEBUG_PARSING && arrayType !== -3 )\n\t\t\t\t\t//\tconsole.log( \"This is an invalid parsing state, typed array with sub-object elements\" );\n\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD_VALUE || parse_context == CONTEXT_CLASS_VALUE ) {\n\t\t\t\t\tif( !val.name && current_class ){\n\t\t\t\t\t\tval.name = current_class.fields[current_class_field++];\n\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"B Stepping current class field:\", val, current_class_field, val.name );\n\t\t\t\t\t}\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"Setting element:\", val.name, tmpobj );\n\t\t\t\t\telements[val.name] = tmpobj;\n\t\t\t\t}\n\n\t\t\t\told_context.context = parse_context;\n\t\t\t\told_context.elements = elements;\n\t\t\t\t//old_context.element_array = element_array;\n\t\t\t\told_context.name = val.name;\n\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"pushing val.name:\", val.name, arrayType );\n\t\t\t\told_context.current_proto = current_proto;\n\t\t\t\told_context.current_class = current_class;\n\t\t\t\told_context.current_class_field = current_class_field;\n\t\t\t\told_context.valueType = val.value_type;\n\t\t\t\told_context.arrayType = arrayType; // pop that we don't want to have this value re-pushed.\n\t\t\t\told_context.className = val.className;\n\t\t\t\t//arrayType = -3; // this doesn't matter, it's an object state, and a new array will reset to -1\n\t\t\t\tval.className = null;\n\t\t\t\tval.name = null;\n\t\t\t\tcurrent_proto = protoDef;\n\t\t\t\tcurrent_class = cls;\n\t\t\t\t//console.log( \"Setting current class:\", current_class.name );\n\t\t\t\tcurrent_class_field = 0;\n\t\t\t\telements = tmpobj;\n\t\t\t\tif( !rootObject ) rootObject = elements;\n\t\t\t\t//_DEBUG_PARSING_STACK && console.log( \"push context (open object): \", context_stack.length, \" new mode:\", nextMode );\n\t\t\t\tcontext_stack.push( old_context );\n\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"RESET OBJECT FIELD\", old_context, context_stack );\n\t\t\t\tRESET_VAL();\n\t\t\t\tparse_context = nextMode;\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tfunction openArray() {\n\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"openArray()...\" );\n\t\t\t\tif( word > WORD_POS_RESET && word < WORD_POS_FIELD )\n\t\t\t\t\trecoverIdent( 91 );\n\n\t\t\t\tif( word == WORD_POS_END && val.string.length ) {\n\t\t\t\t\t//_DEBUG_PARSING && console.log( \"recover arrayType:\", arrayType, val.string );\n\t\t\t\t\tlet typeIndex = knownArrayTypeNames.findIndex( type=>(type === val.string) );\n\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\tif( typeIndex >= 0 ) {\n\t\t\t\t\t\tarrayType = typeIndex;\n\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\tval.string = null;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif( val.string === \"ref\" ) {\n\t\t\t\t\t\t\tval.className = null;\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"This will be a reference recovery for key:\", val );\n\t\t\t\t\t\t\tarrayType = -2;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif( localFromProtoTypes.get( val.string ) ) {\n\t\t\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\t\t} \n\t\t\t\t\t\t\telse if( fromProtoTypes.get( val.string ) ) {\n\t\t\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\t\t} else\n\t\t\t\t\t\t\t\tthrowError( `Unknown type '${val.string}' specified for array`, cInt );\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \" !!!!!A Set Classname:\", val.className );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD || word == WORD_POS_FIELD || word == WORD_POS_AFTER_FIELD ) {\n\t\t\t\t\tthrowError( \"Fault while parsing; while getting field name unexpected\", cInt );\n\t\t\t\t\tstatus = false;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t{\n\t\t\t\t\tlet old_context = getContext();\n\t\t\t\t\t//_DEBUG_PARSING && console.log( \"Begin a new array; previously pushed into elements; but wait until trailing comma or close previously \", val.value_type );\n\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"Opening array:\", val, parse_context );\n\t\t\t\t\tval.value_type = VALUE_ARRAY;\n\t\t\t\t\tlet tmparr = [];\n\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN )\n\t\t\t\t\t\telements = tmparr;\n\t\t\t\t\telse if( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\t\tif( arrayType == -1 ){\n\t\t\t\t\t\t\t//console.log( \"Pushing new opening array into existing array already RE-SET\" );\n\t\t\t\t\t\t\telements.push( tmparr );\n\t\t\t\t\t\t} //else if( //_DEBUG_PARSING && arrayType !== -3 )\n\t\t\t\t\t\tval.name = elements.length;\n\t\t\t\t\t\t//\tconsole.log( \"This is an invalid parsing state, typed array with sub-array elements\" );\n\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD_VALUE ) {\n\t\t\t\t\t\tif( !val.name ) {\n\t\t\t\t\t\t\tconsole.log( \"This says it's resolved.......\" );\n\t\t\t\t\t\t\tarrayType = -3;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif( current_proto && current_proto.protoDef ) {\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"SOMETHING SHOULD HAVE BEEN REPLACED HERE??\", current_proto );\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"(need to do fromprototoypes here) object:\", val, value );\n\t\t\t\t\t\t\tif( current_proto.protoDef.cb ){\n\t\t\t\t\t\t\t\tconst newarr = current_proto.protoDef.cb.call( elements, val.name, tmparr );\n\t\t\t\t\t\t\t\tif( newarr !== undefined ) tmparr = elements[val.name] = newarr;\n\t\t\t\t\t\t\t\t//else console.log( \"Warning: Received undefined for an array; keeping original array, not setting field\" );\n\t\t\t\t\t\t\t}else\n\t\t\t\t\t\t\t\telements[val.name] = tmparr;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse\n\t\t\t\t\t\t\telements[val.name] = tmparr;\n\t\t\t\t\t}\n\t\t\t\t\told_context.context = parse_context;\n\t\t\t\t\told_context.elements = elements;\n\t\t\t\t\t//old_context.element_array = element_array;\n\t\t\t\t\told_context.name = val.name;\n\t\t\t\t\told_context.current_proto = current_proto;\n\t\t\t\t\told_context.current_class = current_class;\n\t\t\t\t\told_context.current_class_field = current_class_field;\n\t\t\t\t\t// already pushed?\n\t\t\t\t\told_context.valueType = val.value_type;\n\t\t\t\t\told_context.arrayType = (arrayType==-1)?-3:arrayType; // pop that we don't want to have this value re-pushed.\n\t\t\t\t\told_context.className = val.className;\n\t\t\t\t\tarrayType = -1;\n\t\t\t\t\tval.className = null;\n\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \" !!!!!B Clear Classname:\", old_context, val.className, old_context.className, old_context.name );\n\t\t\t\t\tval.name = null;\n\t\t\t\t\tcurrent_proto = null;\n\t\t\t\t\tcurrent_class = null;\n\t\t\t\t\tcurrent_class_field = 0;\n\t\t\t\t\t//element_array = tmparr;\n\t\t\t\t\telements = tmparr;\n\t\t\t\t\tif( !rootObject ) rootObject = tmparr;\n\t\t\t\t\t//_DEBUG_PARSING_STACK && console.log( \"push context (open array): \", context_stack.length );\n\t\t\t\t\tcontext_stack.push( old_context );\n\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"RESET ARRAY FIELD\", old_context, context_stack );\n\n\t\t\t\t\tRESET_VAL();\n\t\t\t\t\tparse_context = CONTEXT_IN_ARRAY;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tfunction getProto() {\n\t\t\t\tconst result = {protoDef:null,cls:null};\n\t\t\t\tif( ( result.protoDef = localFromProtoTypes.get( val.string ) ) ) {\n\t\t\t\t\tif( !val.className ){\n\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\tval.string = null;\n\t\t\t\t\t}\n\t\t\t\t\t// need to collect the object, \n\t\t\t\t}\n\t\t\t\telse if( ( result.protoDef = fromProtoTypes.get( val.string ) ) ) {\n\t\t\t\t\tif( !val.className ){\n\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\tval.string = null;\n\t\t\t\t\t}\n\t\t\t\t} \n\t\t\t\tif( val.string )\n\t\t\t\t{\n\t\t\t\t\tresult.cls = classes.find( cls=>cls.name === val.string );\n\t\t\t\t\tif( !result.protoDef && !result.cls ) {\n\t\t\t\t\t    // this will creaet a class def with a new proto to cover when we don't KNOW.\n\t\t\t\t\t    //throwError( \"Referenced class \" + val.string + \" has not been defined\", cInt );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn (result.protoDef||result.cls)?result:null;\n\t\t\t}\n\n\t\t\tif( !status )\n\t\t\t\treturn -1;\n\n\t\t\tif( msg && msg.length ) {\n\t\t\t\tinput = getBuffer();\n\t\t\t\tinput.buf = msg;\n\t\t\t\tinQueue.push( input );\n\t\t\t} else {\n\t\t\t\tif( gatheringNumber ) {\n\t\t\t\t\t//console.log( \"Force completed.\")\n\t\t\t\t\tgatheringNumber = false;\n\t\t\t\t\tval.value_type = VALUE_NUMBER;\n\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t}\n\t\t\t\t\tretval = 1;  // if returning buffers, then obviously there's more in this one.\n\t\t\t\t}\n\t\t\t\tif( parse_context !== CONTEXT_UNKNOWN )\n\t\t\t\t\tthrowError( \"Unclosed object at end of stream.\", cInt );\n\t\t\t}\n\n\t\t\twhile( status && ( input = inQueue.shift() ) ) {\n\t\t\t\tn = input.n;\n\t\t\t\tbuf = input.buf;\n\t\t\t\tif( gatheringString ) {\n\t\t\t\t\tlet string_status = gatherString( gatheringStringFirstChar );\n\t\t\t\t\tif( string_status < 0 )\n\t\t\t\t\t\tstatus = false;\n\t\t\t\t\telse if( string_status > 0 ) {\n\t\t\t\t\t\tgatheringString = false;\n\t\t\t\t\t\tif( status ) val.value_type = VALUE_STRING;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif( gatheringNumber ) {\n\t\t\t\t\tcollectNumber();\n\t\t\t\t}\n\n\t\t\t\twhile( !completed && status && ( n < buf.length ) ) {\n\t\t\t\t\tstr = buf.charAt(n);\n\t\t\t\t\tcInt = buf.codePointAt(n++);\n\t\t\t\t\tif( cInt >= 0x10000 ) { str += buf.charAt(n); n++; }\n\t\t\t\t\t//_DEBUG_PARSING && console.log( \"parsing at \", cInt, str );\n\t\t\t\t\t//_DEBUG_LL && console.log( \"processing: \", cInt, n, str, pos, comment, parse_context, word );\n\t\t\t\t\tpos.col++;\n\t\t\t\t\tif( comment ) {\n\t\t\t\t\t\tif( comment == 1 ) {\n\t\t\t\t\t\t\tif( cInt == 42/*'*'*/ ) comment = 3;\n\t\t\t\t\t\t\telse if( cInt != 47/*'/'*/ ) return throwError( \"fault while parsing;\", cInt );\n\t\t\t\t\t\t\telse comment = 2;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if( comment == 2 ) {\n\t\t\t\t\t\t\tif( cInt == 10/*'\\n'*/ || cInt == 13/*'\\r'*/  ) comment = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if( comment == 3 ) {\n\t\t\t\t\t\t\tif( cInt == 42/*'*'*/ ) comment = 4;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tif( cInt == 47/*'/'*/ ) comment = 0;\n\t\t\t\t\t\t\telse comment = 3;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tswitch( cInt ) {\n\t\t\t\t\tcase 35/*'#'*/:\n\t\t\t\t\t\tcomment = 2; // pretend this is the second slash.\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 47/*'/'*/:\n\t\t\t\t\t\tcomment = 1;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 123/*'{'*/:\n\t\t\t\t\t\topenObject();\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 91/*'['*/:\n\t\t\t\t\t\topenArray();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 58/*':'*/:\n\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"colon received...\")\n\t\t\t\t\t\tif( parse_context == CONTEXT_CLASS_VALUE ) {\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\tval.name = val.string;\n\t\t\t\t\t\t\tval.string = '';\n\t\t\t\t\t\t\tval.value_type = VALUE_UNSET;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD\n\t\t\t\t\t\t\t|| parse_context == CONTEXT_CLASS_FIELD  ) {\n\t\t\t\t\t\t\tif( parse_context == CONTEXT_CLASS_FIELD ) {\n\t\t\t\t\t\t\t\tif( !Object.keys( elements).length ) {\n\t\t\t\t\t\t\t\t\t console.log( \"This is a full object, not a class def...\", val.className );\n\t\t\t\t\t\t\t\tconst privateProto = ()=>{} \n\t\t\t\t\t\t\t\tlocalFromProtoTypes.set( context_stack.last.node.current_class.name,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{ protoCon:privateProto.prototype.constructor\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t, cb: null }\n\t\t\t\t\t\t\t\t\t\t\t\t\t   );\n\t\t\t\t\t\t\t\telements = new privateProto();\n\t\t\t\t\t\t\t\tparse_context = CONTEXT_OBJECT_FIELD_VALUE\n\t\t\t\t\t\t\t\tval.name = val.string;\n\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\tval.string = ''\n\t\t\t\t\t\t\t\tval.value_type = VALUE_UNSET;\n\t\t\t\t\t\t\t\tconsole.log( \"don't do default;s do a revive...\" );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tif( word != WORD_POS_RESET\n\t\t\t\t\t\t\t\t   && word != WORD_POS_END\n\t\t\t\t\t\t\t\t   && word != WORD_POS_FIELD\n\t\t\t\t\t\t\t\t   && word != WORD_POS_AFTER_FIELD ) {\n\t\t\t\t\t\t\t\t\trecoverIdent( 32 );\n\t\t\t\t\t\t\t\t\t// allow starting a new word\n\t\t\t\t\t\t\t\t\t//status = false;\n\t\t\t\t\t\t\t\t\t//throwError( `fault while parsing; unquoted keyword used as object field name (state:${word})`, cInt );\n\t\t\t\t\t\t\t\t\t//break;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\tval.name = val.string;\n\t\t\t\t\t\t\t\tval.string = '';\n\t\t\t\t\t\t\t\tparse_context = (parse_context===CONTEXT_OBJECT_FIELD)?CONTEXT_OBJECT_FIELD_VALUE:CONTEXT_CLASS_FIELD_VALUE;\n\t\t\t\t\t\t\t\tval.value_type = VALUE_UNSET;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if( parse_context == CONTEXT_UNKNOWN ){\n\t\t\t\t\t\t\tconsole.log( \"Override colon found, allow class redefinition\", parse_context );\n\t\t\t\t\t\t\tredefineClass = true;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif( parse_context == CONTEXT_IN_ARRAY )\n\t\t\t\t\t\t\t\tthrowError(  \"(in array, got colon out of string):parsing fault;\", cInt );\n\t\t\t\t\t\t\telse if( parse_context == CONTEXT_OBJECT_FIELD_VALUE ){\n\t\t\t\t\t\t\t\tthrowError( \"String unexpected\", cInt );\n\t\t\t\t\t\t\t} else\n\t\t\t\t\t\t\t\tthrowError( \"(outside any object, got colon out of string):parsing fault;\", cInt );\n\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 125/*'}'*/:\n\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"close bracket context:\", word, parse_context, val.value_type, val.string );\n\t\t\t\t\t\tif( word == WORD_POS_END ) {\n\t\t\t\t\t\t\t// allow starting a new word\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// coming back after pushing an array or sub-object will reset the contxt to FIELD, so an end with a field should still push value.\n\t\t\t\t\t\tif( parse_context == CONTEXT_CLASS_FIELD ) {\n\t\t\t\t\t\t\tif( current_class ) {\n\t\t\t\t\t\t\t\t// allow blank comma at end to not be a field\n\t\t\t\t\t\t\t\tif(val.string) { current_class.fields.push( val.string ); }\n\n\t\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\t\tlet old_context = context_stack.pop();\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"close object:\", old_context, context_stack );\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_STACK && console.log( \"object pop stack (close obj)\", context_stack.length, old_context );\n\t\t\t\t\t\t\t\tparse_context = CONTEXT_UNKNOWN; // this will restore as IN_ARRAY or OBJECT_FIELD\n\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\tval.name = old_context.name;\n\t\t\t\t\t\t\t\telements = old_context.elements;\n\t\t\t\t\t\t\t\t//element_array = old_context.element_array;\n\t\t\t\t\t\t\t\tcurrent_class = old_context.current_class;\n\t\t\t\t\t\t\t\tcurrent_class_field = old_context.current_class_field;\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"A Pop old class field counter:\", current_class_field, val.name );\n\t\t\t\t\t\t\t\tarrayType = old_context.arrayType;\n\t\t\t\t\t\t\t\tval.value_type = old_context.valueType;\n\t\t\t\t\t\t\t\tval.className = old_context.className;\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \" !!!!!C Pop Classname:\", val.className );\n\t\t\t\t\t\t\t\trootObject = null;\n\n\t\t\t\t\t\t\t\tdropContext( old_context );\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthrowError( \"State error; gathering class fields, and lost the class\", cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( ( parse_context == CONTEXT_OBJECT_FIELD ) || ( parse_context == CONTEXT_CLASS_VALUE ) ) {\n\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\tif( current_class ) {\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"C Stepping current class field:\", current_class_field, val.name, arrayType );\n\t\t\t\t\t\t\t\t\tval.name = current_class.fields[current_class_field++];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"Closing object; set value name, and push...\", current_class_field, val );\n\t\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"close object; empty object\", val, elements );\n\n\t\t\t\t\t\t\t\tval.value_type = VALUE_OBJECT;\n\t\t\t\t\t\t\t\tif( current_proto && current_proto.protoDef ) {\n\t\t\t\t\t\t\t\t\tconsole.log( \"SOMETHING SHOULD AHVE BEEN REPLACED HERE??\", current_proto );\n\t\t\t\t\t\t\t\t\tconsole.log( \"The other version only revives on init\" );\n\t\t\t\t\t\t\t\t\telements = new current_proto.protoDef.cb( elements, undefined, undefined );\n\t\t\t\t\t\t\t\t\t//elements = new current_proto.protoCon( elements );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tval.contains = elements;\n\t\t\t\t\t\t\t\tval.string = \"\";\n\n\t\t\t\t\t\t\tlet old_context = context_stack.pop();\n\t\t\t\t\t\t\t//_DEBUG_PARSING_STACK && console.log( \"object pop stack (close obj)\", context_stack.length, old_context );\n\t\t\t\t\t\t\tparse_context = old_context.context; // this will restore as IN_ARRAY or OBJECT_FIELD\n\t\t\t\t\t\t\tval.name = old_context.name;\n\t\t\t\t\t\t\telements = old_context.elements;\n\t\t\t\t\t\t\t//element_array = old_context.element_array;\n\t\t\t\t\t\t\tcurrent_class = old_context.current_class;\n\t\t\t\t\t\t\tcurrent_proto = old_context.current_proto;\n\t\t\t\t\t\t\tcurrent_class_field = old_context.current_class_field;\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"B Pop old class field counter:\", context_stack, current_class_field, val.name );\n\t\t\t\t\t\t\tarrayType = old_context.arrayType;\n\t\t\t\t\t\t\tval.value_type = old_context.valueType;\n\t\t\t\t\t\t\tval.className = old_context.className;\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \" !!!!!D Pop Classname:\", val.className );\n\t\t\t\t\t\t\tdropContext( old_context );\n\n\t\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if( ( parse_context == CONTEXT_OBJECT_FIELD_VALUE ) ) {\n\t\t\t\t\t\t\t// first, add the last value\n\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"close object; push item '%s' %d\", val.name, val.value_type );\n\t\t\t\t\t\t\tif( val.value_type === VALUE_UNSET ) {\n\t\t\t\t\t\t\t\tthrowError( \"Fault while parsing; unexpected\", cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\tval.value_type = VALUE_OBJECT;\n\t\t\t\t\t\t\tval.contains = elements;\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\n\t\t\t\t\t\t\t//let old_context = context_stack.pop();\n\t\t\t\t\t\t\tlet old_context = context_stack.pop();\n\t\t\t\t\t\t\t//_DEBUG_PARSING_STACK  && console.log( \"object pop stack (close object)\", context_stack.length, old_context );\n\t\t\t\t\t\t\tparse_context = old_context.context; // this will restore as IN_ARRAY or OBJECT_FIELD\n\t\t\t\t\t\t\tval.name = old_context.name;\n\t\t\t\t\t\t\telements = old_context.elements;\n\t\t\t\t\t\t\tcurrent_proto = old_context.current_proto;\n\t\t\t\t\t\t\tcurrent_class = old_context.current_class;\n\t\t\t\t\t\t\tcurrent_class_field = old_context.current_class_field;\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"C Pop old class field counter:\", context_stack, current_class_field, val.name );\n\t\t\t\t\t\t\tarrayType = old_context.arrayType;\n\t\t\t\t\t\t\tval.value_type = old_context.valueType;\n\t\t\t\t\t\t\tval.className = old_context.className;\n\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \" !!!!!E Pop Classname:\", val.className );\n\t\t\t\t\t\t\t//element_array = old_context.element_array;\n\t\t\t\t\t\t\tdropContext( old_context );\n\t\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthrowError( \"Fault while parsing; unexpected\", cInt );\n\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tnegative = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 93/*']'*/:\n\t\t\t\t\t\tif( word >= WORD_POS_AFTER_FIELD ) {\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t//_DEBUG_PARSING  && console.log( \"close array, push last element: %d\", val.value_type );\n\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t// name is set when saving a context.\n\t\t\t\t\t\t\t\t// a better sanity check would be val.name === elements.length;\n\t\t\t\t\t\t\t\t//if( val.name ) if( val.name !== elements.length ) console.log( \"Ya this should blow up\" );\n\t\t\t\t\t\t\t\tarrayPush();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tval.contains = elements;\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tlet old_context = context_stack.pop();\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_STACK  && console.log( \"object pop stack (close array)\", context_stack.length );\n\t\t\t\t\t\t\t\tval.name = old_context.name;\n\t\t\t\t\t\t\t\tval.className = old_context.className;\n\t\t\t\t\t\t\t\tparse_context = old_context.context;\n\t\t\t\t\t\t\t\telements = old_context.elements;\n\t\t\t\t\t\t\t\t//element_array = old_context.element_array;\n\t\t\t\t\t\t\t\tcurrent_proto = old_context.current_proto;\n\t\t\t\t\t\t\t\tcurrent_class = old_context.current_class;\n\t\t\t\t\t\t\t\tcurrent_class_field = old_context.current_class_field;\n\t\t\t\t\t\t\t\tarrayType = old_context.arrayType;\n\t\t\t\t\t\t\t\tval.value_type = old_context.valueType;\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"close array:\", old_context );\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"D Pop old class field counter:\", context_stack, current_class_field, val );\n\t\t\t\t\t\t\t\tdropContext( old_context );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tval.value_type = VALUE_ARRAY;\n\t\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrowError( `bad context ${parse_context}; fault while parsing`, cInt );// fault\n\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tnegative = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 44/*','*/:\n\t\t\t\t\t\tif( word < WORD_POS_AFTER_FIELD && word != WORD_POS_RESET ) {\n\t\t\t\t\t\t\trecoverIdent(cInt);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif( word == WORD_POS_END || word == WORD_POS_FIELD ) word = WORD_POS_RESET;  // allow collect new keyword\n\t\t\t\t\t\t//if(//_DEBUG_PARSING) \n\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"comma context:\", parse_context, val );\n\t\t\t\t\t\tif( parse_context == CONTEXT_CLASS_FIELD ) {\n\t\t\t\t\t\t\tif( current_class ) {\n\t\t\t\t\t\t\t\t//console.log( \"Saving field name(set word to IS A FIELD):\", val.string );\n\t\t\t\t\t\t\t\tcurrent_class.fields.push( val.string );\n\t\t\t\t\t\t\t\tval.string = '';\n\t\t\t\t\t\t\t\tword = WORD_POS_FIELD;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthrowError( \"State error; gathering class fields, and lost the class\", cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD ) {\n\t\t\t\t\t\t\tif( current_class ) {\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"D Stepping current class field:\", current_class_field, val.name );\n\t\t\t\t\t\t\t\tval.name = current_class.fields[current_class_field++];\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"should have a completed value at a comma.:\", current_class_field, val );\n\t\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING  && console.log( \"pushing object field:\", val );\n\t\t\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// this is an empty comma...\n\t\t\t\t\t\t\t\tif( val.string || val.value_type )\n\t\t\t\t\t\t\t\t\tthrowError( \"State error; comma in field name and/or lost the class\", cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if( parse_context == CONTEXT_CLASS_VALUE ) {\n\t\t\t\t\t\t\tif( current_class ) {\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"reviving values in class...\", arrayType, current_class.fields[current_class_field ], val );\n\t\t\t\t\t\t\t\tif( arrayType != -3 && !val.name ) {\n\t\t\t\t\t\t\t\t\t// this should have still had a name....\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"E Stepping current class field:\", current_class_field, val, arrayType );\n\t\t\t\t\t\t\t\t\tval.name = current_class.fields[current_class_field++];\n\t\t\t\t\t\t\t\t\t//else val.name = current_class.fields[current_class_field++];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"should have a completed value at a comma.:\", current_class_field, val );\n\t\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t\tif( arrayType != -3 )\n\t\t\t\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t//throwError( \"State error; gathering class values, and lost the class\", cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tval.name = null;\n\t\t\t\t\t\t} else if( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\t\t\tif( val.value_type == VALUE_UNSET )\n\t\t\t\t\t\t\t\tval.value_type = VALUE_EMPTY; // in an array, elements after a comma should init as undefined...\n\n\t\t\t\t\t\t\t//_DEBUG_PARSING  && console.log( \"back in array; push item %d\", val.value_type );\n\t\t\t\t\t\t\tarrayPush();\n\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t// undefined allows [,,,] to be 4 values and [1,2,3,] to be 4 values with an undefined at end.\n\t\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD_VALUE && val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t// after an array value, it will have returned to OBJECT_FIELD anyway\n\t\t\t\t\t\t\t//_DEBUG_PARSING  && console.log( \"comma after field value, push field to object: %s\", val.name, val.value_type );\n\t\t\t\t\t\t\tparse_context = CONTEXT_OBJECT_FIELD;\n\t\t\t\t\t\t\tif( val.value_type != VALUE_UNSET ) {\n\t\t\t\t\t\t\t\tobjectPush();\n\t\t\t\t\t\t\t\tRESET_VAL();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\tthrowError( \"bad context; excessive commas while parsing;\", cInt );// fault\n\t\t\t\t\t\t}\n\t\t\t\t\t\tnegative = false;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tswitch( cInt ) {\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\tif( ( parse_context == CONTEXT_UNKNOWN )\n\t\t\t\t\t\t  || ( parse_context == CONTEXT_OBJECT_FIELD_VALUE && word == WORD_POS_FIELD )\n\t\t\t\t\t\t  || ( ( parse_context == CONTEXT_OBJECT_FIELD ) || word == WORD_POS_FIELD )\n\t\t\t\t\t\t  || ( parse_context == CONTEXT_CLASS_FIELD ) ) {\n\t\t\t\t\t\t\tswitch( cInt ) {\n\t\t\t\t\t\t\tcase 96://'`':\n\t\t\t\t\t\t\tcase 34://'\"':\n\t\t\t\t\t\t\tcase 39://'\\'':\n\t\t\t\t\t\t\t\tif( word == WORD_POS_RESET || word == WORD_POS_FIELD ) {\n\t\t\t\t\t\t\t\t\tif( val.string.length ) {\n\t\t\t\t\t\t\t\t\t\tconsole.log( \"IN ARRAY AND FIXING?\" );\n\t\t\t\t\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\t\t\t\t\tval.string = '';\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tlet string_status = gatherString(cInt );\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"string gather for object field name :\", val.string, string_status );\n\t\t\t\t\t\t\t\t\tif( string_status ) {\n\t\t\t\t\t\t\t\t\t\tval.value_type = VALUE_STRING;\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tgatheringStringFirstChar = cInt;\n\t\t\t\t\t\t\t\t\t\tgatheringString = true;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthrowError( \"fault while parsing; quote not at start of field name\", cInt );\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 10://'\\n':\n\t\t\t\t\t\t\t\tpos.line++;\n\t\t\t\t\t\t\t\tpos.col = 1;\n\t\t\t\t\t\t\t\t// fall through to normal space handling - just updated line/col position\n\t\t\t\t\t\t\tcase 13://'\\r':\n\t\t\t\t\t\t\tcase 32://' ':\n\t\t\t\t\t\t\tcase 0x2028://' ':\n\t\t\t\t\t\t\tcase 0x2029://' ':\n\t\t\t\t\t\t\tcase 9://'\\t':\n\t\t\t\t\t\t\tcase 0xFEFF: // ZWNBS is WS though\n\t\t\t\t\t\t\t\t //_DEBUG_WHITESPACE  && console.log( \"THIS SPACE\", word, parse_context, val );\n\t\t\t\t\t\t\t\tif( parse_context === CONTEXT_UNKNOWN && word === WORD_POS_END ) { // allow collect new keyword\n\t\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\t\tif( parse_context === CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif( word === WORD_POS_RESET || word === WORD_POS_AFTER_FIELD ) { // ignore leading and trailing whitepsace\n\t\t\t\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN && val.value_type ) {\n\t\t\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse if( word === WORD_POS_FIELD ) {\n\t\t\t\t\t\t\t\t\tif( parse_context === CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif( val.string.length )\n\t\t\t\t\t\t\t\t\t\tconsole.log( \"STEP TO NEXT TOKEN.\" );\n\t\t\t\t\t\t\t\t\t\tword = WORD_POS_AFTER_FIELD;\n\t\t\t\t\t\t\t\t\t\t//val.className = val.string; val.string = '';\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\t\tthrowError( \"fault while parsing; whitepsace unexpected\", cInt );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// skip whitespace\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t//if( /((\\n|\\r|\\t)|s|S|[ \\{\\}\\(\\)\\<\\>\\!\\+-\\*\\/\\.\\:\\, ])/.\n\t\t\t\t\t\t\t\tif( testNonIdentifierCharacters ) {\n\t\t\t\t\t\t\t\tlet identRow = nonIdent.find( row=>(row.firstChar >= cInt )&& (row.lastChar > cInt) )\n\t\t\t\t\t\t\t\tif( identRow && ( identRow.bits[(cInt - identRow.firstChar) / 24]\n\t\t\t\t\t\t\t\t    & (1 << ((cInt - identRow.firstChar) % 24)))) {\n\t\t\t\t\t\t\t\t//if( nonIdent[(cInt/(24*16))|0] && nonIdent[(cInt/(24*16))|0][(( cInt % (24*16) )/24)|0] & ( 1 << (cInt%24)) ) {\n\t\t\t\t\t\t\t\t\t// invalid start/continue\n\t\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\t\tthrowError( `fault while parsing object field name; \\\\u${cInt}`, cInt );\t// fault\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t//console.log( \"TICK\" );\n\t\t\t\t\t\t\t\tif( word == WORD_POS_RESET && ( ( cInt >= 48/*'0'*/ && cInt <= 57/*'9'*/ ) || ( cInt == 43/*'+'*/ ) || ( cInt == 46/*'.'*/ ) || ( cInt == 45/*'-'*/ ) ) ) {\n\t\t\t\t\t\t\t\t\tfromHex = false;\n\t\t\t\t\t\t\t\t\texponent = false;\n\t\t\t\t\t\t\t\t\tdate_format = false;\n\t\t\t\t\t\t\t\t\tisBigInt = false;\n\n\t\t\t\t\t\t\t\t\texponent_sign = false;\n\t\t\t\t\t\t\t\t\texponent_digit = false;\n\t\t\t\t\t\t\t\t\tdecimal = false;\n\t\t\t\t\t\t\t\t\tval.string = str;\n\t\t\t\t\t\t\t\t\tinput.n = n;\n\t\t\t\t\t\t\t\t\tcollectNumber();\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tif( word === WORD_POS_AFTER_FIELD ) {\n\t\t\t\t\t\t\t\t\tstatus = false;\n\t\t\t\t\t\t\t\t\tthrowError( \"fault while parsing; character unexpected\", cInt );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif( word === WORD_POS_RESET ) {\n\t\t\t\t\t\t\t\t\tword = WORD_POS_FIELD;\n\t\t\t\t\t\t\t\t\tval.value_type = VALUE_STRING;\n\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t//_DEBUG_PARSING  && console.log( \"START/CONTINUE IDENTIFER\" );\n\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\t}     \n\t\t\t\t\t\t\t\tif( val.value_type == VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t\tif( word !== WORD_POS_RESET && word !== WORD_POS_END )\n\t\t\t\t\t\t\t\t\t\trecoverIdent( cInt );\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tif( word === WORD_POS_END || word === WORD_POS_FIELD ) {\n\t\t\t\t\t\t\t\t\t\t// final word of the line... \n\t\t\t\t\t\t\t\t\t\t// whispace changes the 'word' state to not 'end'\n\t\t\t\t\t\t\t\t\t\t// until the next character, which may restore it to\n\t\t\t\t\t\t\t\t\t\t// 'end' and this will resume collecting the same string.\n\t\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif( parse_context == CONTEXT_OBJECT_FIELD ) {\n\t\t\t\t\t\t\t\t\t\tif( word == WORD_POS_FIELD ) {\n\t\t\t\t\t\t\t\t\t\t\tval.string+=str;\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tthrowError( \"Multiple values found in field name\", cInt );\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif( parse_context == CONTEXT_OBJECT_FIELD_VALUE ) {\n\t\t\t\t\t\t\t\t\t\tthrowError( \"String unexpected\", cInt );\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak; // default\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t}else {\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET && ( ( cInt >= 48/*'0'*/ && cInt <= 57/*'9'*/ ) || ( cInt == 43/*'+'*/ ) || ( cInt == 46/*'.'*/ ) || ( cInt == 45/*'-'*/ ) ) ) {\n\t\t\t\t\t\t\t\tfromHex = false;\n\t\t\t\t\t\t\t\texponent = false;\n\t\t\t\t\t\t\t\tdate_format = false;\n\t\t\t\t\t\t\t\tisBigInt = false;\n\n\t\t\t\t\t\t\t\texponent_sign = false;\n\t\t\t\t\t\t\t\texponent_digit = false;\n\t\t\t\t\t\t\t\tdecimal = false;\n\t\t\t\t\t\t\t\tval.string = str;\n\t\t\t\t\t\t\t\tinput.n = n;\n\t\t\t\t\t\t\t\tcollectNumber();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t//console.log( \"TICK\")\n\t\t\t\t\t\t\t\tif( val.value_type == VALUE_UNSET ) {\n\t\t\t\t\t\t\t\t\tif( word != WORD_POS_RESET ) {\n\t\t\t\t\t\t\t\t\t\trecoverIdent( cInt );\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t\tval.value_type = VALUE_STRING;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tif( parse_context == CONTEXT_OBJECT_FIELD ) {\n\t\t\t\t\t\t\t\t\t\tthrowError( \"Multiple values found in field name\", cInt );\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse if( parse_context == CONTEXT_OBJECT_FIELD_VALUE ) {\n\n\t\t\t\t\t\t\t\t\t\tif( val.value_type != VALUE_STRING ) {\n\t\t\t\t\t\t\t\t\t\t\tif( val.value_type == VALUE_OBJECT || val.value_type == VALUE_ARRAY ){\n\t\t\t\t\t\t\t\t\t\t\t\tthrowError( \"String unexpected\", cInt );\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\trecoverIdent(cInt);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tif( word == WORD_POS_AFTER_FIELD ){\n\t\t\t\t\t\t\t\t\t\t\tconst  protoDef = getProto();\n\t\t\t\t\t\t\t\t\t\t\tif( protoDef){\n\t\t\t\t\t\t\t\t\t\t\t\tword == WORD_POS_END; // last string.\n\t\t\t\t\t\t\t\t\t\t\t\tval.string = str;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\telse \n\t\t\t\t\t\t\t\t\t\t\t\tthrowError( \"String unexpected\", cInt );\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\tif( word == WORD_POS_END ) {\n\t\t\t\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t\t\t}else\n\t\t\t\t\t\t\t\t\t\t\t\tthrowError( \"String unexpected\", cInt );\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse if( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\t\t\t\t\t\tif( word == WORD_POS_AFTER_FIELD ){\n\t\t\t\t\t\t\t\t\t\t\tif( !val.className ){\n\t\t\t\t\t\t\t\t\t\t\t\t//\tgetProto()\n\t\t\t\t\t\t\t\t\t\t\t\tval.className = val.string;\n\t\t\t\t\t\t\t\t\t\t\t\tval.string = '';\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\tif( word == WORD_POS_END )\n\t\t\t\t\t\t\t\t\t\t\t\tval.string += str;\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t//recoverIdent(cInt);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak; // default\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 96://'`':\n\t\t\t\t\t\tcase 34://'\"':\n\t\t\t\t\t\tcase 39://'\\'':\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tif( val.string ) val.className = val.string; val.string = '';\n\t\t\t\t\t\t\tlet string_status = gatherString( cInt );\n\t\t\t\t\t\t\t//_DEBUG_PARSING && console.log( \"string gather for object field value :\", val.string, string_status, completed, input.n, buf.length );\n\t\t\t\t\t\t\tif( string_status ) {\n\t\t\t\t\t\t\t\tval.value_type = VALUE_STRING;\n\t\t\t\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tgatheringStringFirstChar = cInt;\n\t\t\t\t\t\t\t\tgatheringString = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcase 10://'\\n':\n\t\t\t\t\t\t\tpos.line++;\n\t\t\t\t\t\t\tpos.col = 1;\n\t\t\t\t\t\t\t//falls through\n\t\t\t\t\t\tcase 32://' ':\n\t\t\t\t\t\tcase 9://'\\t':\n\t\t\t\t\t\tcase 13://'\\r':\n\t\t\t\t\t\tcase 0x2028: // LS (Line separator)\n\t\t\t\t\t\tcase 0x2029: // PS (paragraph separate)\n\t\t\t\t\t\tcase 0xFEFF://'\\uFEFF':\n\t\t\t\t\t\t\t//_DEBUG_WHITESPACE && console.log( \"Whitespace...\", word, parse_context );\n\t\t\t\t\t\t\tif( word == WORD_POS_END ) {\n\t\t\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN ) {\n\t\t\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD_VALUE ) {\n\t\t\t\t\t\t\t\t\tword = WORD_POS_AFTER_FIELD_VALUE;\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t} else if( parse_context == CONTEXT_OBJECT_FIELD ) {\n\t\t\t\t\t\t\t\t\tword = WORD_POS_AFTER_FIELD;\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t} else if( parse_context == CONTEXT_IN_ARRAY ) {\n\t\t\t\t\t\t\t\t\tword = WORD_POS_AFTER_FIELD;\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET || ( word == WORD_POS_AFTER_FIELD ))\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\telse if( word == WORD_POS_FIELD ) {\n\t\t\t\t\t\t\t\tif( val.string.length )\n\t\t\t\t\t\t\t\t\tword = WORD_POS_AFTER_FIELD;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tif( word < WORD_POS_END ) \n\t\t\t\t\t\t\t\t\trecoverIdent( cInt );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t//----------------------------------------------------------\n\t\t\t\t\t//  catch characters for true/false/null/undefined which are values outside of quotes\n\t\t\t\t\t\tcase 116://'t':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) word = WORD_POS_TRUE_1;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_6 ) word = WORD_POS_INFINITY_7;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 114://'r':\n\t\t\t\t\t\t\tif( word == WORD_POS_TRUE_1 ) word = WORD_POS_TRUE_2;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 117://'u':\n\t\t\t\t\t\t\tif( word == WORD_POS_TRUE_2 ) word = WORD_POS_TRUE_3;\n\t\t\t\t\t\t\telse if( word == WORD_POS_NULL_1 ) word = WORD_POS_NULL_2;\n\t\t\t\t\t\t\telse if( word == WORD_POS_RESET ) word = WORD_POS_UNDEFINED_1;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 101://'e':\n\t\t\t\t\t\t\tif( word == WORD_POS_TRUE_3 ) {\n\t\t\t\t\t\t\t\tval.value_type = VALUE_TRUE;\n\t\t\t\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t\t\t} else if( word == WORD_POS_FALSE_4 ) {\n\t\t\t\t\t\t\t\tval.value_type = VALUE_FALSE;\n\t\t\t\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t\t\t} else if( word == WORD_POS_UNDEFINED_3 ) word = WORD_POS_UNDEFINED_4;\n\t\t\t\t\t\t\telse if( word == WORD_POS_UNDEFINED_7 ) word = WORD_POS_UNDEFINED_8;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 110://'n':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) word = WORD_POS_NULL_1;\n\t\t\t\t\t\t\telse if( word == WORD_POS_UNDEFINED_1 ) word = WORD_POS_UNDEFINED_2;\n\t\t\t\t\t\t\telse if( word == WORD_POS_UNDEFINED_6 ) word = WORD_POS_UNDEFINED_7;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_1 ) word = WORD_POS_INFINITY_2;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_4 ) word = WORD_POS_INFINITY_5;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 100://'d':\n\t\t\t\t\t\t\tif( word == WORD_POS_UNDEFINED_2 ) word = WORD_POS_UNDEFINED_3;\n\t\t\t\t\t\t\telse if( word == WORD_POS_UNDEFINED_8 ) { val.value_type=VALUE_UNDEFINED; word = WORD_POS_END; }\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 105://'i':\n\t\t\t\t\t\t\tif( word == WORD_POS_UNDEFINED_5 ) word = WORD_POS_UNDEFINED_6;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_3 ) word = WORD_POS_INFINITY_4;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_5 ) word = WORD_POS_INFINITY_6;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 108://'l':\n\t\t\t\t\t\t\tif( word == WORD_POS_NULL_2 ) word = WORD_POS_NULL_3;\n\t\t\t\t\t\t\telse if( word == WORD_POS_NULL_3 ) {\n\t\t\t\t\t\t\t\tval.value_type = VALUE_NULL;\n\t\t\t\t\t\t\t\tword = WORD_POS_END;\n\t\t\t\t\t\t\t} else if( word == WORD_POS_FALSE_2 ) word = WORD_POS_FALSE_3;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 102://'f':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) word = WORD_POS_FALSE_1;\n\t\t\t\t\t\t\telse if( word == WORD_POS_UNDEFINED_4 ) word = WORD_POS_UNDEFINED_5;\n\t\t\t\t\t\t\telse if( word == WORD_POS_INFINITY_2 ) word = WORD_POS_INFINITY_3;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 97://'a':\n\t\t\t\t\t\t\tif( word == WORD_POS_FALSE_1 ) word = WORD_POS_FALSE_2;\n\t\t\t\t\t\t\telse if( word == WORD_POS_NAN_1 ) word = WORD_POS_NAN_2;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 115://'s':\n\t\t\t\t\t\t\tif( word == WORD_POS_FALSE_3 ) word = WORD_POS_FALSE_4;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 73://'I':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) word = WORD_POS_INFINITY_1;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 78://'N':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) word = WORD_POS_NAN_1;\n\t\t\t\t\t\t\telse if( word == WORD_POS_NAN_2 ) { val.value_type = negative ? VALUE_NEG_NAN : VALUE_NAN; negative = false; word = WORD_POS_END; }\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 121://'y':\n\t\t\t\t\t\t\tif( word == WORD_POS_INFINITY_7 ) { val.value_type = negative ? VALUE_NEG_INFINITY : VALUE_INFINITY; negative = false; word = WORD_POS_END; }\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 45://'-':\n\t\t\t\t\t\t\tif( word == WORD_POS_RESET ) negative = !negative;\n\t\t\t\t\t\t\telse { recoverIdent(cInt); }// fault\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 43://'+':\n\t\t\t\t\t\t\tif( word !== WORD_POS_RESET ) { recoverIdent(cInt); }\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak; // default of high level switch\n\t\t\t\t\t//\n\t\t\t\t\t//----------------------------------------------------------\n\t\t\t\t\t}\n\t\t\t\t\tif( completed ) {\n\t\t\t\t\t\tif( word == WORD_POS_END ) {\n\t\t\t\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif( n == buf.length ) {\n\t\t\t\t\tdropBuffer( input );\n\t\t\t\t\tif( gatheringString || gatheringNumber || parse_context == CONTEXT_OBJECT_FIELD ) {\n\t\t\t\t\t\tretval = 0;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tif( parse_context == CONTEXT_UNKNOWN && ( val.value_type != VALUE_UNSET || result ) ) {\n\t\t\t\t\t\t\tcompleted = true;\n\t\t\t\t\t\t\tretval = 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// put these back into the stack.\n\t\t\t\t\tinput.n = n;\n\t\t\t\t\tinQueue.unshift( input );\n\t\t\t\t\tretval = 2;  // if returning buffers, then obviously there's more in this one.\n\t\t\t\t}\n\t\t\t\tif( completed ) {\n\t\t\t\t\trootObject = null;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif( !status ) return -1;\n\t\t\tif( completed && val.value_type != VALUE_UNSET ) {\n\t\t\t\tword = WORD_POS_RESET;\n\t\t\t\tresult = convertValue();\n\t\t\t\t//_DEBUG_PARSING && console.log( \"Result(3):\", result );\n\t\t\t\tnegative = false;\n\t\t\t\tval.string = '';\n\t\t\t\tval.value_type = VALUE_UNSET;\n\t\t\t}\n\t\t\tcompleted = false;\n\t\t\treturn retval;\n\t\t}\n\t}\n}\n\n\n\nconst _parser = [Object.freeze( JSOX.begin() )];\nlet _parse_level = 0;\n/**\n * @param {string} msg \n * @param {(this: unknown, key: string, value: unknown) => any} [reviver] \n * @returns {unknown}\n */\nJSOX.parse = function( msg, reviver ) {\n\tlet parse_level = _parse_level++;\n\tlet parser;\n\tif( _parser.length <= parse_level )\n\t\t_parser.push( Object.freeze( JSOX.begin() ) );\n\tparser = _parser[parse_level];\n\tif (typeof msg !== \"string\") msg = String(msg);\n\tparser.reset();\n\tconst writeResult = parser._write( msg, true );\n\tif( writeResult > 0 ) {\n\t\tif( writeResult > 1 ){\n\t\t\t// probably a carriage return.\n\t\t\t//console.log( \"Extra data at end of message\");\n\t\t}\n\t\tlet result = parser.value();\n\t\tif( ( \"undefined\" === typeof result ) && writeResult > 1 ){\n\t\t\tthrow new Error( \"Pending value could not complete\");\n\t\t}\n\n\t\tresult = typeof reviver === 'function' ? (function walk(holder, key) {\n\t\t\tlet k, v, value = holder[key];\n\t\t\tif (value && typeof value === 'object') {\n\t\t\t\tfor (k in value) {\n\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(value, k)) {\n\t\t\t\t\t\tv = walk(value, k);\n\t\t\t\t\t\tif (v !== undefined) {\n\t\t\t\t\t\t\tvalue[k] = v;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tdelete value[k];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn reviver.call(holder, key, value);\n\t\t}({'': result}, '')) : result;\n\t\t_parse_level--;\n\t\treturn result;\n\t}\n\tparser.finalError();\n\treturn undefined;\n}\n\n\nfunction this_value() {/*//_DEBUG_STRINGIFY&&console.log( \"this:\", this, \"valueof:\", this&&this.valueOf() );*/ \n\treturn this&&this.valueOf();\n}\n\n/**\n * Define a class to be used for serialization; the class allows emitting the class fields ahead of time, and just provide values later.\n * @param {string} name \n * @param {object} obj \n */\nJSOX.defineClass = function( name, obj ) {\n\tlet cls;\n\tlet denormKeys = Object.keys(obj);\n\tfor( let i = 1; i < denormKeys.length; i++ ) {\n\t\tlet a, b;\n\t\tif( ( a = denormKeys[i-1] ) > ( b = denormKeys[i] ) ) {\n\t\t\tdenormKeys[i-1] = b;\n\t\t\tdenormKeys[i] = a;\n\t\t\tif( i ) i-=2; // go back 2, this might need to go further pack.\n\t\t\telse i--; // only 1 to check.\n\t\t}\n\t}\n\t//console.log( \"normalized:\", denormKeys );\n\tcommonClasses.push( cls = { name : name\n\t\t   , tag:denormKeys.toString()\n\t\t   , proto : Object.getPrototypeOf(obj)\n\t\t   , fields : Object.keys(obj) } );\n\tfor(let n = 1; n < cls.fields.length; n++) {\n\t\tif( cls.fields[n] < cls.fields[n-1] ) {\n\t\t\tlet tmp = cls.fields[n-1];\n\t\t\tcls.fields[n-1] = cls.fields[n];\n\t\t\tcls.fields[n] = tmp;\n\t\t\tif( n > 1 )\n\t\t\t\tn-=2;\n\t\t}\n\t}\n\tif( cls.proto === Object.getPrototypeOf( {} ) ) cls.proto = null;\n}\n\n/**\n * define a class to be used for serialization\n * @param {string} named\n * @param {class} ptype\n * @param {(any)=>any} f\n */\nJSOX.toJSOX =\nJSOX.registerToJSOX = function( name, ptype, f ) {\n\t//console.log( \"SET OBJECT TYPE:\", ptype, ptype.prototype, Object.prototype, ptype.constructor );\n\tif( !ptype.prototype || ptype.prototype !== Object.prototype ) {\n\t\tif( toProtoTypes.get(ptype.prototype) ) throw new Error( \"Existing toJSOX has been registered for prototype\" );\n\t\t//_DEBUG_PARSING && console.log( \"PUSH PROTOTYPE\" );\n\t\ttoProtoTypes.set( ptype.prototype, { external:true, name:name||f.constructor.name, cb:f } );\n\t} else {\n\t\tlet key = Object.keys( ptype ).toString();\n\t\tif( toObjectTypes.get(key) ) throw new Error( \"Existing toJSOX has been registered for object type\" );\n\t\t//console.log( \"TEST SET OBJECT TYPE:\", key );\n\t\ttoObjectTypes.set( key, { external:true, name:name, cb:f } );\n\t}\n}\n/**\n * define a class to be used for deserialization\n * @param {string} prototypeName \n * @param {class} o \n * @param {(any)=>any} f \n */\nJSOX.fromJSOX = function( prototypeName, o, f ) {\n\tfunction privateProto() { }\n\t\tif( !o ) o = privateProto.prototype;\n\t\tif( fromProtoTypes.get(prototypeName) ) throw new Error( \"Existing fromJSOX has been registered for prototype\" );\n\t\tif( o && !(\"constructor\" in o )){\n\t\t\tthrow new Error( \"Please pass a prototype like thing...\");\n\t}\n\tfromProtoTypes.set( prototypeName, {protoCon: o.prototype.constructor, cb:f } );\n\n}\nJSOX.registerFromJSOX = function( prototypeName, o /*, f*/ ) {\n\tthrow new Error( \"deprecated; please adjust code to use fromJSOX:\" + prototypeName + o.toString() );\n}\nJSOX.addType = function( prototypeName, prototype, to, from ) {\n\tJSOX.toJSOX( prototypeName, prototype, to );\n\tJSOX.fromJSOX( prototypeName, prototype, from );\n}\n\nJSOX.registerToFrom = function( prototypeName, prototype/*, to, from*/ ) {\n\tthrow new Error( \"registerToFrom deprecated; please use addType:\" + prototypeName + prototype.toString() );\n}\n\n/**\n * Create a stringifier to convert objects to JSOX text.  Allows defining custom serialization for objects.\n * @returns {Stringifier}\n */\nJSOX.stringifier = function() {\n\tlet classes = [];\n\tlet useQuote = '\"';\n\n\tlet fieldMap = new WeakMap();\n\tconst path = [];\n\tlet encoding = [];\n\tconst localToProtoTypes = new WeakMap();\n\tconst localToObjectTypes = new Map();\n\tlet objectToJSOX = null;\n\tconst stringifying = []; // things that have been stringified through external toJSOX; allows second pass to skip this toJSOX pass and encode 'normally'\n\tlet ignoreNonEnumerable = false;\n\tfunction getIdentifier(s) {\n\t\n\t\tif( ( \"string\" === typeof s ) && s === '' ) return '\"\"';\n\t\tif( ( \"number\" === typeof s ) && !isNaN( s ) ) {\n\t\t\treturn [\"'\",s.toString(),\"'\"].join('');\n\t\t}\n\t\t// should check also for if any non ident in string...\n\t\tif( s.includes( \"\\u{FEFF}\" ) ) return (useQuote + JSOX.escape(s) +useQuote);\n\t\treturn ( ( s in keywords /* [ \"true\",\"false\",\"null\",\"NaN\",\"Infinity\",\"undefined\"].find( keyword=>keyword===s )*/\n\t\t\t|| /[0-9\\-]/.test(s[0])\n\t\t\t|| /[\\n\\r\\t #\\[\\]{}()<>\\~!+*/.:,\\-\"'`]/.test( s ) )?(useQuote + JSOX.escape(s) +useQuote):s )\n\t}\n\n\n\t/* init prototypes */\n\tif( !toProtoTypes.get( Object.prototype ) )\n\t{\n\t\ttoProtoTypes.set( Object.prototype, { external:false, name:Object.prototype.constructor.name, cb:null } );\n\t   \n\t\t// function https://stackoverflow.com/a/17415677/4619267\n\t\ttoProtoTypes.set( Date.prototype, { external:false,\n\t\t\tname : \"Date\",\n\t\t\tcb : function () {\n\t\t\t\t\tif( this.getTime()=== -62167219200000) \n\t\t\t\t\t{\n\t\t\t\t\t\treturn \"0000-01-01T00:00:00.000Z\";\n\t\t\t\t\t}\n\t\t\t\t\tlet tzo = -this.getTimezoneOffset(),\n\t\t\t\t\tdif = tzo >= 0 ? '+' : '-',\n\t\t\t\t\tpad = function(num) {\n\t\t\t\t\t\tlet norm = Math.floor(Math.abs(num));\n\t\t\t\t\t\treturn (norm < 10 ? '0' : '') + norm;\n\t\t\t\t\t},\n\t\t\t\t\tpad3 = function(num) {\n\t\t\t\t\t\tlet norm = Math.floor(Math.abs(num));\n\t\t\t\t\t\treturn (norm < 100 ? '0' : '') + (norm < 10 ? '0' : '') + norm;\n\t\t\t\t\t};\n\t\t\t\treturn [this.getFullYear() ,\n\t\t\t\t\t'-' , pad(this.getMonth() + 1) ,\n\t\t\t\t\t'-' , pad(this.getDate()) ,\n\t\t\t\t\t'T' , pad(this.getHours()) ,\n\t\t\t\t\t':' , pad(this.getMinutes()) ,\n\t\t\t\t\t':' , pad(this.getSeconds()) ,\n\t\t\t\t\t'.' + pad3(this.getMilliseconds()) +\n\t\t\t\t\tdif , pad(tzo / 60) ,\n\t\t\t\t\t':' , pad(tzo % 60)].join(\"\");\n\t\t\t} \n\t\t} );\n\t\ttoProtoTypes.set( DateNS.prototype, { external:false,\n\t\t\tname : \"DateNS\",\n\t\t\tcb : function () {\n\t\t\t\tlet tzo = -this.getTimezoneOffset(),\n\t\t\t\t\tdif = tzo >= 0 ? '+' : '-',\n\t\t\t\t\tpad = function(num) {\n\t\t\t\t\t\tlet norm = Math.floor(Math.abs(num));\n\t\t\t\t\t\treturn (norm < 10 ? '0' : '') + norm;\n\t\t\t\t\t},\n\t\t\t\t\tpad3 = function(num) {\n\t\t\t\t\t\tlet norm = Math.floor(Math.abs(num));\n\t\t\t\t\t\treturn (norm < 100 ? '0' : '') + (norm < 10 ? '0' : '') + norm;\n\t\t\t\t\t},\n\t\t\t\t\tpad6 = function(num) {\n\t\t\t\t\t\tlet norm = Math.floor(Math.abs(num));\n\t\t\t\t\t\treturn (norm < 100000 ? '0' : '') + (norm < 10000 ? '0' : '') + (norm < 1000 ? '0' : '') + (norm < 100 ? '0' : '') + (norm < 10 ? '0' : '') + norm;\n\t\t\t\t\t};\n\t\t\t\treturn [this.getFullYear() ,\n\t\t\t\t\t'-' , pad(this.getMonth() + 1) ,\n\t\t\t\t\t'-' , pad(this.getDate()) ,\n\t\t\t\t\t'T' , pad(this.getHours()) ,\n\t\t\t\t\t':' , pad(this.getMinutes()) ,\n\t\t\t\t\t':' , pad(this.getSeconds()) ,\n\t\t\t\t\t'.' + pad3(this.getMilliseconds()) + pad6(this.ns) +\n\t\t\t\t\tdif , pad(tzo / 60) ,\n\t\t\t\t\t':' , pad(tzo % 60)].join(\"\");\n\t\t\t} \n\t\t} );\n\t\ttoProtoTypes.set( Boolean.prototype, { external:false, name:\"Boolean\", cb:this_value  } );\n\t\ttoProtoTypes.set( Number.prototype, { external:false, name:\"Number\"\n\t\t    , cb:function(){ \n\t\t\t\tif( isNaN(this) )  return \"NaN\";\n\t\t\t\treturn (isFinite(this))\n\t\t\t\t\t? String(this)\n\t\t\t\t\t: (this<0)?\"-Infinity\":\"Infinity\";\n\t\t    }\n\t\t} );\n\t\ttoProtoTypes.set( String.prototype, { external:false\n\t\t    , name : \"String\"\n\t\t    , cb:function(){ return '\"' + JSOX.escape(this_value.apply(this)) + '\"' } } );\n\t\tif( typeof BigInt === \"function\" )\n\t\t\ttoProtoTypes.set( BigInt.prototype\n\t\t\t     , { external:false, name:\"BigInt\", cb:function() { return this + 'n' } } );\n\t   \n\t\ttoProtoTypes.set( ArrayBuffer.prototype, { external:true, name:\"ab\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this))+\"]\" }\n\t\t} );\n\t   \n\t\ttoProtoTypes.set( Uint8Array.prototype, { external:true, name:\"u8\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Uint8ClampedArray.prototype, { external:true, name:\"uc8\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Int8Array.prototype, { external:true, name:\"s8\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Uint16Array.prototype, { external:true, name:\"u16\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Int16Array.prototype, { external:true, name:\"s16\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Uint32Array.prototype, { external:true, name:\"u32\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Int32Array.prototype, { external:true, name:\"s32\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\t/*\n\t\tif( typeof Uint64Array != \"undefined\" )\n\t\t\ttoProtoTypes.set( Uint64Array.prototype, { external:true, name:\"u64\"\n\t\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t\t} );\n\t\tif( typeof Int64Array != \"undefined\" )\n\t\t\ttoProtoTypes.set( Int64Array.prototype, { external:true, name:\"s64\"\n\t\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t\t} );\n\t\t*/\n\t\ttoProtoTypes.set( Float32Array.prototype, { external:true, name:\"f32\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Float64Array.prototype, { external:true, name:\"f64\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t\ttoProtoTypes.set( Float64Array.prototype, { external:true, name:\"f64\"\n\t\t    , cb:function() { return \"[\"+getIdentifier(base64ArrayBuffer(this.buffer))+\"]\" }\n\t\t} );\n\t   \n\t\ttoProtoTypes.set( RegExp.prototype, mapToJSOX = { external:true, name:\"regex\"\n\t\t    , cb:function(o,stringifier){\n\t\t\t\treturn \"'\"+escape(this.source)+\"'\";\n\t\t\t}\n\t\t} );\n\t\tfromProtoTypes.set( \"regex\", { protoCon:RegExp, cb:function (field,val){\n\t\t\treturn new RegExp( this );\n\t\t} } );\n\n\t\ttoProtoTypes.set( Map.prototype, mapToJSOX = { external:true, name:\"map\"\n\t\t    , cb:null\n\t\t} );\n\t\tfromProtoTypes.set( \"map\", { protoCon:Map, cb:function (field,val){\n\t\t\tif( field ) {\n\t\t\t\tthis.set( field, val );\n\t\t\t\treturn undefined;\n\t\t\t}\n\t\t\treturn this;\n\t\t} } );\n\t   \n\t\ttoProtoTypes.set( Array.prototype, arrayToJSOX = { external:false, name:Array.prototype.constructor.name\n\t\t    , cb: null\t\t    \n\t\t} );\n\n\t}\n\n\tconst stringifier = {\n\t\tdefineClass(name,obj) { \n\t\t\tlet cls; \n\t\t\tlet denormKeys = Object.keys(obj);\n\t\t\tfor( let i = 1; i < denormKeys.length; i++ ) {\n\t\t\t\t// normalize class key order\n\t\t\t\tlet a, b;\n\t\t\t\tif( ( a = denormKeys[i-1] ) > ( b = denormKeys[i] ) ) {\n\t\t\t\t\tdenormKeys[i-1] = b;\n\t\t\t\t\tdenormKeys[i] = a;\n\t\t\t\t\tif( i ) i-=2; // go back 2, this might need to go further pack.\n\t\t\t\t\telse i--; // only 1 to check.\n\t\t\t\t}\n\t\t\t}\n\t\t\tclasses.push( cls = { name : name\n\t\t\t       , tag:denormKeys.toString()\n\t\t\t       , proto : Object.getPrototypeOf(obj)\n\t\t\t       , fields : Object.keys(obj) } );\n\n\t\t\tfor(let n = 1; n < cls.fields.length; n++) {\n\t\t\t\tif( cls.fields[n] < cls.fields[n-1] ) {\n\t\t\t\t\tlet tmp = cls.fields[n-1];\n\t\t\t\t\tcls.fields[n-1] = cls.fields[n];\n\t\t\t\t\tcls.fields[n] = tmp;\n\t\t\t\t\tif( n > 1 )\n\t\t\t\t\t\tn-=2;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif( cls.proto === Object.getPrototypeOf( {} ) ) cls.proto = null;\n\t\t},\n\t\tsetDefaultObjectToJSOX( cb ) { objectToJSOX = cb },\n\t\tisEncoding(o) {\n\t\t\t//console.log( \"is object encoding?\", encoding.length, o, encoding );\n\t\t\treturn !!encoding.find( (eo,i)=>eo===o && i < (encoding.length-1) )\n\t\t},\n\t\tencodeObject(o) {\n\t\t\tif( objectToJSOX ) \n\t\t\t\treturn objectToJSOX.apply(o, [this]);\n\t\t\treturn o;\n\t\t},\n\t\tstringify(o,r,s) { return stringify(o,r,s) },\n\t\tsetQuote(q) { useQuote = q; },\n\t\tregisterToJSOX(n,p,f) { return this.toJSOX( n,p,f ) },\n\t\ttoJSOX( name, ptype, f ) {\n\t\t\tif( ptype.prototype && ptype.prototype !== Object.prototype ) {\n\t\t\t\tif( localToProtoTypes.get(ptype.prototype) ) throw new Error( \"Existing toJSOX has been registered for prototype\" );\n\t\t\t\tlocalToProtoTypes.set( ptype.prototype, { external:true, name:name||f.constructor.name, cb:f } );\n\t\t\t} else {\n\t\t\t\tlet key = Object.keys( ptype ).toString();\n\t\t\t\tif( localToObjectTypes.get(key) ) throw new Error( \"Existing toJSOX has been registered for object type\" );\n\t\t\t\tlocalToObjectTypes.set( key, { external:true, name:name, cb:f } );\n\t\t\t}\n\t\t},\n\t\tget ignoreNonEnumerable() { return ignoreNonEnumerable; },\n\t\tset ignoreNonEnumerable(val) { ignoreNonEnumerable = val; },\n\t}\n\treturn stringifier;\n\n\t/**\n\t * get a reference to a previously seen object\n\t * @param {any} here \n\t * @returns reference to existing object, or undefined if not found.\n\t */\n\tfunction getReference( here ) {\n\t\tif( here === null ) return undefined;\n\t\tlet field = fieldMap.get( here );\n\t\t//_DEBUG_STRINGIFY && console.log( \"path:\", _JSON.stringify(path), field );\n\t\tif( !field ) {\n\t\t\tfieldMap.set( here, _JSON.stringify(path) );\n\t\t\treturn undefined;\n\t\t}\n\t\treturn \"ref\"+field;\n\t}\n\n\n\t/**\n\t * find the prototype definition for a class\n\t * @param {object} o \n\t * @param {map} useK \n\t * @returns object\n\t */\n\tfunction matchObject(o,useK) {\n\t\tlet k;\n\t\tlet cls;\n\t\tlet prt = Object.getPrototypeOf(o);\n\t\tcls = classes.find( cls=>{\n\t\t\tif( cls.proto && cls.proto === prt ) return true;\n\t\t} );\n\t\tif( cls ) return cls;\n\n\t\tif( classes.length || commonClasses.length ) {\n\t\t\tif( useK )  {\n\t\t\t\tuseK = useK.map( v=>{ if( typeof v === \"string\" ) return v; else return undefined; } );\n\t\t\t\tk = useK.toString();\n\t\t\t} else {\n\t\t\t\tlet denormKeys = Object.keys(o);\n\t\t\t\tfor( let i = 1; i < denormKeys.length; i++ ) {\n\t\t\t\t\tlet a, b;\n\t\t\t\t\tif( ( a = denormKeys[i-1] ) > ( b = denormKeys[i] ) ) {\n\t\t\t\t\t\tdenormKeys[i-1] = b;\n\t\t\t\t\t\tdenormKeys[i] = a;\n\t\t\t\t\t\tif( i ) i-=2; // go back 2, this might need to go further pack.\n\t\t\t\t\t\telse i--; // only 1 to check.\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tk = denormKeys.toString();\n\t\t\t}\n\t\t\tcls = classes.find( cls=>{\n\t\t\t\tif( cls.tag === k ) return true;\n\t\t\t} );\n\t\t\tif( !cls )\n\t\t\t\tcls = commonClasses.find( cls=>{\n\t\t\t\t\tif( cls.tag === k ) return true;\n\t\t\t\t} );\n\t\t}\n\t\treturn cls;\n\t}\n\n\t/**\n\t * Serialize an object to JSOX text.\n\t * @param {any} object \n\t * @param {(key:string,value:any)=>string} replacer \n\t * @param {string|number} space \n\t * @returns \n\t */\n\tfunction stringify( object, replacer, space ) {\n\t\tif( object === undefined ) return \"undefined\";\n\t\tif( object === null ) return;\n\t\tlet gap;\n\t\tlet indent;\n\t\tlet rep;\n\n\t\tlet i;\n\t\tconst spaceType = typeof space;\n\t\tconst repType = typeof replacer;\n\t\tgap = \"\";\n\t\tindent = \"\";\n\n\t\t// If the space parameter is a number, make an indent string containing that\n\t\t// many spaces.\n\n\t\tif (spaceType === \"number\") {\n\t\t\tfor (i = 0; i < space; i += 1) {\n\t\t\t\tindent += \" \";\n\t\t\t}\n\n\t\t// If the space parameter is a string, it will be used as the indent string.\n\t\t} else if (spaceType === \"string\") {\n\t\t\tindent = space;\n\t\t}\n\n\t\t// If there is a replacer, it must be a function or an array.\n\t\t// Otherwise, throw an error.\n\n\t\trep = replacer;\n\t\tif( replacer && repType !== \"function\"\n\t\t    && ( repType !== \"object\"\n\t\t       || typeof replacer.length !== \"number\"\n\t\t   )) {\n\t\t\tthrow new Error(\"JSOX.stringify\");\n\t\t}\n\n\t\tpath.length = 0;\n\t\tfieldMap = new WeakMap();\n\n\t\tconst finalResult = str( \"\", {\"\":object} );\n\t\tcommonClasses.length = 0;\n\t\treturn finalResult;\n\n\t\t// from https://github.com/douglascrockford/JSON-js/blob/master/json2.js#L181\n\t\tfunction str(key, holder) {\n\t\t\tvar mind = gap;\n\t\t\tconst doArrayToJSOX_ = arrayToJSOX.cb;\n\t\t\tconst mapToObject_ = mapToJSOX.cb;\t\t \n\t\t\tarrayToJSOX.cb = doArrayToJSOX;\n\t\t\tmapToJSOX.cb = mapToObject;\n\t\t\tconst v = str_(key,holder);\n\t\t\tarrayToJSOX.cb = doArrayToJSOX_;\n\t\t\tmapToJSOX.cb = mapToObject_;\n\t\t\treturn v;\n\n\t\t\tfunction doArrayToJSOX() {\n\t\t\t\tlet v;\n\t\t\t\tlet partial = [];\n\t\t\t\tlet thisNodeNameIndex = path.length;\n\n\t\t\t\t// The value is an array. Stringify every element. Use null as a placeholder\n\t\t\t\t// for non-JSOX values.\n\t\t\t\n\t\t\t\tfor (let i = 0; i < this.length; i += 1) {\n\t\t\t\t\tpath[thisNodeNameIndex] = i;\n\t\t\t\t\tpartial[i] = str(i, this) || \"null\";\n\t\t\t\t}\n\t\t\t\tpath.length = thisNodeNameIndex;\n\t\t\t\t//console.log( \"remove encoding item\", thisNodeNameIndex, encoding.length);\n\t\t\t\tencoding.length = thisNodeNameIndex;\n\t\t\t\n\t\t\t\t// Join all of the elements together, separated with commas, and wrap them in\n\t\t\t\t// brackets.\n\t\t\t\tv = ( partial.length === 0\n\t\t\t\t\t? \"[]\"\n\t\t\t\t\t: gap\n\t\t\t\t\t\t? [\n\t\t\t\t\t\t\t\"[\\n\"\n\t\t\t\t\t\t\t, gap\n\t\t\t\t\t\t\t, partial.join(\",\\n\" + gap)\n\t\t\t\t\t\t\t, \"\\n\"\n\t\t\t\t\t\t\t, mind\n\t\t\t\t\t\t\t, \"]\"\n\t\t\t\t\t\t].join(\"\")\n\t\t\t\t\t\t: \"[\" + partial.join(\",\") + \"]\" );\n\t\t\t\treturn v;\n\t\t\t} \n\t\t\tfunction mapToObject(){\n\t\t\t\t//_DEBUG_PARSING_DETAILS && console.log( \"---------- NEW MAP -------------\" );\n\t\t\t\tlet tmp = {tmp:null};\n\t\t\t\tlet out = '{'\n\t\t\t\tlet first = true;\n\t\t\t\t//console.log( \"CONVERT:\", map);\n\t\t\t\tfor (let [key, value] of this) {\n\t\t\t\t\t//console.log( \"er...\", key, value )\n\t\t\t\t\ttmp.tmp = value;\n\t\t\t\t\tlet thisNodeNameIndex = path.length;\n\t\t\t\t\tpath[thisNodeNameIndex] = key;\n\t\t\t\t\t\t\t\n\t\t\t\t\tout += (first?\"\":\",\") + getIdentifier(key) +':' + str(\"tmp\", tmp);\n\t\t\t\t\tpath.length = thisNodeNameIndex;\n\t\t\t\t\tfirst = false;\n\t\t\t\t}\n\t\t\t\tout += '}';\n\t\t\t\t//console.log( \"out is:\", out );\n\t\t\t\treturn out;\n\t\t\t}\n\n\t\t// Produce a string from holder[key].\n\t\tfunction str_(key, holder) {\n\n\t\t\tlet i;          // The loop counter.\n\t\t\tlet k;          // The member key.\n\t\t\tlet v;          // The member value.\n\t\t\tlet length;\n\t\t\tlet partialClass;\n\t\t\tlet partial;\n\t\t\tlet thisNodeNameIndex = path.length;\n\t\t\tlet isValue = true;\n\t\t\tlet value = holder[key];\n\t\t\tlet isObject = (typeof value === \"object\");\n\t\t\tlet c;\n\n\t\t\tif( isObject && ( value !== null ) ) {\n\t\t\t\tif( objectToJSOX ){\n\t\t\t\t\tif( !stringifying.find( val=>val===value ) ) {\n\t\t\t\t\t\tstringifying.push( value );\n\t\t\t\t\t\tencoding[thisNodeNameIndex] = value;\n\t\t\t\t\t\tisValue = false;\n\t\t\t\t\t\tvalue = objectToJSOX.apply(value, [stringifier]);\n\t\t\t\t\t\t//console.log( \"Converted by object lookup -it's now a different type\"\n\t\t\t\t\t\t//\t, protoConverter, objectConverter );\n\t\t\t\t\t\tisObject = ( typeof value === \"object\" );\n\t\t\t\t\t\tstringifying.pop();\n\t\t\t\t\t\tencoding.length = thisNodeNameIndex;\n\t\t\t\t\t\tisObject = (typeof value === \"object\");\n\t\t\t\t\t}\n\t\t\t\t\t//console.log( \"Value convereted to:\", key, value );\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst objType = (value !== undefined && value !== null) && Object.getPrototypeOf( value );\n\t\t\t\n\t\t\tlet protoConverter = objType\n\t\t\t\t&& ( localToProtoTypes.get( objType ) \n\t\t\t\t|| toProtoTypes.get( objType ) \n\t\t\t\t|| null )\n\t\t\tlet objectConverter = !protoConverter && (value !== undefined && value !== null) \n\t\t\t\t&& ( localToObjectTypes.get( Object.keys( value ).toString() ) \n\t\t\t\t|| toObjectTypes.get( Object.keys( value ).toString() ) \n\t\t\t\t|| null )\n\n\t\t\t// If we were called with a replacer function, then call the replacer to\n\t\t\t// obtain a replacement value.\n\n\t\t\tif (typeof rep === \"function\") {\n\t\t\t\tisValue = false;\n\t\t\t\tvalue = rep.call(holder, key, value);\n\t\t\t}\n\t\t\t\t//console.log( \"PROTOTYPE:\", Object.getPrototypeOf( value ) )\n\t\t\t\t//console.log( \"PROTOTYPE:\", toProtoTypes.get(Object.getPrototypeOf( value )) )\n\t\t\t\t//if( protoConverter )\n\t\t\t//_DEBUG_STRINGIFY && console.log( \"TEST()\", value, protoConverter, objectConverter );\n\n\t\t\tlet toJSOX = ( protoConverter && protoConverter.cb ) \n\t\t\t          || ( objectConverter && objectConverter.cb );\n\t\t\t// If the value has a toJSOX method, call it to obtain a replacement value.\n\t\t\t//_DEBUG_STRINGIFY && console.log( \"type:\", typeof value, protoConverter, !!toJSOX, path );\n\n\t\t\tif( value !== undefined\n\t\t\t    && value !== null\n\t\t\t\t&& typeof value === \"object\"\n\t\t\t    && typeof toJSOX === \"function\"\n\t\t\t) {\n\t\t\t\tif( !stringifying.find( val=>val===value ) ) {\n\t\t\t\t\tif( typeof value === \"object\" ) {\n\t\t\t\t\t\tv = getReference( value );\n\t\t\t\t\t\tif( v )\treturn v;\n\t\t\t\t\t}\n\n\t\t\t\t\tstringifying.push( value );\n\t\t\t\t\tencoding[thisNodeNameIndex] = value;\n\t\t\t\t\tvalue = toJSOX.call(value, stringifier);\n\t\t\t\t\tisValue = false;\n\t\t\t\t\tstringifying.pop();\n\t\t\t\t\tif( protoConverter && protoConverter.name ) {\n\t\t\t\t\t\t// stringify may return a unquoted string\n\t\t\t\t\t\t// which needs an extra space betwen its tag and value.\n\t\t\t\t\t\tif( \"string\" === typeof value \n\t\t\t\t\t\t\t&& value[0] !== '-'\n\t\t\t\t\t\t\t&& (value[0] < '0' || value[0] > '9' )\n\t\t\t\t\t\t\t&& value[0] !== '\"'\n\t\t\t\t\t\t\t&& value[0] !== '\\'' \n\t\t\t\t\t\t\t&& value[0] !== '`' \n\t\t\t\t\t\t\t&& value[0] !== '[' \n\t\t\t\t\t\t\t&& value[0] !== '{' \n\t\t\t\t\t\t\t){\n\t\t\t\t\t\t\tvalue = ' ' + value;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t//console.log( \"Value converted:\", value );\n\t\t\t\t\tencoding.length = thisNodeNameIndex;\n\t\t\t\t} else {\n\t\t\t\t\tv = getReference( value );\n\t\t\t\t}\n\t\t} else \n\t\t\t\tif( typeof value === \"object\" ) {\n\t\t\t\t\tv = getReference( value );\n\t\t\t\t\tif( v ) return v;\n\t\t\t\t}\n\n\t\t\t// What happens next depends on the value's type.\n\t\t\tswitch (typeof value) {\n\t\t\tcase \"bigint\":\n\t\t\t\treturn value + 'n';\n\t\t\tcase \"string\":\n\t\t\t\t{\n\t\t\t\t\t//console.log( `Value was converted before?  [${value}]`);\n\t\t\t\t\tvalue = isValue?getIdentifier(value):value;\n\t\t\t\t\tlet c = '';\n\t\t\t\t\tif( key===\"\" )\n\t\t\t\t\t\tc = classes.map( cls=> cls.name+\"{\"+cls.fields.join(\",\")+\"}\" ).join(gap?\"\\n\":\"\")+\n\t\t\t\t\t\t    commonClasses.map( cls=> cls.name+\"{\"+cls.fields.join(\",\")+\"}\" ).join(gap?\"\\n\":\"\")\n\t\t\t\t\t\t\t\t+(gap?\"\\n\":\"\");\n\t\t\t\t\tif( protoConverter && protoConverter.external ) \n\t\t\t\t\t\treturn c + protoConverter.name + value;\n\t\t\t\t\tif( objectConverter && objectConverter.external ) \n\t\t\t\t\t\treturn c + objectConverter.name + value;\n\t\t\t\t\treturn c + value;//useQuote+JSOX.escape( value )+useQuote;\n\t\t\t\t}\n\t\t\tcase \"number\":\n\t\t\tcase \"boolean\":\n\t\t\tcase \"null\":\n\n\t\t\t\t// If the value is a boolean or null, convert it to a string. Note:\n\t\t\t\t// typeof null does not produce \"null\". The case is included here in\n\t\t\t\t// the remote chance that this gets fixed someday.\n\n\t\t\t\treturn String(value);\n\n\t\t\t\t// If the type is \"object\", we might be dealing with an object or an array or\n\t\t\t\t// null.\n\n\t\t\tcase \"object\":\n\t\t\t\t//_DEBUG_STRINGIFY && console.log( \"ENTERINT OBJECT EMISSION WITH:\", v );\n\t\t\t\tif( v ) return \"ref\"+v;\n\n\t\t\t\t// Due to a specification blunder in ECMAScript, typeof null is \"object\",\n\t\t\t\t// so watch out for that case.\n\t\t\t\tif (!value) {\n\t\t\t\t\treturn \"null\";\n\t\t\t\t}\n\n\t\t\t\t// Make an array to hold the partial results of stringifying this object value.\n\t\t\t\tgap += indent;\n\t\t\t\tpartialClass = null;\n\t\t\t\tpartial = [];\n\n\t\t\t\t// If the replacer is an array, use it to select the members to be stringified.\n\t\t\t\tif (rep && typeof rep === \"object\") {\n\t\t\t\t\tlength = rep.length;\n\t\t\t\t\tpartialClass = matchObject( value, rep );\n\t\t\t\t\tfor (i = 0; i < length; i += 1) {\n\t\t\t\t\t\tif (typeof rep[i] === \"string\") {\n\t\t\t\t\t\t\tk = rep[i];\n\t\t\t\t\t\t\tpath[thisNodeNameIndex] = k;\n\t\t\t\t\t\t\tv = str(k, value);\n\n\t\t\t\t\t\t\tif (v !== undefined ) {\n\t\t\t\t\t\t\t\tif( partialClass ) {\n\t\t\t\t\t\t\t\t\tpartial.push(v);\n\t\t\t\t\t\t\t\t} else\n\t\t\t\t\t\t\t\t\tpartial.push( getIdentifier(k) \n\t\t\t\t\t\t\t\t\t+ (\n\t\t\t\t\t\t\t\t\t\t(gap)\n\t\t\t\t\t\t\t\t\t\t\t? \": \"\n\t\t\t\t\t\t\t\t\t\t\t: \":\"\n\t\t\t\t\t\t\t\t\t) + v);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tpath.splice( thisNodeNameIndex, 1 );\n\t\t\t\t} else {\n\n\t\t\t\t\t// Otherwise, iterate through all of the keys in the object.\n\t\t\t\t\tpartialClass = matchObject( value );\n\t\t\t\t\tlet keys = [];\n\t\t\t\t\tfor (k in value) {\n\t\t\t\t\t\tif( ignoreNonEnumerable )\n\t\t\t\t\t\t\tif( !Object.prototype.propertyIsEnumerable.call( value, k ) ){\n\t\t\t\t\t\t\t\t//_DEBUG_STRINGIFY && console.log( \"skipping non-enuerable?\", k );\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(value, k)) {\n\t\t\t\t\t\t\tlet n;\n\t\t\t\t\t\t\tfor( n = 0; n < keys.length; n++ ) \n\t\t\t\t\t\t\t\tif( keys[n] > k ) {\t\n\t\t\t\t\t\t\t\t\tkeys.splice(n,0,k );\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif( n == keys.length )\n\t\t\t\t\t\t\t\tkeys.push(k);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tfor(let n = 0; n < keys.length; n++) {\n\t\t\t\t\t\tk = keys[n];\n\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(value, k)) {\n\t\t\t\t\t\t\tpath[thisNodeNameIndex] = k;\n\t\t\t\t\t\t\tv = str(k, value);\n\n\t\t\t\t\t\t\tif (v !== undefined ) {\n\t\t\t\t\t\t\t\tif( partialClass ) {\n\t\t\t\t\t\t\t\t\tpartial.push(v);\n\t\t\t\t\t\t\t\t} else\n\t\t\t\t\t\t\t\t\tpartial.push(getIdentifier(k) + (\n\t\t\t\t\t\t\t\t\t\t(gap)\n\t\t\t\t\t\t\t\t\t\t\t? \": \"\n\t\t\t\t\t\t\t\t\t\t\t: \":\"\n\t\t\t\t\t\t\t\t\t) + v);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tpath.splice( thisNodeNameIndex, 1 );\n\t\t\t\t}\n\n\t\t\t\t// Join all of the member texts together, separated with commas,\n\t\t\t\t// and wrap them in braces.\n\t\t\t\t//_DEBUG_STRINGIFY && console.log( \"partial:\", partial )\n\n\t\t\t\t//let c;\n\t\t\t\tif( key===\"\" )\n\t\t\t\t\tc = ( classes.map( cls=> cls.name+\"{\"+cls.fields.join(\",\")+\"}\" ).join(gap?\"\\n\":\"\")\n\t\t\t\t\t\t|| commonClasses.map( cls=> cls.name+\"{\"+cls.fields.join(\",\")+\"}\" ).join(gap?\"\\n\":\"\"))+(gap?\"\\n\":\"\");\n\t\t\t\telse\n\t\t\t\t\tc = '';\n\n\t\t\t\tif( protoConverter && protoConverter.external ) \n\t\t\t\t\tc = c + getIdentifier(protoConverter.name);\n\n\t\t\t\t//_DEBUG_STRINGIFY && console.log( \"PREFIX FOR THIS FIELD:\", c );\n\t\t\t\tlet ident = null;\n\t\t\t\tif( partialClass )\n\t\t\t\t\tident = getIdentifier( partialClass.name ) ;\n\t\t\t\tv = c +\n\t\t\t\t\t( partial.length === 0\n\t\t\t\t\t? \"{}\"\n\t\t\t\t\t: gap\n\t\t\t\t\t\t\t? (partialClass?ident:\"\")+\"{\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"}\"\n\t\t\t\t\t\t\t: (partialClass?ident:\"\")+\"{\" + partial.join(\",\") + \"}\"\n\t\t\t\t\t);\n\n\t\t\t\tgap = mind;\n\t\t\t\treturn v;\n\t\t\t}\n\t\t}\n\t}\n\n\t}\n}\n\n\t// Converts an ArrayBuffer directly to base64, without any intermediate 'convert to string then\n\t// use window.btoa' step. According to my tests, this appears to be a faster approach:\n\t// http://jsperf.com/encoding-xhr-image-data/5\n\t// doesn't have to be reversable....\n\tconst encodings = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789$_'\n\tconst decodings = { '~':-1\n\t\t,'=':-1\n\t\t,'$':62\n\t\t,'_':63\n\t\t,'+':62\n\t\t,'-':62\n\t\t,'.':62\n\t\t,'/':63\n\t\t,',':63\n\t};\n\t\n\tfor( let x = 0; x < encodings.length; x++ ) {\n\t\tdecodings[encodings[x]] = x;\n\t}\n\tObject.freeze( decodings );\n\t\n\tfunction base64ArrayBuffer(arrayBuffer) {\n\t\tlet base64    = ''\n\t\n\t\tlet bytes         = new Uint8Array(arrayBuffer)\n\t\tlet byteLength    = bytes.byteLength\n\t\tlet byteRemainder = byteLength % 3\n\t\tlet mainLength    = byteLength - byteRemainder\n\t\n\t\tlet a, b, c, d\n\t\tlet chunk\n\t\t//throw \"who's using this?\"\n\t\t//console.log( \"buffer..\", arrayBuffer )\n\t\t// Main loop deals with bytes in chunks of 3\n\t\tfor (let i = 0; i < mainLength; i = i + 3) {\n\t\t\t// Combine the three bytes into a single integer\n\t\t\tchunk = (bytes[i] << 16) | (bytes[i + 1] << 8) | bytes[i + 2]\n\n\t\t\t// Use bitmasks to extract 6-bit segments from the triplet\n\t\t\ta = (chunk & 16515072) >> 18 // 16515072 = (2^6 - 1) << 18\n\t\t\tb = (chunk & 258048)   >> 12 // 258048   = (2^6 - 1) << 12\n\t\t\tc = (chunk & 4032)     >>  6 // 4032     = (2^6 - 1) << 6\n\t\t\td = chunk & 63               // 63       = 2^6 - 1\n\t\n\t\t\t// Convert the raw binary segments to the appropriate ASCII encoding\n\t\t\tbase64 += encodings[a] + encodings[b] + encodings[c] + encodings[d]\n\t\t}\n\t\n\t// Deal with the remaining bytes and padding\n\t\tif (byteRemainder == 1) {\n\t\t\tchunk = bytes[mainLength]\n\t\t\ta = (chunk & 252) >> 2 // 252 = (2^6 - 1) << 2\n\t\t\t// Set the 4 least significant bits to zero\n\t\t\tb = (chunk & 3)   << 4 // 3   = 2^2 - 1\n\t\t\tbase64 += encodings[a] + encodings[b] + '=='\n\t\t} else if (byteRemainder == 2) {\n\t\t\tchunk = (bytes[mainLength] << 8) | bytes[mainLength + 1]\n\t\t\ta = (chunk & 64512) >> 10 // 64512 = (2^6 - 1) << 10\n\t\t\tb = (chunk & 1008)  >>  4 // 1008  = (2^6 - 1) << 4\n\t\t\t// Set the 2 least significant bits to zero\n\t\t\tc = (chunk & 15)    <<  2 // 15    = 2^4 - 1\n\t\t\tbase64 += encodings[a] + encodings[b] + encodings[c] + '='\n\t\t}\n\t\t//console.log( \"dup?\", base64)\n\t\treturn base64\n\t}\n\t\n\t\n\tfunction DecodeBase64( buf ) {\t\n\t\tlet outsize;\n\t\tif( buf.length % 4 == 1 )\n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 3;\n\t\telse if( buf.length % 4 == 2 )\n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 2;\n\t\telse if( buf.length % 4 == 3 )\n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 1;\n\t\telse if( decodings[buf[buf.length - 3]] == -1 )\n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 3;\n\t\telse if( decodings[buf[buf.length - 2]] == -1 ) \n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 2;\n\t\telse if( decodings[buf[buf.length - 1]] == -1 ) \n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3) - 1;\n\t\telse\n\t\t\toutsize = ((((buf.length + 3) / 4)|0) * 3);\n\t\tlet ab = new ArrayBuffer( outsize );\n\t\tlet out = new Uint8Array(ab);\n\n\t\tlet n;\n\t\tlet l = (buf.length+3)>>2;\n\t\tfor( n = 0; n < l; n++ ) {\n\t\t\tlet index0 = decodings[buf[n*4]];\n\t\t\tlet index1 = (n*4+1)<buf.length?decodings[buf[n*4+1]]:-1;\n\t\t\tlet index2 = (index1>=0) && (n*4+2)<buf.length?decodings[buf[n*4+2]]:-1 || 0;\n\t\t\tlet index3 = (index2>=0) && (n*4+3)<buf.length?decodings[buf[n*4+3]]:-1 || 0;\n\t\t\tif( index1 >= 0 )\n\t\t\t\tout[n*3+0] = (( index0 ) << 2 | ( index1 ) >> 4);\n\t\t\tif( index2 >= 0 )\n\t\t\t\tout[n*3+1] = (( index1 ) << 4 | ( ( ( index2 ) >> 2 ) & 0x0f ));\n\t\t\tif( index3 >= 0 )\n\t\t\t\tout[n*3+2] = (( index2 ) << 6 | ( ( index3 ) & 0x3F ));\n\t\t}\n\n\t\treturn ab;\n\t}\n\t\n/**\n * @param {unknown} object \n * @param {(this: unknown, key: string, value: unknown)} [replacer] \n * @param {string | number} [space] \n * @returns {string}\n */\t\nJSOX.stringify = function( object, replacer, space ) {\n\tlet stringifier = JSOX.stringifier();\n\treturn stringifier.stringify( object, replacer, space );\n}\n\nconst nonIdent = \n[ [ 0,256,[ 0xffd9ff,0xff6aff,0x1fc00,0x380000,0x0,0xfffff8,0xffffff,0x7fffff] ]\n].map( row=>{ return{ firstChar : row[0], lastChar: row[1], bits : row[2] }; } );\n\n//} privateizeEverything();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/jsox@1.2.121/node_modules/jsox/lib/jsox.mjs\n");

/***/ })

};
;