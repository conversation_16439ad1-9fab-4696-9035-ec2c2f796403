# =================================================================
# Authentication Configuration (Clerk)
# =================================================================
# IMPORTANT: This template supports Clerk's keyless mode!
# You can start using the app immediately without any configuration.
# When you're ready to claim your application, simply click the Clerk
# popup at the bottom of the screen to get your API keys.

# Required: Clerk API Keys (Leave empty for keyless mode)

NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_d2VsY29tZWQtZmVsaW5lLTgyLmNsZXJrLmFjY291bnRzLmRldiQ
CLERK_SECRET_KEY=sk_test_bMupAGrYalCrR1x5L7xfk0SDDEdcyQILbFTOzCq44A


# Authentication Redirect URLs
# These control where users are directed after authentication actions

NEXT_PUBLIC_CLERK_SIGN_IN_URL="/auth/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/auth/sign-up"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/dashboard"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/dashboard"

# =================================================================
# API Configuration
# =================================================================
# Payload CMS Backend URL
NEXT_PUBLIC_API_URL=http://localhost:8002

# Service Authentication (for production)
# PAYLOAD_SERVICE_TOKEN=your-service-token-here
# PAYLOAD_SERVICE_USER_EMAIL=<EMAIL>


# =================================================================
# Error Tracking Configuration (Sentry)
# =================================================================
# To set up Sentry error tracking:
# 1. Create an account at https://sentry.io
# 2. Create a new project for Next.js
# 3. Follow the setup instructions below

# Step 1: Sentry DSN (Required)
# Found at: Settings > Projects > [Your Project] > Client Keys (DSN)

NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/****************

# Step 2: Organization & Project Details
# Found at: Settings > Organization > General Settings

NEXT_PUBLIC_SENTRY_ORG=jym-93
NEXT_PUBLIC_SENTRY_PROJECT=nord-coast


# Step 3: Sentry Auth Token

# Sentry can automatically provide readable stack traces for errors using source maps, requiring a Sentry auth token.
# More info: https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/#step-4-add-readable-stack-traces-with-source-maps-optional

SENTRY_AUTH_TOKEN=sntrys_eyJpYXQiOjE3NTIwMTI1NTcuMzI5MzQ3LCJ1cmwiO