[{"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\importMap.js": "3", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx": "4", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql\\route.ts": "6", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql-playground\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\[...slug]\\route.ts": "8", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\layout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\[id]\\route.ts": "11", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments-direct\\route.ts": "12", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\route.ts": "13", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\[id]\\route.ts": "14", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\generate-from-appointment\\route.ts": "15", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\route.ts": "16", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\[id]\\route.ts": "17", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\db-test\\route.ts": "18", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\apply-to-bill\\route.ts": "19", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\route.ts": "20", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\[id]\\route.ts": "21", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\convert-to-patient\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\route.ts": "23", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\[id]\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reset-db\\route.ts": "27", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\test\\route.ts": "28", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\route.ts": "29", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\[id]\\route.ts": "30", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\route.ts": "31", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\[id]\\route.ts": "32", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\my-route\\route.ts": "33", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Appointments.ts": "34", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\BillItems.ts": "35", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Bills.ts": "36", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Deposits.ts": "37", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Media.ts": "38", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Patients.ts": "39", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Payments.ts": "40", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Treatments.ts": "41", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Users.ts": "42", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\auth-sync.ts": "43", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\clerk-auth-strategy.ts": "44", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\payload-auth-middleware.ts": "45", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload-types.ts": "46", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload.config.ts": "47"}, {"size": 400, "mtime": 1752009631000, "results": "48", "hashOfConfig": "49"}, {"size": 1847, "mtime": 1752048389803, "results": "50", "hashOfConfig": "49"}, {"size": 5898, "mtime": 1752016400380, "results": "51", "hashOfConfig": "52"}, {"size": 731, "mtime": 1752009631000, "results": "53", "hashOfConfig": "49"}, {"size": 715, "mtime": 1752009631000, "results": "54", "hashOfConfig": "49"}, {"size": 315, "mtime": 1752009631000, "results": "55", "hashOfConfig": "49"}, {"size": 305, "mtime": 1752009631000, "results": "56", "hashOfConfig": "49"}, {"size": 550, "mtime": 1752009631000, "results": "57", "hashOfConfig": "49"}, {"size": 810, "mtime": 1752009631000, "results": "58", "hashOfConfig": "49"}, {"size": 2212, "mtime": 1752032650690, "results": "59", "hashOfConfig": "49"}, {"size": 3171, "mtime": 1752037068700, "results": "60", "hashOfConfig": "49"}, {"size": 1346, "mtime": 1752018809145, "results": "61", "hashOfConfig": "49"}, {"size": 3305, "mtime": 1752048156005, "results": "62", "hashOfConfig": "49"}, {"size": 3783, "mtime": 1752042973170, "results": "63", "hashOfConfig": "49"}, {"size": 4548, "mtime": 1752049047117, "results": "64", "hashOfConfig": "49"}, {"size": 3707, "mtime": 1752048176586, "results": "65", "hashOfConfig": "49"}, {"size": 3921, "mtime": 1752042932094, "results": "66", "hashOfConfig": "49"}, {"size": 777, "mtime": 1752018791568, "results": "67", "hashOfConfig": "49"}, {"size": 4732, "mtime": 1752049787004, "results": "68", "hashOfConfig": "49"}, {"size": 3266, "mtime": 1752048189770, "results": "69", "hashOfConfig": "49"}, {"size": 3767, "mtime": 1752043056882, "results": "70", "hashOfConfig": "49"}, {"size": 2308, "mtime": 1752049995084, "results": "71", "hashOfConfig": "49"}, {"size": 2459, "mtime": 1752032674926, "results": "72", "hashOfConfig": "49"}, {"size": 3000, "mtime": 1752037001759, "results": "73", "hashOfConfig": "49"}, {"size": 3659, "mtime": 1752048214156, "results": "74", "hashOfConfig": "49"}, {"size": 3559, "mtime": 1752043015028, "results": "75", "hashOfConfig": "49"}, {"size": 1029, "mtime": 1752018919310, "results": "76", "hashOfConfig": "49"}, {"size": 231, "mtime": 1752018622704, "results": "77", "hashOfConfig": "49"}, {"size": 2022, "mtime": 1752032698666, "results": "78", "hashOfConfig": "49"}, {"size": 3036, "mtime": 1752037033875, "results": "79", "hashOfConfig": "49"}, {"size": 1952, "mtime": 1752032614878, "results": "80", "hashOfConfig": "49"}, {"size": 2946, "mtime": 1752036952764, "results": "81", "hashOfConfig": "49"}, {"size": 289, "mtime": 1752048092114, "results": "82", "hashOfConfig": "49"}, {"size": 6433, "mtime": 1752042577243, "results": "83", "hashOfConfig": "49"}, {"size": 4016, "mtime": 1752044074913, "results": "84", "hashOfConfig": "49"}, {"size": 6894, "mtime": 1752044196002, "results": "85", "hashOfConfig": "49"}, {"size": 6445, "mtime": 1752044247760, "results": "86", "hashOfConfig": "49"}, {"size": 255, "mtime": 1752009631000, "results": "87", "hashOfConfig": "49"}, {"size": 6672, "mtime": 1752042473052, "results": "88", "hashOfConfig": "49"}, {"size": 5652, "mtime": 1752044295176, "results": "89", "hashOfConfig": "49"}, {"size": 1401, "mtime": 1752025370327, "results": "90", "hashOfConfig": "49"}, {"size": 2394, "mtime": 1752025408079, "results": "91", "hashOfConfig": "49"}, {"size": 2856, "mtime": 1752044417674, "results": "92", "hashOfConfig": "49"}, {"size": 951, "mtime": 1752044472492, "results": "93", "hashOfConfig": "49"}, {"size": 2700, "mtime": 1752050933716, "results": "94", "hashOfConfig": "49"}, {"size": 18559, "mtime": 1752044130835, "results": "95", "hashOfConfig": "49"}, {"size": 1461, "mtime": 1752042863174, "results": "96", "hashOfConfig": "49"}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e1u0sl", {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pn1qvw", {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\importMap.js", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql-playground\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\[...slug]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments-direct\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\generate-from-appointment\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\db-test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\apply-to-bill\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\convert-to-patient\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reset-db\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\my-route\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Appointments.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\BillItems.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Bills.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Deposits.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Media.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Patients.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Payments.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Treatments.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Users.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\auth-sync.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\clerk-auth-strategy.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\payload-auth-middleware.ts", ["238", "239", "240", "241"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload-types.ts", [], ["242", "243", "244", "245", "246"], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload.config.ts", [], [], {"ruleId": "247", "severity": 1, "message": "248", "line": 78, "column": 12, "nodeType": "249", "messageId": "250", "endLine": 78, "endColumn": 15, "suggestions": "251"}, {"ruleId": "247", "severity": 1, "message": "248", "line": 85, "column": 12, "nodeType": "249", "messageId": "250", "endLine": 85, "endColumn": 15, "suggestions": "252"}, {"ruleId": "247", "severity": 1, "message": "248", "line": 92, "column": 12, "nodeType": "249", "messageId": "250", "endLine": 92, "endColumn": 15, "suggestions": "253"}, {"ruleId": "247", "severity": 1, "message": "248", "line": 99, "column": 12, "nodeType": "249", "messageId": "250", "endLine": 99, "endColumn": 15, "suggestions": "254"}, {"ruleId": "255", "severity": 1, "message": "256", "line": 68, "column": 11, "nodeType": "257", "messageId": "258", "endLine": 68, "endColumn": 13, "suggestions": "259", "suppressions": "260"}, {"ruleId": "255", "severity": 1, "message": "256", "line": 83, "column": 21, "nodeType": "257", "messageId": "258", "endLine": 83, "endColumn": 23, "suggestions": "261", "suppressions": "262"}, {"ruleId": "255", "severity": 1, "message": "256", "line": 101, "column": 12, "nodeType": "257", "messageId": "258", "endLine": 101, "endColumn": 14, "suggestions": "263", "suppressions": "264"}, {"ruleId": "255", "severity": 1, "message": "256", "line": 102, "column": 18, "nodeType": "257", "messageId": "258", "endLine": 102, "endColumn": 20, "suggestions": "265", "suppressions": "266"}, {"ruleId": "255", "severity": 1, "message": "267", "line": 825, "column": 20, "nodeType": "268", "messageId": "269", "endLine": 825, "endColumn": 34, "suggestions": "270", "suppressions": "271"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["272", "273"], ["274", "275"], ["276", "277"], ["278", "279"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["280", "281"], ["282"], ["283", "284"], ["285"], ["286", "287"], ["288"], ["289", "290"], ["291"], "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["292"], ["293"], {"messageId": "294", "fix": "295", "desc": "296"}, {"messageId": "297", "fix": "298", "desc": "299"}, {"messageId": "294", "fix": "300", "desc": "296"}, {"messageId": "297", "fix": "301", "desc": "299"}, {"messageId": "294", "fix": "302", "desc": "296"}, {"messageId": "297", "fix": "303", "desc": "299"}, {"messageId": "294", "fix": "304", "desc": "296"}, {"messageId": "297", "fix": "305", "desc": "299"}, {"messageId": "306", "data": "307", "fix": "308", "desc": "309"}, {"messageId": "306", "data": "310", "fix": "311", "desc": "312"}, {"kind": "313", "justification": "314"}, {"messageId": "306", "data": "315", "fix": "316", "desc": "309"}, {"messageId": "306", "data": "317", "fix": "318", "desc": "312"}, {"kind": "313", "justification": "314"}, {"messageId": "306", "data": "319", "fix": "320", "desc": "309"}, {"messageId": "306", "data": "321", "fix": "322", "desc": "312"}, {"kind": "313", "justification": "314"}, {"messageId": "306", "data": "323", "fix": "324", "desc": "309"}, {"messageId": "306", "data": "325", "fix": "326", "desc": "312"}, {"kind": "313", "justification": "314"}, {"messageId": "327", "fix": "328", "desc": "329"}, {"kind": "313", "justification": "314"}, "suggestUnknown", {"range": "330", "text": "331"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "332", "text": "333"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "334", "text": "331"}, {"range": "335", "text": "333"}, {"range": "336", "text": "331"}, {"range": "337", "text": "333"}, {"range": "338", "text": "331"}, {"range": "339", "text": "333"}, "replaceEmptyObjectType", {"replacement": "340"}, {"range": "341", "text": "340"}, "Replace `{}` with `object`.", {"replacement": "331"}, {"range": "342", "text": "331"}, "Replace `{}` with `unknown`.", "directive", "", {"replacement": "340"}, {"range": "343", "text": "340"}, {"replacement": "331"}, {"range": "344", "text": "331"}, {"replacement": "340"}, {"range": "345", "text": "340"}, {"replacement": "331"}, {"range": "346", "text": "331"}, {"replacement": "340"}, {"range": "347", "text": "340"}, {"replacement": "331"}, {"range": "348", "text": "331"}, "replaceEmptyInterfaceWithSuper", {"range": "349", "text": "350"}, "Replace empty interface with a type alias.", [2231, 2234], "unknown", [2231, 2234], "never", [2358, 2361], [2358, 2361], [2485, 2488], [2485, 2488], [2612, 2615], [2612, 2615], "object", [1506, 1508], [1506, 1508], [1904, 1906], [1904, 1906], [2813, 2815], [2813, 2815], [2834, 2836], [2834, 2836], [17423, 17465], "type GeneratedTypes = Config"]