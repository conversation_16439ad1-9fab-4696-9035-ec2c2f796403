// Comprehensive error handling system for billing operations
// Provides consistent error handling, logging, and user feedback

import { toast } from 'sonner';

// Error types and interfaces
export interface BillingError {
  code: string;
  message: string;
  userMessage: string;
  severity: 'error' | 'warning' | 'info';
  details?: any;
  timestamp: Date;
  context?: string;
}

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  context?: string;
  fallbackMessage?: string;
}

// Error code mappings with user-friendly messages
const ERROR_MESSAGES: Record<string, { message: string; severity: 'error' | 'warning' | 'info' }> = {
  // Authentication errors
  AUTH_REQUIRED: { message: '请先登录以继续操作', severity: 'error' },
  AUTH_SERVICE_ERROR: { message: '认证服务暂时不可用，请稍后重试', severity: 'error' },
  CLERK_USER_FETCH_ERROR: { message: '获取用户信息失败，请重新登录', severity: 'error' },
  USER_EMAIL_MISSING: { message: '用户邮箱信息缺失，请联系管理员', severity: 'error' },

  // Validation errors
  VALIDATION_ERROR: { message: '输入数据验证失败，请检查表单内容', severity: 'error' },
  INVALID_JSON: { message: '数据格式错误，请刷新页面重试', severity: 'error' },
  INVALID_LIMIT: { message: '分页参数错误', severity: 'warning' },
  INVALID_PAGE: { message: '页码参数错误', severity: 'warning' },
  SUBTOTAL_MISMATCH: { message: '账单金额计算错误，请重新检查', severity: 'error' },

  // Business logic errors
  INVALID_PAYMENT_AMOUNT: { message: '支付金额无效', severity: 'error' },
  BILL_NOT_FOUND: { message: '账单不存在或已被删除', severity: 'error' },
  PAYMENT_METHOD_ERROR: { message: '支付方式验证失败', severity: 'error' },
  INSUFFICIENT_PERMISSIONS: { message: '权限不足，无法执行此操作', severity: 'error' },

  // Backend/Service errors
  BACKEND_ERROR: { message: '后端服务错误', severity: 'error' },
  BACKEND_SERVICE_ERROR: { message: '后端服务暂时不可用，请稍后重试', severity: 'error' },
  DATABASE_ERROR: { message: '数据库操作失败，请稍后重试', severity: 'error' },
  NETWORK_ERROR: { message: '网络连接错误，请检查网络连接', severity: 'error' },

  // Generic errors
  INTERNAL_ERROR: { message: '系统内部错误，请稍后重试', severity: 'error' },
  UNKNOWN_ERROR: { message: '发生未知错误，请联系技术支持', severity: 'error' },
};

// Error handler class
export class BillingErrorHandler {
  private static instance: BillingErrorHandler;
  private errorLog: BillingError[] = [];

  private constructor() {}

  static getInstance(): BillingErrorHandler {
    if (!BillingErrorHandler.instance) {
      BillingErrorHandler.instance = new BillingErrorHandler();
    }
    return BillingErrorHandler.instance;
  }

  /**
   * Handle API response errors
   */
  handleAPIError(error: any, options: ErrorHandlerOptions = {}): BillingError {
    const {
      showToast = true,
      logError = true,
      context = 'API',
      fallbackMessage = '操作失败，请稍后重试'
    } = options;

    let billingError: BillingError;

    if (error?.code && ERROR_MESSAGES[error.code]) {
      const errorInfo = ERROR_MESSAGES[error.code];
      billingError = {
        code: error.code,
        message: error.message || errorInfo.message,
        userMessage: error.message || errorInfo.message,
        severity: errorInfo.severity,
        details: error.details,
        timestamp: new Date(),
        context
      };
    } else {
      // Handle unknown errors
      billingError = {
        code: 'UNKNOWN_ERROR',
        message: error?.message || 'Unknown error occurred',
        userMessage: fallbackMessage,
        severity: 'error',
        details: error,
        timestamp: new Date(),
        context
      };
    }

    if (logError) {
      this.logError(billingError);
    }

    if (showToast) {
      this.showErrorToast(billingError);
    }

    return billingError;
  }

  /**
   * Handle network/fetch errors
   */
  handleNetworkError(error: any, options: ErrorHandlerOptions = {}): BillingError {
    const networkError: BillingError = {
      code: 'NETWORK_ERROR',
      message: error?.message || 'Network request failed',
      userMessage: '网络连接错误，请检查网络连接后重试',
      severity: 'error',
      details: error,
      timestamp: new Date(),
      context: options.context || 'Network'
    };

    if (options.logError !== false) {
      this.logError(networkError);
    }

    if (options.showToast !== false) {
      this.showErrorToast(networkError);
    }

    return networkError;
  }

  /**
   * Handle validation errors
   */
  handleValidationError(validationErrors: any[], options: ErrorHandlerOptions = {}): BillingError {
    const firstError = validationErrors[0];
    const validationError: BillingError = {
      code: 'VALIDATION_ERROR',
      message: 'Validation failed',
      userMessage: firstError?.message || '表单验证失败，请检查输入内容',
      severity: 'error',
      details: validationErrors,
      timestamp: new Date(),
      context: options.context || 'Validation'
    };

    if (options.logError !== false) {
      this.logError(validationError);
    }

    if (options.showToast !== false) {
      this.showErrorToast(validationError);
    }

    return validationError;
  }

  /**
   * Show success message
   */
  showSuccess(message: string, description?: string): void {
    toast.success(message, {
      description,
      duration: 3000,
    });
  }

  /**
   * Show warning message
   */
  showWarning(message: string, description?: string): void {
    toast.warning(message, {
      description,
      duration: 4000,
    });
  }

  /**
   * Show info message
   */
  showInfo(message: string, description?: string): void {
    toast.info(message, {
      description,
      duration: 3000,
    });
  }

  /**
   * Log error to console and internal log
   */
  private logError(error: BillingError): void {
    console.error(`[${error.context}] ${error.code}: ${error.message}`, {
      userMessage: error.userMessage,
      details: error.details,
      timestamp: error.timestamp
    });

    // Add to internal error log (keep last 100 errors)
    this.errorLog.push(error);
    if (this.errorLog.length > 100) {
      this.errorLog.shift();
    }
  }

  /**
   * Show error toast notification
   */
  private showErrorToast(error: BillingError): void {
    const toastOptions = {
      duration: error.severity === 'error' ? 5000 : 4000,
    };

    switch (error.severity) {
      case 'error':
        toast.error(error.userMessage, toastOptions);
        break;
      case 'warning':
        toast.warning(error.userMessage, toastOptions);
        break;
      case 'info':
        toast.info(error.userMessage, toastOptions);
        break;
    }
  }

  /**
   * Get error log for debugging
   */
  getErrorLog(): BillingError[] {
    return [...this.errorLog];
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }
}

// Convenience functions for common error handling patterns
export const billingErrorHandler = BillingErrorHandler.getInstance();

export const handleAPIError = (error: any, options?: ErrorHandlerOptions) => 
  billingErrorHandler.handleAPIError(error, options);

export const handleNetworkError = (error: any, options?: ErrorHandlerOptions) => 
  billingErrorHandler.handleNetworkError(error, options);

export const handleValidationError = (errors: any[], options?: ErrorHandlerOptions) => 
  billingErrorHandler.handleValidationError(errors, options);

export const showSuccess = (message: string, description?: string) => 
  billingErrorHandler.showSuccess(message, description);

export const showWarning = (message: string, description?: string) => 
  billingErrorHandler.showWarning(message, description);

export const showInfo = (message: string, description?: string) => 
  billingErrorHandler.showInfo(message, description);

// Error boundary helper for React components
export const withErrorHandling = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  context?: string
): T => {
  return (async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      handleAPIError(error, { context });
      throw error;
    }
  }) as T;
};

// Retry utility with exponential backoff
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  context?: string
): Promise<T> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        handleAPIError(error, { 
          context: context || 'Retry',
          fallbackMessage: `操作失败，已重试${maxRetries}次`
        });
        throw error;
      }

      // Exponential backoff delay
      const delay = baseDelay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};
