/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/(frontend)/layout"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjimmy%5C%5CDesktop%5C%5Cnord-coast%5C%5Cbackend%5C%5Csrc%5C%5Capp%5C%5C(frontend)%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjimmy%5C%5CDesktop%5C%5Cnord-coast%5C%5Cbackend%5C%5Csrc%5C%5Capp%5C%5C(frontend)%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(frontend)/styles.css */ \"(app-pages-browser)/./src/app/(frontend)/styles.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuMl9AYmFiZWwrY29yZUA3LjJfOTE1OGZhNzg2YTgyYmVhYTFmMDEyZTMzNmJlN2VhMTAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNqaW1teSU1QyU1Q0Rlc2t0b3AlNUMlNUNub3JkLWNvYXN0JTVDJTVDYmFja2VuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Qyhmcm9udGVuZCklNUMlNUNzdHlsZXMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0xBQXFIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxqaW1teVxcXFxEZXNrdG9wXFxcXG5vcmQtY29hc3RcXFxcYmFja2VuZFxcXFxzcmNcXFxcYXBwXFxcXChmcm9udGVuZClcXFxcc3R5bGVzLmNzc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjimmy%5C%5CDesktop%5C%5Cnord-coast%5C%5Cbackend%5C%5Csrc%5C%5Capp%5C%5C(frontend)%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(frontend)/styles.css":
/*!***************************************!*\
  !*** ./src/app/(frontend)/styles.css ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ae2dabebe58e\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGZyb250ZW5kKS9zdHlsZXMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqaW1teVxcRGVza3RvcFxcbm9yZC1jb2FzdFxcYmFja2VuZFxcc3JjXFxhcHBcXChmcm9udGVuZClcXHN0eWxlcy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhZTJkYWJlYmU1OGVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(frontend)/styles.css\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjimmy%5C%5CDesktop%5C%5Cnord-coast%5C%5Cbackend%5C%5Csrc%5C%5Capp%5C%5C(frontend)%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);