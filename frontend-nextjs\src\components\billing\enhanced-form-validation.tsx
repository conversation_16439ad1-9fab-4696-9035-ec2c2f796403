'use client';

// Enhanced form validation component with real-time validation and error handling
// Provides comprehensive validation feedback for billing forms

import React, { useState, useEffect, useCallback } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  IconAlertCircle, 
  IconAlertTriangle, 
  IconInfoCircle,
  IconCheck,
  IconX
} from '@tabler/icons-react';
import { 
  ValidationResult, 
  ValidationError, 
  ValidationWarning,
  FormValidator 
} from '@/lib/validation/validation-utils';
import { handleValidationError } from '@/lib/billing-error-handler';

interface EnhancedFormValidationProps {
  validator: FormValidator;
  formData: any;
  onValidationChange?: (isValid: boolean, errors: ValidationError[]) => void;
  showWarnings?: boolean;
  showRealTimeValidation?: boolean;
  className?: string;
}

export function EnhancedFormValidation({
  validator,
  formData,
  onValidationChange,
  showWarnings = true,
  showRealTimeValidation = true,
  className = ''
}: EnhancedFormValidationProps) {
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
    errors: [],
    warnings: []
  });
  const [isValidating, setIsValidating] = useState(false);

  // Debounced validation function
  const validateForm = useCallback(
    async (data: any) => {
      if (!showRealTimeValidation) return;
      
      setIsValidating(true);
      
      try {
        const result = validator.validateForm(data);
        setValidationResult(result);
        
        if (onValidationChange) {
          onValidationChange(result.isValid, result.errors);
        }
      } catch (error) {
        console.error('Form validation error:', error);
        handleValidationError([{
          field: 'form',
          message: '表单验证过程中发生错误',
          code: 'VALIDATION_ERROR'
        }], { context: 'Form Validation' });
      } finally {
        setIsValidating(false);
      }
    },
    [validator, onValidationChange, showRealTimeValidation]
  );

  // Effect to validate form when data changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      validateForm(formData);
    }, 300); // Debounce validation

    return () => clearTimeout(timeoutId);
  }, [formData, validateForm]);

  // Manual validation trigger
  const triggerValidation = useCallback(() => {
    validateForm(formData);
  }, [formData, validateForm]);

  // Group errors by field
  const errorsByField = validationResult.errors.reduce((acc, error) => {
    if (!acc[error.field]) {
      acc[error.field] = [];
    }
    acc[error.field].push(error);
    return acc;
  }, {} as Record<string, ValidationError[]>);

  // Group warnings by field
  const warningsByField = validationResult.warnings.reduce((acc, warning) => {
    if (!acc[warning.field]) {
      acc[warning.field] = [];
    }
    acc[warning.field].push(warning);
    return acc;
  }, {} as Record<string, ValidationWarning[]>);

  if (!showRealTimeValidation && validationResult.isValid && validationResult.warnings.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Validation Status Summary */}
      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-2">
          {isValidating ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-600">验证中...</span>
            </>
          ) : validationResult.isValid ? (
            <>
              <IconCheck className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-600">表单验证通过</span>
            </>
          ) : (
            <>
              <IconX className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-600">
                发现 {validationResult.errors.length} 个错误
              </span>
            </>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {validationResult.warnings.length > 0 && showWarnings && (
            <Badge variant="outline" className="text-yellow-600 border-yellow-300">
              {validationResult.warnings.length} 个警告
            </Badge>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={triggerValidation}
            disabled={isValidating}
          >
            重新验证
          </Button>
        </div>
      </div>

      {/* Error Messages */}
      {validationResult.errors.length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <IconAlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium text-red-800">请修复以下错误：</p>
              <ul className="space-y-1">
                {Object.entries(errorsByField).map(([field, errors]) => (
                  <li key={field} className="text-sm">
                    <span className="font-medium text-red-700">
                      {getFieldDisplayName(field)}:
                    </span>
                    <ul className="ml-4 mt-1 space-y-1">
                      {errors.map((error, index) => (
                        <li key={index} className="text-red-600">
                          • {error.message}
                        </li>
                      ))}
                    </ul>
                  </li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Warning Messages */}
      {validationResult.warnings.length > 0 && showWarnings && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <IconAlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium text-yellow-800">建议注意：</p>
              <ul className="space-y-1">
                {Object.entries(warningsByField).map(([field, warnings]) => (
                  <li key={field} className="text-sm">
                    <span className="font-medium text-yellow-700">
                      {getFieldDisplayName(field)}:
                    </span>
                    <ul className="ml-4 mt-1 space-y-1">
                      {warnings.map((warning, index) => (
                        <li key={index} className="text-yellow-600">
                          • {warning.message}
                          {warning.suggestion && (
                            <span className="text-yellow-500 ml-2">
                              ({warning.suggestion})
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                  </li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Validation Tips */}
      {validationResult.errors.length === 0 && validationResult.warnings.length === 0 && (
        <Alert className="border-blue-200 bg-blue-50">
          <IconInfoCircle className="h-4 w-4 text-blue-600" />
          <AlertDescription className="text-blue-800">
            <div className="space-y-1">
              <p className="font-medium">表单验证提示：</p>
              <ul className="text-sm space-y-1">
                <li>• 所有必填字段都已正确填写</li>
                <li>• 金额格式正确，最多支持2位小数</li>
                <li>• 日期格式有效且在合理范围内</li>
                <li>• 账单项目数量和单价都在允许范围内</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

// Helper function to get user-friendly field names
function getFieldDisplayName(field: string): string {
  const fieldNames: Record<string, string> = {
    patient: '患者',
    appointment: '预约',
    treatment: '治疗项目',
    billType: '账单类型',
    description: '账单描述',
    notes: '备注',
    dueDate: '到期日期',
    discountAmount: '折扣金额',
    taxAmount: '税费金额',
    items: '账单项目',
    'items.itemName': '项目名称',
    'items.quantity': '数量',
    'items.unitPrice': '单价',
    'items.discountRate': '折扣率',
    amount: '支付金额',
    paymentMethod: '支付方式',
    transactionId: '交易ID',
    fullName: '姓名',
    phone: '手机号码',
    email: '邮箱地址',
    medicalNotes: '医疗备注',
    status: '状态',
    search: '搜索关键词',
    dateFrom: '开始日期',
    dateTo: '结束日期',
    amountMin: '最小金额',
    amountMax: '最大金额',
  };

  return fieldNames[field] || field;
}

// Field-level validation component for individual form fields
interface FieldValidationProps {
  fieldName: string;
  value: any;
  validator: FormValidator;
  formData: any;
  showIcon?: boolean;
  className?: string;
}

export function FieldValidation({
  fieldName,
  value,
  validator,
  formData,
  showIcon = true,
  className = ''
}: FieldValidationProps) {
  const [fieldErrors, setFieldErrors] = useState<ValidationError[]>([]);
  const [isValidating, setIsValidating] = useState(false);

  useEffect(() => {
    const timeoutId = setTimeout(async () => {
      setIsValidating(true);
      
      try {
        const result = validator.validateForm(formData);
        const errors = result.errors.filter(error => 
          error.field === fieldName || error.field.startsWith(`${fieldName}.`)
        );
        setFieldErrors(errors);
      } catch (error) {
        console.error('Field validation error:', error);
      } finally {
        setIsValidating(false);
      }
    }, 200);

    return () => clearTimeout(timeoutId);
  }, [value, formData, fieldName, validator]);

  if (fieldErrors.length === 0 && !isValidating) {
    return showIcon ? (
      <IconCheck className="h-4 w-4 text-green-500" />
    ) : null;
  }

  return (
    <div className={`space-y-1 ${className}`}>
      {isValidating && (
        <div className="flex items-center space-x-1">
          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
          <span className="text-xs text-gray-500">验证中...</span>
        </div>
      )}
      
      {fieldErrors.map((error, index) => (
        <div key={index} className="flex items-center space-x-1 text-red-600">
          {showIcon && <IconAlertCircle className="h-3 w-3" />}
          <span className="text-xs">{error.message}</span>
        </div>
      ))}
    </div>
  );
}
