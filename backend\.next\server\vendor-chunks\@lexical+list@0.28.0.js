"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+list@0.28.0";
exports.ids = ["vendor-chunks/@lexical+list@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+list@0.28.0/node_modules/@lexical/list/LexicalList.dev.mjs":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+list@0.28.0/node_modules/@lexical/list/LexicalList.dev.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $createListItemNode: () => (/* binding */ $createListItemNode),\n/* harmony export */   $createListNode: () => (/* binding */ $createListNode),\n/* harmony export */   $getListDepth: () => (/* binding */ $getListDepth),\n/* harmony export */   $handleListInsertParagraph: () => (/* binding */ $handleListInsertParagraph),\n/* harmony export */   $insertList: () => (/* binding */ $insertList),\n/* harmony export */   $isListItemNode: () => (/* binding */ $isListItemNode),\n/* harmony export */   $isListNode: () => (/* binding */ $isListNode),\n/* harmony export */   $removeList: () => (/* binding */ $removeList),\n/* harmony export */   INSERT_CHECK_LIST_COMMAND: () => (/* binding */ INSERT_CHECK_LIST_COMMAND),\n/* harmony export */   INSERT_ORDERED_LIST_COMMAND: () => (/* binding */ INSERT_ORDERED_LIST_COMMAND),\n/* harmony export */   INSERT_UNORDERED_LIST_COMMAND: () => (/* binding */ INSERT_UNORDERED_LIST_COMMAND),\n/* harmony export */   ListItemNode: () => (/* binding */ ListItemNode),\n/* harmony export */   ListNode: () => (/* binding */ ListNode),\n/* harmony export */   REMOVE_LIST_COMMAND: () => (/* binding */ REMOVE_LIST_COMMAND),\n/* harmony export */   insertList: () => (/* binding */ insertList),\n/* harmony export */   registerList: () => (/* binding */ registerList),\n/* harmony export */   removeList: () => (/* binding */ removeList)\n/* harmony export */ });\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(rsc)/./node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/selection */ \"(rsc)/./node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Checks the depth of listNode from the root node.\n * @param listNode - The ListNode to be checked.\n * @returns The depth of the ListNode.\n */\nfunction $getListDepth(listNode) {\n  let depth = 1;\n  let parent = listNode.getParent();\n  while (parent != null) {\n    if ($isListItemNode(parent)) {\n      const parentList = parent.getParent();\n      if ($isListNode(parentList)) {\n        depth++;\n        parent = parentList.getParent();\n        continue;\n      }\n      {\n        formatDevErrorMessage(`A ListItemNode must have a ListNode for a parent.`);\n      }\n    }\n    return depth;\n  }\n  return depth;\n}\n\n/**\n * Finds the nearest ancestral ListNode and returns it, throws an invariant if listItem is not a ListItemNode.\n * @param listItem - The node to be checked.\n * @returns The ListNode found.\n */\nfunction $getTopListNode(listItem) {\n  let list = listItem.getParent();\n  if (!$isListNode(list)) {\n    {\n      formatDevErrorMessage(`A ListItemNode must have a ListNode for a parent.`);\n    }\n  }\n  let parent = list;\n  while (parent !== null) {\n    parent = parent.getParent();\n    if ($isListNode(parent)) {\n      list = parent;\n    }\n  }\n  return list;\n}\n\n/**\n * A recursive Depth-First Search (Postorder Traversal) that finds all of a node's children\n * that are of type ListItemNode and returns them in an array.\n * @param node - The ListNode to start the search.\n * @returns An array containing all nodes of type ListItemNode found.\n */\n// This should probably be $getAllChildrenOfType\nfunction $getAllListItems(node) {\n  let listItemNodes = [];\n  const listChildren = node.getChildren().filter($isListItemNode);\n  for (let i = 0; i < listChildren.length; i++) {\n    const listItemNode = listChildren[i];\n    const firstChild = listItemNode.getFirstChild();\n    if ($isListNode(firstChild)) {\n      listItemNodes = listItemNodes.concat($getAllListItems(firstChild));\n    } else {\n      listItemNodes.push(listItemNode);\n    }\n  }\n  return listItemNodes;\n}\n\n/**\n * Checks to see if the passed node is a ListItemNode and has a ListNode as a child.\n * @param node - The node to be checked.\n * @returns true if the node is a ListItemNode and has a ListNode child, false otherwise.\n */\nfunction isNestedListNode(node) {\n  return $isListItemNode(node) && $isListNode(node.getFirstChild());\n}\n\n/**\n * Takes a deeply nested ListNode or ListItemNode and traverses up the branch to delete the first\n * ancestral ListNode (which could be the root ListNode) or ListItemNode with siblings, essentially\n * bringing the deeply nested node up the branch once. Would remove sublist if it has siblings.\n * Should not break ListItem -> List -> ListItem chain as empty List/ItemNodes should be removed on .remove().\n * @param sublist - The nested ListNode or ListItemNode to be brought up the branch.\n */\nfunction $removeHighestEmptyListParent(sublist) {\n  // Nodes may be repeatedly indented, to create deeply nested lists that each\n  // contain just one bullet.\n  // Our goal is to remove these (empty) deeply nested lists. The easiest\n  // way to do that is crawl back up the tree until we find a node that has siblings\n  // (e.g. is actually part of the list contents) and delete that, or delete\n  // the root of the list (if no list nodes have siblings.)\n  let emptyListPtr = sublist;\n  while (emptyListPtr.getNextSibling() == null && emptyListPtr.getPreviousSibling() == null) {\n    const parent = emptyListPtr.getParent();\n    if (parent == null || !($isListItemNode(parent) || $isListNode(parent))) {\n      break;\n    }\n    emptyListPtr = parent;\n  }\n  emptyListPtr.remove();\n}\n\n/**\n * Wraps a node into a ListItemNode.\n * @param node - The node to be wrapped into a ListItemNode\n * @returns The ListItemNode which the passed node is wrapped in.\n */\nfunction $wrapInListItem(node) {\n  const listItemWrapper = $createListItemNode();\n  return listItemWrapper.append(node);\n}\n\nfunction $isSelectingEmptyListItem(anchorNode, nodes) {\n  return $isListItemNode(anchorNode) && (nodes.length === 0 || nodes.length === 1 && anchorNode.is(nodes[0]) && anchorNode.getChildrenSize() === 0);\n}\n\n/**\n * Inserts a new ListNode. If the selection's anchor node is an empty ListItemNode and is a child of\n * the root/shadow root, it will replace the ListItemNode with a ListNode and the old ListItemNode.\n * Otherwise it will replace its parent with a new ListNode and re-insert the ListItemNode and any previous children.\n * If the selection's anchor node is not an empty ListItemNode, it will add a new ListNode or merge an existing ListNode,\n * unless the the node is a leaf node, in which case it will attempt to find a ListNode up the branch and replace it with\n * a new ListNode, or create a new ListNode at the nearest root/shadow root.\n * @param listType - The type of list, \"number\" | \"bullet\" | \"check\".\n */\nfunction $insertList(listType) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (selection !== null) {\n    let nodes = selection.getNodes();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const anchorAndFocus = selection.getStartEndPoints();\n      if (!(anchorAndFocus !== null)) {\n        formatDevErrorMessage(`insertList: anchor should be defined`);\n      }\n      const [anchor] = anchorAndFocus;\n      const anchorNode = anchor.getNode();\n      const anchorNodeParent = anchorNode.getParent();\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(anchorNode)) {\n        const firstChild = anchorNode.getFirstChild();\n        if (firstChild) {\n          nodes = firstChild.selectStart().getNodes();\n        } else {\n          const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n          anchorNode.append(paragraph);\n          nodes = paragraph.select().getNodes();\n        }\n      } else if ($isSelectingEmptyListItem(anchorNode, nodes)) {\n        const list = $createListNode(listType);\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(anchorNodeParent)) {\n          anchorNode.replace(list);\n          const listItem = $createListItemNode();\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(anchorNode)) {\n            listItem.setFormat(anchorNode.getFormatType());\n            listItem.setIndent(anchorNode.getIndent());\n          }\n          list.append(listItem);\n        } else if ($isListItemNode(anchorNode)) {\n          const parent = anchorNode.getParentOrThrow();\n          append(list, parent.getChildren());\n          parent.replace(list);\n        }\n        return;\n      }\n    }\n    const handled = new Set();\n    for (let i = 0; i < nodes.length; i++) {\n      const node = nodes[i];\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && node.isEmpty() && !$isListItemNode(node) && !handled.has(node.getKey())) {\n        $createListOrMerge(node, listType);\n        continue;\n      }\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isLeafNode)(node)) {\n        let parent = node.getParent();\n        while (parent != null) {\n          const parentKey = parent.getKey();\n          if ($isListNode(parent)) {\n            if (!handled.has(parentKey)) {\n              const newListNode = $createListNode(listType);\n              append(newListNode, parent.getChildren());\n              parent.replace(newListNode);\n              handled.add(parentKey);\n            }\n            break;\n          } else {\n            const nextParent = parent.getParent();\n            if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(nextParent) && !handled.has(parentKey)) {\n              handled.add(parentKey);\n              $createListOrMerge(parent, listType);\n              break;\n            }\n            parent = nextParent;\n          }\n        }\n      }\n    }\n  }\n}\nfunction append(node, nodesToAppend) {\n  node.splice(node.getChildrenSize(), 0, nodesToAppend);\n}\nfunction $createListOrMerge(node, listType) {\n  if ($isListNode(node)) {\n    return node;\n  }\n  const previousSibling = node.getPreviousSibling();\n  const nextSibling = node.getNextSibling();\n  const listItem = $createListItemNode();\n  append(listItem, node.getChildren());\n  let targetList;\n  if ($isListNode(previousSibling) && listType === previousSibling.getListType()) {\n    previousSibling.append(listItem);\n    // if the same type of list is on both sides, merge them.\n    if ($isListNode(nextSibling) && listType === nextSibling.getListType()) {\n      append(previousSibling, nextSibling.getChildren());\n      nextSibling.remove();\n    }\n    targetList = previousSibling;\n  } else if ($isListNode(nextSibling) && listType === nextSibling.getListType()) {\n    nextSibling.getFirstChildOrThrow().insertBefore(listItem);\n    targetList = nextSibling;\n  } else {\n    const list = $createListNode(listType);\n    list.append(listItem);\n    node.replace(list);\n    targetList = list;\n  }\n  // listItem needs to be attached to root prior to setting indent\n  listItem.setFormat(node.getFormatType());\n  listItem.setIndent(node.getIndent());\n  node.remove();\n  return targetList;\n}\n\n/**\n * A recursive function that goes through each list and their children, including nested lists,\n * appending list2 children after list1 children and updating ListItemNode values.\n * @param list1 - The first list to be merged.\n * @param list2 - The second list to be merged.\n */\nfunction mergeLists(list1, list2) {\n  const listItem1 = list1.getLastChild();\n  const listItem2 = list2.getFirstChild();\n  if (listItem1 && listItem2 && isNestedListNode(listItem1) && isNestedListNode(listItem2)) {\n    mergeLists(listItem1.getFirstChild(), listItem2.getFirstChild());\n    listItem2.remove();\n  }\n  const toMerge = list2.getChildren();\n  if (toMerge.length > 0) {\n    list1.append(...toMerge);\n  }\n  list2.remove();\n}\n\n/**\n * Searches for the nearest ancestral ListNode and removes it. If selection is an empty ListItemNode\n * it will remove the whole list, including the ListItemNode. For each ListItemNode in the ListNode,\n * removeList will also generate new ParagraphNodes in the removed ListNode's place. Any child node\n * inside a ListItemNode will be appended to the new ParagraphNodes.\n */\nfunction $removeList() {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    const listNodes = new Set();\n    const nodes = selection.getNodes();\n    const anchorNode = selection.anchor.getNode();\n    if ($isSelectingEmptyListItem(anchorNode, nodes)) {\n      listNodes.add($getTopListNode(anchorNode));\n    } else {\n      for (let i = 0; i < nodes.length; i++) {\n        const node = nodes[i];\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isLeafNode)(node)) {\n          const listItemNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$getNearestNodeOfType)(node, ListItemNode);\n          if (listItemNode != null) {\n            listNodes.add($getTopListNode(listItemNode));\n          }\n        }\n      }\n    }\n    for (const listNode of listNodes) {\n      let insertionPoint = listNode;\n      const listItems = $getAllListItems(listNode);\n      for (const listItemNode of listItems) {\n        const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)().setTextStyle(selection.style).setTextFormat(selection.format);\n        append(paragraph, listItemNode.getChildren());\n        insertionPoint.insertAfter(paragraph);\n        insertionPoint = paragraph;\n\n        // When the anchor and focus fall on the textNode\n        // we don't have to change the selection because the textNode will be appended to\n        // the newly generated paragraph.\n        // When selection is in empty nested list item, selection is actually on the listItemNode.\n        // When the corresponding listItemNode is deleted and replaced by the newly generated paragraph\n        // we should manually set the selection's focus and anchor to the newly generated paragraph.\n        if (listItemNode.__key === selection.anchor.key) {\n          (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.anchor, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(paragraph, 'next')));\n        }\n        if (listItemNode.__key === selection.focus.key) {\n          (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.focus, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(paragraph, 'next')));\n        }\n        listItemNode.remove();\n      }\n      listNode.remove();\n    }\n  }\n}\n\n/**\n * Takes the value of a child ListItemNode and makes it the value the ListItemNode\n * should be if it isn't already. Also ensures that checked is undefined if the\n * parent does not have a list type of 'check'.\n * @param list - The list whose children are updated.\n */\nfunction updateChildrenListItemValue(list) {\n  const isNotChecklist = list.getListType() !== 'check';\n  let value = list.getStart();\n  for (const child of list.getChildren()) {\n    if ($isListItemNode(child)) {\n      if (child.getValue() !== value) {\n        child.setValue(value);\n      }\n      if (isNotChecklist && child.getLatest().__checked != null) {\n        child.setChecked(undefined);\n      }\n      if (!$isListNode(child.getFirstChild())) {\n        value++;\n      }\n    }\n  }\n}\n\n/**\n * Merge the next sibling list if same type.\n * <ul> will merge with <ul>, but NOT <ul> with <ol>.\n * @param list - The list whose next sibling should be potentially merged\n */\nfunction mergeNextSiblingListIfSameType(list) {\n  const nextSibling = list.getNextSibling();\n  if ($isListNode(nextSibling) && list.getListType() === nextSibling.getListType()) {\n    mergeLists(list, nextSibling);\n  }\n}\n\n/**\n * Adds an empty ListNode/ListItemNode chain at listItemNode, so as to\n * create an indent effect. Won't indent ListItemNodes that have a ListNode as\n * a child, but does merge sibling ListItemNodes if one has a nested ListNode.\n * @param listItemNode - The ListItemNode to be indented.\n */\nfunction $handleIndent(listItemNode) {\n  // go through each node and decide where to move it.\n  const removed = new Set();\n  if (isNestedListNode(listItemNode) || removed.has(listItemNode.getKey())) {\n    return;\n  }\n  const parent = listItemNode.getParent();\n\n  // We can cast both of the below `isNestedListNode` only returns a boolean type instead of a user-defined type guards\n  const nextSibling = listItemNode.getNextSibling();\n  const previousSibling = listItemNode.getPreviousSibling();\n  // if there are nested lists on either side, merge them all together.\n\n  if (isNestedListNode(nextSibling) && isNestedListNode(previousSibling)) {\n    const innerList = previousSibling.getFirstChild();\n    if ($isListNode(innerList)) {\n      innerList.append(listItemNode);\n      const nextInnerList = nextSibling.getFirstChild();\n      if ($isListNode(nextInnerList)) {\n        const children = nextInnerList.getChildren();\n        append(innerList, children);\n        nextSibling.remove();\n        removed.add(nextSibling.getKey());\n      }\n    }\n  } else if (isNestedListNode(nextSibling)) {\n    // if the ListItemNode is next to a nested ListNode, merge them\n    const innerList = nextSibling.getFirstChild();\n    if ($isListNode(innerList)) {\n      const firstChild = innerList.getFirstChild();\n      if (firstChild !== null) {\n        firstChild.insertBefore(listItemNode);\n      }\n    }\n  } else if (isNestedListNode(previousSibling)) {\n    const innerList = previousSibling.getFirstChild();\n    if ($isListNode(innerList)) {\n      innerList.append(listItemNode);\n    }\n  } else {\n    // otherwise, we need to create a new nested ListNode\n\n    if ($isListNode(parent)) {\n      const newListItem = $createListItemNode().setTextFormat(parent.getTextFormat()).setTextStyle(parent.getTextStyle());\n      const newList = $createListNode(parent.getListType()).setTextFormat(parent.getTextFormat()).setTextStyle(parent.getTextStyle());\n      newListItem.append(newList);\n      newList.append(listItemNode);\n      if (previousSibling) {\n        previousSibling.insertAfter(newListItem);\n      } else if (nextSibling) {\n        nextSibling.insertBefore(newListItem);\n      } else {\n        parent.append(newListItem);\n      }\n    }\n  }\n}\n\n/**\n * Removes an indent by removing an empty ListNode/ListItemNode chain. An indented ListItemNode\n * has a great grandparent node of type ListNode, which is where the ListItemNode will reside\n * within as a child.\n * @param listItemNode - The ListItemNode to remove the indent (outdent).\n */\nfunction $handleOutdent(listItemNode) {\n  // go through each node and decide where to move it.\n\n  if (isNestedListNode(listItemNode)) {\n    return;\n  }\n  const parentList = listItemNode.getParent();\n  const grandparentListItem = parentList ? parentList.getParent() : undefined;\n  const greatGrandparentList = grandparentListItem ? grandparentListItem.getParent() : undefined;\n  // If it doesn't have these ancestors, it's not indented.\n\n  if ($isListNode(greatGrandparentList) && $isListItemNode(grandparentListItem) && $isListNode(parentList)) {\n    // if it's the first child in it's parent list, insert it into the\n    // great grandparent list before the grandparent\n    const firstChild = parentList ? parentList.getFirstChild() : undefined;\n    const lastChild = parentList ? parentList.getLastChild() : undefined;\n    if (listItemNode.is(firstChild)) {\n      grandparentListItem.insertBefore(listItemNode);\n      if (parentList.isEmpty()) {\n        grandparentListItem.remove();\n      }\n      // if it's the last child in it's parent list, insert it into the\n      // great grandparent list after the grandparent.\n    } else if (listItemNode.is(lastChild)) {\n      grandparentListItem.insertAfter(listItemNode);\n      if (parentList.isEmpty()) {\n        grandparentListItem.remove();\n      }\n    } else {\n      // otherwise, we need to split the siblings into two new nested lists\n      const listType = parentList.getListType();\n      const previousSiblingsListItem = $createListItemNode();\n      const previousSiblingsList = $createListNode(listType);\n      previousSiblingsListItem.append(previousSiblingsList);\n      listItemNode.getPreviousSiblings().forEach(sibling => previousSiblingsList.append(sibling));\n      const nextSiblingsListItem = $createListItemNode();\n      const nextSiblingsList = $createListNode(listType);\n      nextSiblingsListItem.append(nextSiblingsList);\n      append(nextSiblingsList, listItemNode.getNextSiblings());\n      // put the sibling nested lists on either side of the grandparent list item in the great grandparent.\n      grandparentListItem.insertBefore(previousSiblingsListItem);\n      grandparentListItem.insertAfter(nextSiblingsListItem);\n      // replace the grandparent list item (now between the siblings) with the outdented list item.\n      grandparentListItem.replace(listItemNode);\n    }\n  }\n}\n\n/**\n * Attempts to insert a ParagraphNode at selection and selects the new node. The selection must contain a ListItemNode\n * or a node that does not already contain text. If its grandparent is the root/shadow root, it will get the ListNode\n * (which should be the parent node) and insert the ParagraphNode as a sibling to the ListNode. If the ListNode is\n * nested in a ListItemNode instead, it will add the ParagraphNode after the grandparent ListItemNode.\n * Throws an invariant if the selection is not a child of a ListNode.\n * @returns true if a ParagraphNode was inserted succesfully, false if there is no selection\n * or the selection does not contain a ListItemNode or the node already holds text.\n */\nfunction $handleListInsertParagraph() {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || !selection.isCollapsed()) {\n    return false;\n  }\n  // Only run this code on empty list items\n  const anchor = selection.anchor.getNode();\n  if (!$isListItemNode(anchor) || anchor.getChildrenSize() !== 0) {\n    return false;\n  }\n  const topListNode = $getTopListNode(anchor);\n  const parent = anchor.getParent();\n  if (!$isListNode(parent)) {\n    formatDevErrorMessage(`A ListItemNode must have a ListNode for a parent.`);\n  }\n  const grandparent = parent.getParent();\n  let replacementNode;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(grandparent)) {\n    replacementNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    topListNode.insertAfter(replacementNode);\n  } else if ($isListItemNode(grandparent)) {\n    replacementNode = $createListItemNode();\n    grandparent.insertAfter(replacementNode);\n  } else {\n    return false;\n  }\n  replacementNode.setTextStyle(selection.style).setTextFormat(selection.format).select();\n  const nextSiblings = anchor.getNextSiblings();\n  if (nextSiblings.length > 0) {\n    const newList = $createListNode(parent.getListType());\n    if ($isListItemNode(replacementNode)) {\n      const newListItem = $createListItemNode();\n      newListItem.append(newList);\n      replacementNode.insertAfter(newListItem);\n    } else {\n      replacementNode.insertAfter(newList);\n    }\n    newList.append(...nextSiblings);\n  }\n\n  // Don't leave hanging nested empty lists\n  $removeHighestEmptyListParent(anchor);\n  return true;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction normalizeClassNames(...classNames) {\n  const rval = [];\n  for (const className of classNames) {\n    if (className && typeof className === 'string') {\n      for (const [s] of className.matchAll(/\\S+/g)) {\n        rval.push(s);\n      }\n    }\n  }\n  return rval;\n}\n\nfunction applyMarkerStyles(dom, node, prevNode) {\n  const styles = (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_2__.getStyleObjectFromCSS)(node.__textStyle);\n  for (const k in styles) {\n    dom.style.setProperty(`--listitem-marker-${k}`, styles[k]);\n  }\n  if (prevNode) {\n    for (const k in (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_2__.getStyleObjectFromCSS)(prevNode.__textStyle)) {\n      if (!(k in styles)) {\n        dom.style.removeProperty(`--listitem-marker-${k}`);\n      }\n    }\n  }\n}\n\n/** @noInheritDoc */\nclass ListItemNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  /** @internal */\n\n  static getType() {\n    return 'listitem';\n  }\n  static clone(node) {\n    return new ListItemNode(node.__value, node.__checked, node.__key);\n  }\n  constructor(value, checked, key) {\n    super(key);\n    this.__value = value === undefined ? 1 : value;\n    this.__checked = checked;\n  }\n  createDOM(config) {\n    const element = document.createElement('li');\n    const parent = this.getParent();\n    if ($isListNode(parent) && parent.getListType() === 'check') {\n      updateListItemChecked(element, this, null);\n    }\n    element.value = this.__value;\n    $setListItemThemeClassNames(element, config.theme, this);\n    const nextStyle = this.__style;\n    if (nextStyle) {\n      element.style.cssText = nextStyle;\n    }\n    applyMarkerStyles(element, this, null);\n    return element;\n  }\n  updateDOM(prevNode, dom, config) {\n    const parent = this.getParent();\n    if ($isListNode(parent) && parent.getListType() === 'check') {\n      updateListItemChecked(dom, this, prevNode);\n    }\n    // @ts-expect-error - this is always HTMLListItemElement\n    dom.value = this.__value;\n    $setListItemThemeClassNames(dom, config.theme, this);\n    const prevStyle = prevNode.__style;\n    const nextStyle = this.__style;\n    if (prevStyle !== nextStyle) {\n      if (nextStyle === '') {\n        dom.removeAttribute('style');\n      } else {\n        dom.style.cssText = nextStyle;\n      }\n    }\n    applyMarkerStyles(dom, this, prevNode);\n    return false;\n  }\n  static transform() {\n    return node => {\n      if (!$isListItemNode(node)) {\n        formatDevErrorMessage(`node is not a ListItemNode`);\n      }\n      if (node.__checked == null) {\n        return;\n      }\n      const parent = node.getParent();\n      if ($isListNode(parent)) {\n        if (parent.getListType() !== 'check' && node.getChecked() != null) {\n          node.setChecked(undefined);\n        }\n      }\n    };\n  }\n  static importDOM() {\n    return {\n      li: () => ({\n        conversion: $convertListItemElement,\n        priority: 0\n      })\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createListItemNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setValue(serializedNode.value).setChecked(serializedNode.checked);\n  }\n  exportDOM(editor) {\n    const element = this.createDOM(editor._config);\n    element.style.textAlign = this.getFormatType();\n    const direction = this.getDirection();\n    if (direction) {\n      element.dir = direction;\n    }\n    return {\n      element\n    };\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      checked: this.getChecked(),\n      value: this.getValue()\n    };\n  }\n  append(...nodes) {\n    for (let i = 0; i < nodes.length; i++) {\n      const node = nodes[i];\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && this.canMergeWith(node)) {\n        const children = node.getChildren();\n        this.append(...children);\n        node.remove();\n      } else {\n        super.append(node);\n      }\n    }\n    return this;\n  }\n  replace(replaceWithNode, includeChildren) {\n    if ($isListItemNode(replaceWithNode)) {\n      return super.replace(replaceWithNode);\n    }\n    this.setIndent(0);\n    const list = this.getParentOrThrow();\n    if (!$isListNode(list)) {\n      return replaceWithNode;\n    }\n    if (list.__first === this.getKey()) {\n      list.insertBefore(replaceWithNode);\n    } else if (list.__last === this.getKey()) {\n      list.insertAfter(replaceWithNode);\n    } else {\n      // Split the list\n      const newList = $createListNode(list.getListType());\n      let nextSibling = this.getNextSibling();\n      while (nextSibling) {\n        const nodeToAppend = nextSibling;\n        nextSibling = nextSibling.getNextSibling();\n        newList.append(nodeToAppend);\n      }\n      list.insertAfter(replaceWithNode);\n      replaceWithNode.insertAfter(newList);\n    }\n    if (includeChildren) {\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(replaceWithNode)) {\n        formatDevErrorMessage(`includeChildren should only be true for ElementNodes`);\n      }\n      this.getChildren().forEach(child => {\n        replaceWithNode.append(child);\n      });\n    }\n    this.remove();\n    if (list.getChildrenSize() === 0) {\n      list.remove();\n    }\n    return replaceWithNode;\n  }\n  insertAfter(node, restoreSelection = true) {\n    const listNode = this.getParentOrThrow();\n    if (!$isListNode(listNode)) {\n      {\n        formatDevErrorMessage(`insertAfter: list node is not parent of list item node`);\n      }\n    }\n    if ($isListItemNode(node)) {\n      return super.insertAfter(node, restoreSelection);\n    }\n    const siblings = this.getNextSiblings();\n\n    // Split the lists and insert the node in between them\n    listNode.insertAfter(node, restoreSelection);\n    if (siblings.length !== 0) {\n      const newListNode = $createListNode(listNode.getListType());\n      siblings.forEach(sibling => newListNode.append(sibling));\n      node.insertAfter(newListNode, restoreSelection);\n    }\n    return node;\n  }\n  remove(preserveEmptyParent) {\n    const prevSibling = this.getPreviousSibling();\n    const nextSibling = this.getNextSibling();\n    super.remove(preserveEmptyParent);\n    if (prevSibling && nextSibling && isNestedListNode(prevSibling) && isNestedListNode(nextSibling)) {\n      mergeLists(prevSibling.getFirstChild(), nextSibling.getFirstChild());\n      nextSibling.remove();\n    }\n  }\n  insertNewAfter(_, restoreSelection = true) {\n    const newElement = $createListItemNode().updateFromJSON(this.exportJSON()).setChecked(this.getChecked() ? false : undefined);\n    this.insertAfter(newElement, restoreSelection);\n    return newElement;\n  }\n  collapseAtStart(selection) {\n    const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    const children = this.getChildren();\n    children.forEach(child => paragraph.append(child));\n    const listNode = this.getParentOrThrow();\n    const listNodeParent = listNode.getParentOrThrow();\n    const isIndented = $isListItemNode(listNodeParent);\n    if (listNode.getChildrenSize() === 1) {\n      if (isIndented) {\n        // if the list node is nested, we just want to remove it,\n        // effectively unindenting it.\n        listNode.remove();\n        listNodeParent.select();\n      } else {\n        listNode.insertBefore(paragraph);\n        listNode.remove();\n        // If we have selection on the list item, we'll need to move it\n        // to the paragraph\n        const anchor = selection.anchor;\n        const focus = selection.focus;\n        const key = paragraph.getKey();\n        if (anchor.type === 'element' && anchor.getNode().is(this)) {\n          anchor.set(key, anchor.offset, 'element');\n        }\n        if (focus.type === 'element' && focus.getNode().is(this)) {\n          focus.set(key, focus.offset, 'element');\n        }\n      }\n    } else {\n      listNode.insertBefore(paragraph);\n      this.remove();\n    }\n    return true;\n  }\n  getValue() {\n    const self = this.getLatest();\n    return self.__value;\n  }\n  setValue(value) {\n    const self = this.getWritable();\n    self.__value = value;\n    return self;\n  }\n  getChecked() {\n    const self = this.getLatest();\n    let listType;\n    const parent = this.getParent();\n    if ($isListNode(parent)) {\n      listType = parent.getListType();\n    }\n    return listType === 'check' ? Boolean(self.__checked) : undefined;\n  }\n  setChecked(checked) {\n    const self = this.getWritable();\n    self.__checked = checked;\n    return self;\n  }\n  toggleChecked() {\n    const self = this.getWritable();\n    return self.setChecked(!self.__checked);\n  }\n  getIndent() {\n    // If we don't have a parent, we are likely serializing\n    const parent = this.getParent();\n    if (parent === null || !this.isAttached()) {\n      return this.getLatest().__indent;\n    }\n    // ListItemNode should always have a ListNode for a parent.\n    let listNodeParent = parent.getParentOrThrow();\n    let indentLevel = 0;\n    while ($isListItemNode(listNodeParent)) {\n      listNodeParent = listNodeParent.getParentOrThrow().getParentOrThrow();\n      indentLevel++;\n    }\n    return indentLevel;\n  }\n  setIndent(indent) {\n    if (!(typeof indent === 'number')) {\n      formatDevErrorMessage(`Invalid indent value.`);\n    }\n    indent = Math.floor(indent);\n    if (!(indent >= 0)) {\n      formatDevErrorMessage(`Indent value must be non-negative.`);\n    }\n    let currentIndent = this.getIndent();\n    while (currentIndent !== indent) {\n      if (currentIndent < indent) {\n        $handleIndent(this);\n        currentIndent++;\n      } else {\n        $handleOutdent(this);\n        currentIndent--;\n      }\n    }\n    return this;\n  }\n\n  /** @deprecated @internal */\n  canInsertAfter(node) {\n    return $isListItemNode(node);\n  }\n\n  /** @deprecated @internal */\n  canReplaceWith(replacement) {\n    return $isListItemNode(replacement);\n  }\n  canMergeWith(node) {\n    return $isListItemNode(node) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isParagraphNode)(node);\n  }\n  extractWithChild(child, selection) {\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const anchorNode = selection.anchor.getNode();\n    const focusNode = selection.focus.getNode();\n    return this.isParentOf(anchorNode) && this.isParentOf(focusNode) && this.getTextContent().length === selection.getTextContent().length;\n  }\n  isParentRequired() {\n    return true;\n  }\n  createParentElementNode() {\n    return $createListNode('bullet');\n  }\n  canMergeWhenEmpty() {\n    return true;\n  }\n}\nfunction $setListItemThemeClassNames(dom, editorThemeClasses, node) {\n  const classesToAdd = [];\n  const classesToRemove = [];\n  const listTheme = editorThemeClasses.list;\n  const listItemClassName = listTheme ? listTheme.listitem : undefined;\n  let nestedListItemClassName;\n  if (listTheme && listTheme.nested) {\n    nestedListItemClassName = listTheme.nested.listitem;\n  }\n  if (listItemClassName !== undefined) {\n    classesToAdd.push(...normalizeClassNames(listItemClassName));\n  }\n  if (listTheme) {\n    const parentNode = node.getParent();\n    const isCheckList = $isListNode(parentNode) && parentNode.getListType() === 'check';\n    const checked = node.getChecked();\n    if (!isCheckList || checked) {\n      classesToRemove.push(listTheme.listitemUnchecked);\n    }\n    if (!isCheckList || !checked) {\n      classesToRemove.push(listTheme.listitemChecked);\n    }\n    if (isCheckList) {\n      classesToAdd.push(checked ? listTheme.listitemChecked : listTheme.listitemUnchecked);\n    }\n  }\n  if (nestedListItemClassName !== undefined) {\n    const nestedListItemClasses = normalizeClassNames(nestedListItemClassName);\n    if (node.getChildren().some(child => $isListNode(child))) {\n      classesToAdd.push(...nestedListItemClasses);\n    } else {\n      classesToRemove.push(...nestedListItemClasses);\n    }\n  }\n  if (classesToRemove.length > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, ...classesToRemove);\n  }\n  if (classesToAdd.length > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, ...classesToAdd);\n  }\n}\nfunction updateListItemChecked(dom, listItemNode, prevListItemNode, listNode) {\n  // Only add attributes for leaf list items\n  if ($isListNode(listItemNode.getFirstChild())) {\n    dom.removeAttribute('role');\n    dom.removeAttribute('tabIndex');\n    dom.removeAttribute('aria-checked');\n  } else {\n    dom.setAttribute('role', 'checkbox');\n    dom.setAttribute('tabIndex', '-1');\n    if (!prevListItemNode || listItemNode.__checked !== prevListItemNode.__checked) {\n      dom.setAttribute('aria-checked', listItemNode.getChecked() ? 'true' : 'false');\n    }\n  }\n}\nfunction $convertListItemElement(domNode) {\n  const isGitHubCheckList = domNode.classList.contains('task-list-item');\n  if (isGitHubCheckList) {\n    for (const child of domNode.children) {\n      if (child.tagName === 'INPUT') {\n        return $convertCheckboxInput(child);\n      }\n    }\n  }\n  const ariaCheckedAttr = domNode.getAttribute('aria-checked');\n  const checked = ariaCheckedAttr === 'true' ? true : ariaCheckedAttr === 'false' ? false : undefined;\n  return {\n    node: $createListItemNode(checked)\n  };\n}\nfunction $convertCheckboxInput(domNode) {\n  const isCheckboxInput = domNode.getAttribute('type') === 'checkbox';\n  if (!isCheckboxInput) {\n    return {\n      node: null\n    };\n  }\n  const checked = domNode.hasAttribute('checked');\n  return {\n    node: $createListItemNode(checked)\n  };\n}\n\n/**\n * Creates a new List Item node, passing true/false will convert it to a checkbox input.\n * @param checked - Is the List Item a checkbox and, if so, is it checked? undefined/null: not a checkbox, true/false is a checkbox and checked/unchecked, respectively.\n * @returns The new List Item.\n */\nfunction $createListItemNode(checked) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new ListItemNode(undefined, checked));\n}\n\n/**\n * Checks to see if the node is a ListItemNode.\n * @param node - The node to be checked.\n * @returns true if the node is a ListItemNode, false otherwise.\n */\nfunction $isListItemNode(node) {\n  return node instanceof ListItemNode;\n}\n\n/** @noInheritDoc */\nclass ListNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  static getType() {\n    return 'list';\n  }\n  static clone(node) {\n    const listType = node.__listType || TAG_TO_LIST_TYPE[node.__tag];\n    return new ListNode(listType, node.__start, node.__key);\n  }\n  constructor(listType = 'number', start = 1, key) {\n    super(key);\n    const _listType = TAG_TO_LIST_TYPE[listType] || listType;\n    this.__listType = _listType;\n    this.__tag = _listType === 'number' ? 'ol' : 'ul';\n    this.__start = start;\n  }\n  getTag() {\n    return this.__tag;\n  }\n  setListType(type) {\n    const writable = this.getWritable();\n    writable.__listType = type;\n    writable.__tag = type === 'number' ? 'ol' : 'ul';\n    return writable;\n  }\n  getListType() {\n    return this.__listType;\n  }\n  getStart() {\n    return this.__start;\n  }\n  setStart(start) {\n    const self = this.getWritable();\n    self.__start = start;\n    return self;\n  }\n\n  // View\n\n  createDOM(config, _editor) {\n    const tag = this.__tag;\n    const dom = document.createElement(tag);\n    if (this.__start !== 1) {\n      dom.setAttribute('start', String(this.__start));\n    }\n    // @ts-expect-error Internal field.\n    dom.__lexicalListType = this.__listType;\n    $setListThemeClassNames(dom, config.theme, this);\n    return dom;\n  }\n  updateDOM(prevNode, dom, config) {\n    if (prevNode.__tag !== this.__tag) {\n      return true;\n    }\n    $setListThemeClassNames(dom, config.theme, this);\n    return false;\n  }\n  static transform() {\n    return node => {\n      if (!$isListNode(node)) {\n        formatDevErrorMessage(`node is not a ListNode`);\n      }\n      mergeNextSiblingListIfSameType(node);\n      updateChildrenListItemValue(node);\n    };\n  }\n  static importDOM() {\n    return {\n      ol: () => ({\n        conversion: $convertListNode,\n        priority: 0\n      }),\n      ul: () => ({\n        conversion: $convertListNode,\n        priority: 0\n      })\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createListNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setListType(serializedNode.listType).setStart(serializedNode.start);\n  }\n  exportDOM(editor) {\n    const element = this.createDOM(editor._config, editor);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n      if (this.__start !== 1) {\n        element.setAttribute('start', String(this.__start));\n      }\n      if (this.__listType === 'check') {\n        element.setAttribute('__lexicalListType', 'check');\n      }\n    }\n    return {\n      element\n    };\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      listType: this.getListType(),\n      start: this.getStart(),\n      tag: this.getTag()\n    };\n  }\n  canBeEmpty() {\n    return false;\n  }\n  canIndent() {\n    return false;\n  }\n  splice(start, deleteCount, nodesToInsert) {\n    let listItemNodesToInsert = nodesToInsert;\n    for (let i = 0; i < nodesToInsert.length; i++) {\n      const node = nodesToInsert[i];\n      if (!$isListItemNode(node)) {\n        if (listItemNodesToInsert === nodesToInsert) {\n          listItemNodesToInsert = [...nodesToInsert];\n        }\n        listItemNodesToInsert[i] = $createListItemNode().append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && !($isListNode(node) || node.isInline()) ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(node.getTextContent()) : node);\n      }\n    }\n    return super.splice(start, deleteCount, listItemNodesToInsert);\n  }\n  extractWithChild(child) {\n    return $isListItemNode(child);\n  }\n}\nfunction $setListThemeClassNames(dom, editorThemeClasses, node) {\n  const classesToAdd = [];\n  const classesToRemove = [];\n  const listTheme = editorThemeClasses.list;\n  if (listTheme !== undefined) {\n    const listLevelsClassNames = listTheme[`${node.__tag}Depth`] || [];\n    const listDepth = $getListDepth(node) - 1;\n    const normalizedListDepth = listDepth % listLevelsClassNames.length;\n    const listLevelClassName = listLevelsClassNames[normalizedListDepth];\n    const listClassName = listTheme[node.__tag];\n    let nestedListClassName;\n    const nestedListTheme = listTheme.nested;\n    const checklistClassName = listTheme.checklist;\n    if (nestedListTheme !== undefined && nestedListTheme.list) {\n      nestedListClassName = nestedListTheme.list;\n    }\n    if (listClassName !== undefined) {\n      classesToAdd.push(listClassName);\n    }\n    if (checklistClassName !== undefined && node.__listType === 'check') {\n      classesToAdd.push(checklistClassName);\n    }\n    if (listLevelClassName !== undefined) {\n      classesToAdd.push(...normalizeClassNames(listLevelClassName));\n      for (let i = 0; i < listLevelsClassNames.length; i++) {\n        if (i !== normalizedListDepth) {\n          classesToRemove.push(node.__tag + i);\n        }\n      }\n    }\n    if (nestedListClassName !== undefined) {\n      const nestedListItemClasses = normalizeClassNames(nestedListClassName);\n      if (listDepth > 1) {\n        classesToAdd.push(...nestedListItemClasses);\n      } else {\n        classesToRemove.push(...nestedListItemClasses);\n      }\n    }\n  }\n  if (classesToRemove.length > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, ...classesToRemove);\n  }\n  if (classesToAdd.length > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, ...classesToAdd);\n  }\n}\n\n/*\n * This function normalizes the children of a ListNode after the conversion from HTML,\n * ensuring that they are all ListItemNodes and contain either a single nested ListNode\n * or some other inline content.\n */\nfunction $normalizeChildren(nodes) {\n  const normalizedListItems = [];\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if ($isListItemNode(node)) {\n      normalizedListItems.push(node);\n      const children = node.getChildren();\n      if (children.length > 1) {\n        children.forEach(child => {\n          if ($isListNode(child)) {\n            normalizedListItems.push($wrapInListItem(child));\n          }\n        });\n      }\n    } else {\n      normalizedListItems.push($wrapInListItem(node));\n    }\n  }\n  return normalizedListItems;\n}\nfunction isDomChecklist(domNode) {\n  if (domNode.getAttribute('__lexicallisttype') === 'check' ||\n  // is github checklist\n  domNode.classList.contains('contains-task-list')) {\n    return true;\n  }\n  // if children are checklist items, the node is a checklist ul. Applicable for googledoc checklist pasting.\n  for (const child of domNode.childNodes) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(child) && child.hasAttribute('aria-checked')) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction $convertListNode(domNode) {\n  const nodeName = domNode.nodeName.toLowerCase();\n  let node = null;\n  if (nodeName === 'ol') {\n    // @ts-ignore\n    const start = domNode.start;\n    node = $createListNode('number', start);\n  } else if (nodeName === 'ul') {\n    if (isDomChecklist(domNode)) {\n      node = $createListNode('check');\n    } else {\n      node = $createListNode('bullet');\n    }\n  }\n  return {\n    after: $normalizeChildren,\n    node\n  };\n}\nconst TAG_TO_LIST_TYPE = {\n  ol: 'number',\n  ul: 'bullet'\n};\n\n/**\n * Creates a ListNode of listType.\n * @param listType - The type of list to be created. Can be 'number', 'bullet', or 'check'.\n * @param start - Where an ordered list starts its count, start = 1 if left undefined.\n * @returns The new ListNode\n */\nfunction $createListNode(listType = 'number', start = 1) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new ListNode(listType, start));\n}\n\n/**\n * Checks to see if the node is a ListNode.\n * @param node - The node to be checked.\n * @returns true if the node is a ListNode, false otherwise.\n */\nfunction $isListNode(node) {\n  return node instanceof ListNode;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst INSERT_UNORDERED_LIST_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('INSERT_UNORDERED_LIST_COMMAND');\nconst INSERT_ORDERED_LIST_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('INSERT_ORDERED_LIST_COMMAND');\nconst INSERT_CHECK_LIST_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('INSERT_CHECK_LIST_COMMAND');\nconst REMOVE_LIST_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('REMOVE_LIST_COMMAND');\nfunction registerList(editor) {\n  const removeListener = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.mergeRegister)(editor.registerCommand(INSERT_ORDERED_LIST_COMMAND, () => {\n    $insertList('number');\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_LOW), editor.registerCommand(INSERT_UNORDERED_LIST_COMMAND, () => {\n    $insertList('bullet');\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_LOW), editor.registerCommand(REMOVE_LIST_COMMAND, () => {\n    $removeList();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_LOW), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, () => $handleListInsertParagraph(), lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_LOW), editor.registerNodeTransform(ListItemNode, node => {\n    const firstChild = node.getFirstChild();\n    if (firstChild) {\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(firstChild)) {\n        const style = firstChild.getStyle();\n        const format = firstChild.getFormat();\n        if (node.getTextStyle() !== style) {\n          node.setTextStyle(style);\n        }\n        if (node.getTextFormat() !== format) {\n          node.setTextFormat(format);\n        }\n      }\n    } else {\n      // If it's empty, check the selection\n      const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && (selection.style !== node.getTextStyle() || selection.format !== node.getTextFormat()) && selection.isCollapsed() && node.is(selection.anchor.getNode())) {\n        node.setTextStyle(selection.style).setTextFormat(selection.format);\n      }\n    }\n  }), editor.registerNodeTransform(lexical__WEBPACK_IMPORTED_MODULE_0__.TextNode, node => {\n    const listItemParentNode = node.getParent();\n    if ($isListItemNode(listItemParentNode) && node.is(listItemParentNode.getFirstChild())) {\n      const style = node.getStyle();\n      const format = node.getFormat();\n      if (style !== listItemParentNode.getTextStyle() || format !== listItemParentNode.getTextFormat()) {\n        listItemParentNode.setTextStyle(style).setTextFormat(format);\n      }\n    }\n  }));\n  return removeListener;\n}\n\n/**\n * @deprecated use {@link $insertList} from an update or command listener.\n *\n * Inserts a new ListNode. If the selection's anchor node is an empty ListItemNode and is a child of\n * the root/shadow root, it will replace the ListItemNode with a ListNode and the old ListItemNode.\n * Otherwise it will replace its parent with a new ListNode and re-insert the ListItemNode and any previous children.\n * If the selection's anchor node is not an empty ListItemNode, it will add a new ListNode or merge an existing ListNode,\n * unless the the node is a leaf node, in which case it will attempt to find a ListNode up the branch and replace it with\n * a new ListNode, or create a new ListNode at the nearest root/shadow root.\n * @param editor - The lexical editor.\n * @param listType - The type of list, \"number\" | \"bullet\" | \"check\".\n */\nfunction insertList(editor, listType) {\n  editor.update(() => $insertList(listType));\n}\n\n/**\n * @deprecated use {@link $removeList} from an update or command listener.\n *\n * Searches for the nearest ancestral ListNode and removes it. If selection is an empty ListItemNode\n * it will remove the whole list, including the ListItemNode. For each ListItemNode in the ListNode,\n * removeList will also generate new ParagraphNodes in the removed ListNode's place. Any child node\n * inside a ListItemNode will be appended to the new ParagraphNodes.\n * @param editor - The lexical editor.\n */\nfunction removeList(editor) {\n  editor.update(() => $removeList());\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+list@0.28.0/node_modules/@lexical/list/LexicalList.dev.mjs\n");

/***/ })

};
;