"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+clipboard@0.28.0";
exports.ids = ["vendor-chunks/@lexical+clipboard@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+clipboard@0.28.0/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+clipboard@0.28.0/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $generateJSONFromSelectedNodes: () => (/* binding */ $generateJSONFromSelectedNodes),\n/* harmony export */   $generateNodesFromSerializedNodes: () => (/* binding */ $generateNodesFromSerializedNodes),\n/* harmony export */   $getClipboardDataFromSelection: () => (/* binding */ $getClipboardDataFromSelection),\n/* harmony export */   $getHtmlContent: () => (/* binding */ $getHtmlContent),\n/* harmony export */   $getLexicalContent: () => (/* binding */ $getLexicalContent),\n/* harmony export */   $insertDataTransferForPlainText: () => (/* binding */ $insertDataTransferForPlainText),\n/* harmony export */   $insertDataTransferForRichText: () => (/* binding */ $insertDataTransferForRichText),\n/* harmony export */   $insertGeneratedNodes: () => (/* binding */ $insertGeneratedNodes),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   setLexicalClipboardDataTransfer: () => (/* binding */ setLexicalClipboardDataTransfer)\n/* harmony export */ });\n/* harmony import */ var _lexical_html__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/html */ \"(rsc)/./node_modules/.pnpm/@lexical+html@0.28.0/node_modules/@lexical/html/LexicalHtml.dev.mjs\");\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/selection */ \"(rsc)/./node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/utils */ \"(rsc)/./node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Returns the *currently selected* Lexical content as an HTML string, relying on the\n * logic defined in the exportDOM methods on the LexicalNode classes. Note that\n * this will not return the HTML content of the entire editor (unless all the content is included\n * in the current selection).\n *\n * @param editor - LexicalEditor instance to get HTML content from\n * @param selection - The selection to use (default is $getSelection())\n * @returns a string of HTML content\n */\nfunction $getHtmlContent(editor, selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)()) {\n  if (selection == null) {\n    {\n      formatDevErrorMessage(`Expected valid LexicalSelection`);\n    }\n  }\n\n  // If we haven't selected anything\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed() || selection.getNodes().length === 0) {\n    return '';\n  }\n  return (0,_lexical_html__WEBPACK_IMPORTED_MODULE_1__.$generateHtmlFromNodes)(editor, selection);\n}\n\n/**\n * Returns the *currently selected* Lexical content as a JSON string, relying on the\n * logic defined in the exportJSON methods on the LexicalNode classes. Note that\n * this will not return the JSON content of the entire editor (unless all the content is included\n * in the current selection).\n *\n * @param editor  - LexicalEditor instance to get the JSON content from\n * @param selection - The selection to use (default is $getSelection())\n * @returns\n */\nfunction $getLexicalContent(editor, selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)()) {\n  if (selection == null) {\n    {\n      formatDevErrorMessage(`Expected valid LexicalSelection`);\n    }\n  }\n\n  // If we haven't selected anything\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed() || selection.getNodes().length === 0) {\n    return null;\n  }\n  return JSON.stringify($generateJSONFromSelectedNodes(editor, selection));\n}\n\n/**\n * Attempts to insert content of the mime-types text/plain or text/uri-list from\n * the provided DataTransfer object into the editor at the provided selection.\n * text/uri-list is only used if text/plain is not also provided.\n *\n * @param dataTransfer an object conforming to the [DataTransfer interface] (https://html.spec.whatwg.org/multipage/dnd.html#the-datatransfer-interface)\n * @param selection the selection to use as the insertion point for the content in the DataTransfer object\n */\nfunction $insertDataTransferForPlainText(dataTransfer, selection) {\n  const text = dataTransfer.getData('text/plain') || dataTransfer.getData('text/uri-list');\n  if (text != null) {\n    selection.insertRawText(text);\n  }\n}\n\n/**\n * Attempts to insert content of the mime-types application/x-lexical-editor, text/html,\n * text/plain, or text/uri-list (in descending order of priority) from the provided DataTransfer\n * object into the editor at the provided selection.\n *\n * @param dataTransfer an object conforming to the [DataTransfer interface] (https://html.spec.whatwg.org/multipage/dnd.html#the-datatransfer-interface)\n * @param selection the selection to use as the insertion point for the content in the DataTransfer object\n * @param editor the LexicalEditor the content is being inserted into.\n */\nfunction $insertDataTransferForRichText(dataTransfer, selection, editor) {\n  const lexicalString = dataTransfer.getData('application/x-lexical-editor');\n  if (lexicalString) {\n    try {\n      const payload = JSON.parse(lexicalString);\n      if (payload.namespace === editor._config.namespace && Array.isArray(payload.nodes)) {\n        const nodes = $generateNodesFromSerializedNodes(payload.nodes);\n        return $insertGeneratedNodes(editor, nodes, selection);\n      }\n    } catch (_unused) {\n      // Fail silently.\n    }\n  }\n  const htmlString = dataTransfer.getData('text/html');\n  const plainString = dataTransfer.getData('text/plain');\n\n  // Skip HTML handling if it matches the plain text representation.\n  // This avoids unnecessary processing for plain text strings created by\n  // iOS Safari autocorrect, which incorrectly includes a `text/html` type.\n  if (htmlString && plainString !== htmlString) {\n    try {\n      const parser = new DOMParser();\n      const dom = parser.parseFromString(trustHTML(htmlString), 'text/html');\n      const nodes = (0,_lexical_html__WEBPACK_IMPORTED_MODULE_1__.$generateNodesFromDOM)(editor, dom);\n      return $insertGeneratedNodes(editor, nodes, selection);\n    } catch (_unused2) {\n      // Fail silently.\n    }\n  }\n\n  // Multi-line plain text in rich text mode pasted as separate paragraphs\n  // instead of single paragraph with linebreaks.\n  // Webkit-specific: Supports read 'text/uri-list' in clipboard.\n  const text = plainString || dataTransfer.getData('text/uri-list');\n  if (text != null) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const parts = text.split(/(\\r?\\n|\\t)/);\n      if (parts[parts.length - 1] === '') {\n        parts.pop();\n      }\n      for (let i = 0; i < parts.length; i++) {\n        const currentSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(currentSelection)) {\n          const part = parts[i];\n          if (part === '\\n' || part === '\\r\\n') {\n            currentSelection.insertParagraph();\n          } else if (part === '\\t') {\n            currentSelection.insertNodes([(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTabNode)()]);\n          } else {\n            currentSelection.insertText(part);\n          }\n        }\n      }\n    } else {\n      selection.insertRawText(text);\n    }\n  }\n}\nfunction trustHTML(html) {\n  if (window.trustedTypes && window.trustedTypes.createPolicy) {\n    const policy = window.trustedTypes.createPolicy('lexical', {\n      createHTML: input => input\n    });\n    return policy.createHTML(html);\n  }\n  return html;\n}\n\n/**\n * Inserts Lexical nodes into the editor using different strategies depending on\n * some simple selection-based heuristics. If you're looking for a generic way to\n * to insert nodes into the editor at a specific selection point, you probably want\n * {@link lexical.$insertNodes}\n *\n * @param editor LexicalEditor instance to insert the nodes into.\n * @param nodes The nodes to insert.\n * @param selection The selection to insert the nodes into.\n */\nfunction $insertGeneratedNodes(editor, nodes, selection) {\n  if (!editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_INSERT_CLIPBOARD_NODES_COMMAND, {\n    nodes,\n    selection\n  })) {\n    selection.insertNodes(nodes);\n  }\n  return;\n}\nfunction exportNodeToJSON(node) {\n  const serializedNode = node.exportJSON();\n  const nodeClass = node.constructor;\n  if (serializedNode.type !== nodeClass.getType()) {\n    {\n      formatDevErrorMessage(`LexicalNode: Node ${nodeClass.name} does not implement .exportJSON().`);\n    }\n  }\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n    const serializedChildren = serializedNode.children;\n    if (!Array.isArray(serializedChildren)) {\n      {\n        formatDevErrorMessage(`LexicalNode: Node ${nodeClass.name} is an element but .exportJSON() does not have a children array.`);\n      }\n    }\n  }\n  return serializedNode;\n}\nfunction $appendNodesToJSON(editor, selection, currentNode, targetArray = []) {\n  let shouldInclude = selection !== null ? currentNode.isSelected(selection) : true;\n  const shouldExclude = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode) && currentNode.excludeFromCopy('html');\n  let target = currentNode;\n  if (selection !== null) {\n    let clone = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$cloneWithProperties)(currentNode);\n    clone = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(clone) && selection !== null ? (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_2__.$sliceSelectedTextNodeContent)(selection, clone) : clone;\n    target = clone;\n  }\n  const children = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(target) ? target.getChildren() : [];\n  const serializedNode = exportNodeToJSON(target);\n\n  // TODO: TextNode calls getTextContent() (NOT node.__text) within its exportJSON method\n  // which uses getLatest() to get the text from the original node with the same key.\n  // This is a deeper issue with the word \"clone\" here, it's still a reference to the\n  // same node as far as the LexicalEditor is concerned since it shares a key.\n  // We need a way to create a clone of a Node in memory with its own key, but\n  // until then this hack will work for the selected text extract use case.\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(target)) {\n    const text = target.__text;\n    // If an uncollapsed selection ends or starts at the end of a line of specialized,\n    // TextNodes, such as code tokens, we will get a 'blank' TextNode here, i.e., one\n    // with text of length 0. We don't want this, it makes a confusing mess. Reset!\n    if (text.length > 0) {\n      serializedNode.text = text;\n    } else {\n      shouldInclude = false;\n    }\n  }\n  for (let i = 0; i < children.length; i++) {\n    const childNode = children[i];\n    const shouldIncludeChild = $appendNodesToJSON(editor, selection, childNode, serializedNode.children);\n    if (!shouldInclude && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode) && shouldIncludeChild && currentNode.extractWithChild(childNode, selection, 'clone')) {\n      shouldInclude = true;\n    }\n  }\n  if (shouldInclude && !shouldExclude) {\n    targetArray.push(serializedNode);\n  } else if (Array.isArray(serializedNode.children)) {\n    for (let i = 0; i < serializedNode.children.length; i++) {\n      const serializedChildNode = serializedNode.children[i];\n      targetArray.push(serializedChildNode);\n    }\n  }\n  return shouldInclude;\n}\n\n// TODO why $ function with Editor instance?\n/**\n * Gets the Lexical JSON of the nodes inside the provided Selection.\n *\n * @param editor LexicalEditor to get the JSON content from.\n * @param selection Selection to get the JSON content from.\n * @returns an object with the editor namespace and a list of serializable nodes as JavaScript objects.\n */\nfunction $generateJSONFromSelectedNodes(editor, selection) {\n  const nodes = [];\n  const root = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)();\n  const topLevelChildren = root.getChildren();\n  for (let i = 0; i < topLevelChildren.length; i++) {\n    const topLevelNode = topLevelChildren[i];\n    $appendNodesToJSON(editor, selection, topLevelNode, nodes);\n  }\n  return {\n    namespace: editor._config.namespace,\n    nodes\n  };\n}\n\n/**\n * This method takes an array of objects conforming to the BaseSeralizedNode interface and returns\n * an Array containing instances of the corresponding LexicalNode classes registered on the editor.\n * Normally, you'd get an Array of BaseSerialized nodes from {@link $generateJSONFromSelectedNodes}\n *\n * @param serializedNodes an Array of objects conforming to the BaseSerializedNode interface.\n * @returns an Array of Lexical Node objects.\n */\nfunction $generateNodesFromSerializedNodes(serializedNodes) {\n  const nodes = [];\n  for (let i = 0; i < serializedNodes.length; i++) {\n    const serializedNode = serializedNodes[i];\n    const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$parseSerializedNode)(serializedNode);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(node)) {\n      (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_2__.$addNodeStyle)(node);\n    }\n    nodes.push(node);\n  }\n  return nodes;\n}\nconst EVENT_LATENCY = 50;\nlet clipboardEventTimeout = null;\n\n// TODO custom selection\n// TODO potentially have a node customizable version for plain text\n/**\n * Copies the content of the current selection to the clipboard in\n * text/plain, text/html, and application/x-lexical-editor (Lexical JSON)\n * formats.\n *\n * @param editor the LexicalEditor instance to copy content from\n * @param event the native browser ClipboardEvent to add the content to.\n * @returns\n */\nasync function copyToClipboard(editor, event, data) {\n  if (clipboardEventTimeout !== null) {\n    // Prevent weird race conditions that can happen when this function is run multiple times\n    // synchronously. In the future, we can do better, we can cancel/override the previously running job.\n    return false;\n  }\n  if (event !== null) {\n    return new Promise((resolve, reject) => {\n      editor.update(() => {\n        resolve($copyToClipboardEvent(editor, event, data));\n      });\n    });\n  }\n  const rootElement = editor.getRootElement();\n  const editorWindow = editor._window || window;\n  const windowDocument = window.document;\n  const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(editorWindow);\n  if (rootElement === null || domSelection === null) {\n    return false;\n  }\n  const element = windowDocument.createElement('span');\n  element.style.cssText = 'position: fixed; top: -1000px;';\n  element.append(windowDocument.createTextNode('#'));\n  rootElement.append(element);\n  const range = new Range();\n  range.setStart(element, 0);\n  range.setEnd(element, 1);\n  domSelection.removeAllRanges();\n  domSelection.addRange(range);\n  return new Promise((resolve, reject) => {\n    const removeListener = editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.COPY_COMMAND, secondEvent => {\n      if ((0,_lexical_utils__WEBPACK_IMPORTED_MODULE_3__.objectKlassEquals)(secondEvent, ClipboardEvent)) {\n        removeListener();\n        if (clipboardEventTimeout !== null) {\n          window.clearTimeout(clipboardEventTimeout);\n          clipboardEventTimeout = null;\n        }\n        resolve($copyToClipboardEvent(editor, secondEvent, data));\n      }\n      // Block the entire copy flow while we wait for the next ClipboardEvent\n      return true;\n    }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL);\n    // If the above hack execCommand hack works, this timeout code should never fire. Otherwise,\n    // the listener will be quickly freed so that the user can reuse it again\n    clipboardEventTimeout = window.setTimeout(() => {\n      removeListener();\n      clipboardEventTimeout = null;\n      resolve(false);\n    }, EVENT_LATENCY);\n    windowDocument.execCommand('copy');\n    element.remove();\n  });\n}\n\n// TODO shouldn't pass editor (pass namespace directly)\nfunction $copyToClipboardEvent(editor, event, data) {\n  if (data === undefined) {\n    const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(editor._window);\n    if (!domSelection) {\n      return false;\n    }\n    const anchorDOM = domSelection.anchorNode;\n    const focusDOM = domSelection.focusNode;\n    if (anchorDOM !== null && focusDOM !== null && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isSelectionWithinEditor)(editor, anchorDOM, focusDOM)) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (selection === null) {\n      return false;\n    }\n    data = $getClipboardDataFromSelection(selection);\n  }\n  event.preventDefault();\n  const clipboardData = event.clipboardData;\n  if (clipboardData === null) {\n    return false;\n  }\n  setLexicalClipboardDataTransfer(clipboardData, data);\n  return true;\n}\nconst clipboardDataFunctions = [['text/html', $getHtmlContent], ['application/x-lexical-editor', $getLexicalContent]];\n\n/**\n * Serialize the content of the current selection to strings in\n * text/plain, text/html, and application/x-lexical-editor (Lexical JSON)\n * formats (as available).\n *\n * @param selection the selection to serialize (defaults to $getSelection())\n * @returns LexicalClipboardData\n */\nfunction $getClipboardDataFromSelection(selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)()) {\n  const clipboardData = {\n    'text/plain': selection ? selection.getTextContent() : ''\n  };\n  if (selection) {\n    const editor = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getEditor)();\n    for (const [mimeType, $editorFn] of clipboardDataFunctions) {\n      const v = $editorFn(editor, selection);\n      if (v !== null) {\n        clipboardData[mimeType] = v;\n      }\n    }\n  }\n  return clipboardData;\n}\n\n/**\n * Call setData on the given clipboardData for each MIME type present\n * in the given data (from {@link $getClipboardDataFromSelection})\n *\n * @param clipboardData the event.clipboardData to populate from data\n * @param data The lexical data\n */\nfunction setLexicalClipboardDataTransfer(clipboardData, data) {\n  for (const k in data) {\n    const v = data[k];\n    if (v !== undefined) {\n      clipboardData.setData(k, v);\n    }\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+clipboard@0.28.0/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs\n");

/***/ })

};
;