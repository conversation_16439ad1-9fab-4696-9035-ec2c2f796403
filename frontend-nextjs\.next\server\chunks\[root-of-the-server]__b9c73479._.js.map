{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/app/api/payments/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { auth } from '@clerk/nextjs/server';\n\nconst BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';\n\n/**\n * GET /api/payments - Proxy to backend payments API\n */\nexport async function GET(request: NextRequest) {\n  try {\n    const { userId } = await auth();\n    \n    if (!userId) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    // Get user info from Clerk\n    const user = await fetch(`https://api.clerk.com/v1/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,\n      },\n    }).then(res => res.json());\n\n    // Forward request to backend with authentication headers\n    const url = new URL(request.url);\n    const backendUrl = `${BACKEND_URL}/api/payments${url.search}`;\n    \n    const response = await fetch(backendUrl, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'x-clerk-user-id': userId,\n        'x-user-email': user.email_addresses[0]?.email_address || '',\n      },\n    });\n\n    const data = await response.json();\n    \n    if (!response.ok) {\n      return NextResponse.json(data, { status: response.status });\n    }\n\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error('Error proxying payments request:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * POST /api/payments - Proxy to backend payments API\n */\nexport async function POST(request: NextRequest) {\n  try {\n    const { userId } = await auth();\n    \n    if (!userId) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    // Get user info from Clerk\n    const user = await fetch(`https://api.clerk.com/v1/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,\n      },\n    }).then(res => res.json());\n\n    // Get request body\n    const body = await request.json();\n\n    // Forward request to backend with authentication headers\n    const backendUrl = `${BACKEND_URL}/api/payments`;\n    \n    const response = await fetch(backendUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'x-clerk-user-id': userId,\n        'x-user-email': user.email_addresses[0]?.email_address || '',\n      },\n      body: JSON.stringify(body),\n    });\n\n    const data = await response.json();\n    \n    if (!response.ok) {\n      return NextResponse.json(data, { status: response.status });\n    }\n\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error('Error proxying payments request:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,cAAc,6DAAmC;AAKhD,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,+RAAA,CAAA,OAAI,AAAD;QAE5B,IAAI,CAAC,QAAQ;YACX,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,MAAM,OAAO,MAAM,MAAM,CAAC,+BAA+B,EAAE,QAAQ,EAAE;YACnE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE;YACzD;QACF,GAAG,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;QAEvB,yDAAyD;QACzD,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,aAAa,GAAG,YAAY,aAAa,EAAE,IAAI,MAAM,EAAE;QAE7D,MAAM,WAAW,MAAM,MAAM,YAAY;YACvC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,mBAAmB;gBACnB,gBAAgB,KAAK,eAAe,CAAC,EAAE,EAAE,iBAAiB;YAC5D;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAC3D;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAKO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,+RAAA,CAAA,OAAI,AAAD;QAE5B,IAAI,CAAC,QAAQ;YACX,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,MAAM,OAAO,MAAM,MAAM,CAAC,+BAA+B,EAAE,QAAQ,EAAE;YACnE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE;YACzD;QACF,GAAG,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;QAEvB,mBAAmB;QACnB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,yDAAyD;QACzD,MAAM,aAAa,GAAG,YAAY,aAAa,CAAC;QAEhD,MAAM,WAAW,MAAM,MAAM,YAAY;YACvC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,mBAAmB;gBACnB,gBAAgB,KAAK,eAAe,CAAC,EAAE,EAAE,iBAAiB;YAC5D;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAC3D;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}