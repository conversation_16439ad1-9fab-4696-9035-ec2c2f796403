"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+table@0.28.0";
exports.ids = ["vendor-chunks/@lexical+table@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+table@0.28.0/node_modules/@lexical/table/LexicalTable.dev.mjs":
/*!***************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+table@0.28.0/node_modules/@lexical/table/LexicalTable.dev.mjs ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $computeTableMap: () => (/* binding */ $computeTableMap),\n/* harmony export */   $computeTableMapSkipCellCheck: () => (/* binding */ $computeTableMapSkipCellCheck),\n/* harmony export */   $createTableCellNode: () => (/* binding */ $createTableCellNode),\n/* harmony export */   $createTableNode: () => (/* binding */ $createTableNode),\n/* harmony export */   $createTableNodeWithDimensions: () => (/* binding */ $createTableNodeWithDimensions),\n/* harmony export */   $createTableRowNode: () => (/* binding */ $createTableRowNode),\n/* harmony export */   $createTableSelection: () => (/* binding */ $createTableSelection),\n/* harmony export */   $createTableSelectionFrom: () => (/* binding */ $createTableSelectionFrom),\n/* harmony export */   $deleteTableColumn: () => (/* binding */ $deleteTableColumn),\n/* harmony export */   $deleteTableColumn__EXPERIMENTAL: () => (/* binding */ $deleteTableColumn__EXPERIMENTAL),\n/* harmony export */   $deleteTableRow__EXPERIMENTAL: () => (/* binding */ $deleteTableRow__EXPERIMENTAL),\n/* harmony export */   $findCellNode: () => (/* binding */ $findCellNode),\n/* harmony export */   $findTableNode: () => (/* binding */ $findTableNode),\n/* harmony export */   $getElementForTableNode: () => (/* binding */ $getElementForTableNode),\n/* harmony export */   $getNodeTriplet: () => (/* binding */ $getNodeTriplet),\n/* harmony export */   $getTableAndElementByKey: () => (/* binding */ $getTableAndElementByKey),\n/* harmony export */   $getTableCellNodeFromLexicalNode: () => (/* binding */ $getTableCellNodeFromLexicalNode),\n/* harmony export */   $getTableCellNodeRect: () => (/* binding */ $getTableCellNodeRect),\n/* harmony export */   $getTableColumnIndexFromTableCellNode: () => (/* binding */ $getTableColumnIndexFromTableCellNode),\n/* harmony export */   $getTableNodeFromLexicalNodeOrThrow: () => (/* binding */ $getTableNodeFromLexicalNodeOrThrow),\n/* harmony export */   $getTableRowIndexFromTableCellNode: () => (/* binding */ $getTableRowIndexFromTableCellNode),\n/* harmony export */   $getTableRowNodeFromTableCellNodeOrThrow: () => (/* binding */ $getTableRowNodeFromTableCellNodeOrThrow),\n/* harmony export */   $insertTableColumn: () => (/* binding */ $insertTableColumn),\n/* harmony export */   $insertTableColumn__EXPERIMENTAL: () => (/* binding */ $insertTableColumn__EXPERIMENTAL),\n/* harmony export */   $insertTableRow: () => (/* binding */ $insertTableRow),\n/* harmony export */   $insertTableRow__EXPERIMENTAL: () => (/* binding */ $insertTableRow__EXPERIMENTAL),\n/* harmony export */   $isScrollableTablesActive: () => (/* binding */ $isScrollableTablesActive),\n/* harmony export */   $isTableCellNode: () => (/* binding */ $isTableCellNode),\n/* harmony export */   $isTableNode: () => (/* binding */ $isTableNode),\n/* harmony export */   $isTableRowNode: () => (/* binding */ $isTableRowNode),\n/* harmony export */   $isTableSelection: () => (/* binding */ $isTableSelection),\n/* harmony export */   $removeTableRowAtIndex: () => (/* binding */ $removeTableRowAtIndex),\n/* harmony export */   $unmergeCell: () => (/* binding */ $unmergeCell),\n/* harmony export */   INSERT_TABLE_COMMAND: () => (/* binding */ INSERT_TABLE_COMMAND),\n/* harmony export */   TableCellHeaderStates: () => (/* binding */ TableCellHeaderStates),\n/* harmony export */   TableCellNode: () => (/* binding */ TableCellNode),\n/* harmony export */   TableNode: () => (/* binding */ TableNode),\n/* harmony export */   TableObserver: () => (/* binding */ TableObserver),\n/* harmony export */   TableRowNode: () => (/* binding */ TableRowNode),\n/* harmony export */   applyTableHandlers: () => (/* binding */ applyTableHandlers),\n/* harmony export */   getDOMCellFromTarget: () => (/* binding */ getDOMCellFromTarget),\n/* harmony export */   getTableElement: () => (/* binding */ getTableElement),\n/* harmony export */   getTableObserverFromTableElement: () => (/* binding */ getTableObserverFromTableElement),\n/* harmony export */   registerTableCellUnmergeTransform: () => (/* binding */ registerTableCellUnmergeTransform),\n/* harmony export */   registerTablePlugin: () => (/* binding */ registerTablePlugin),\n/* harmony export */   registerTableSelectionObserver: () => (/* binding */ registerTableSelectionObserver),\n/* harmony export */   setScrollableTablesActive: () => (/* binding */ setScrollableTablesActive)\n/* harmony export */ });\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(rsc)/./node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/clipboard */ \"(rsc)/./node_modules/.pnpm/@lexical+clipboard@0.28.0/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst PIXEL_VALUE_REG_EXP = /^(\\d+(?:\\.\\d+)?)px$/;\n\n// .PlaygroundEditorTheme__tableCell width value from\n// packages/lexical-playground/src/themes/PlaygroundEditorTheme.css\nconst COLUMN_WIDTH = 75;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst TableCellHeaderStates = {\n  BOTH: 3,\n  COLUMN: 2,\n  NO_STATUS: 0,\n  ROW: 1\n};\n/** @noInheritDoc */\nclass TableCellNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  static getType() {\n    return 'tablecell';\n  }\n  static clone(node) {\n    return new TableCellNode(node.__headerState, node.__colSpan, node.__width, node.__key);\n  }\n  afterCloneFrom(node) {\n    super.afterCloneFrom(node);\n    this.__rowSpan = node.__rowSpan;\n    this.__backgroundColor = node.__backgroundColor;\n    this.__verticalAlign = node.__verticalAlign;\n  }\n  static importDOM() {\n    return {\n      td: node => ({\n        conversion: $convertTableCellNodeElement,\n        priority: 0\n      }),\n      th: node => ({\n        conversion: $convertTableCellNodeElement,\n        priority: 0\n      })\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createTableCellNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setHeaderStyles(serializedNode.headerState).setColSpan(serializedNode.colSpan || 1).setRowSpan(serializedNode.rowSpan || 1).setWidth(serializedNode.width || undefined).setBackgroundColor(serializedNode.backgroundColor || null).setVerticalAlign(serializedNode.verticalAlign || undefined);\n  }\n  constructor(headerState = TableCellHeaderStates.NO_STATUS, colSpan = 1, width, key) {\n    super(key);\n    this.__colSpan = colSpan;\n    this.__rowSpan = 1;\n    this.__headerState = headerState;\n    this.__width = width;\n    this.__backgroundColor = null;\n    this.__verticalAlign = undefined;\n  }\n  createDOM(config) {\n    const element = document.createElement(this.getTag());\n    if (this.__width) {\n      element.style.width = `${this.__width}px`;\n    }\n    if (this.__colSpan > 1) {\n      element.colSpan = this.__colSpan;\n    }\n    if (this.__rowSpan > 1) {\n      element.rowSpan = this.__rowSpan;\n    }\n    if (this.__backgroundColor !== null) {\n      element.style.backgroundColor = this.__backgroundColor;\n    }\n    if (isValidVerticalAlign(this.__verticalAlign)) {\n      element.style.verticalAlign = this.__verticalAlign;\n    }\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, config.theme.tableCell, this.hasHeader() && config.theme.tableCellHeader);\n    return element;\n  }\n  exportDOM(editor) {\n    const output = super.exportDOM(editor);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(output.element)) {\n      const element = output.element;\n      element.setAttribute('data-temporary-table-cell-lexical-key', this.getKey());\n      element.style.border = '1px solid black';\n      if (this.__colSpan > 1) {\n        element.colSpan = this.__colSpan;\n      }\n      if (this.__rowSpan > 1) {\n        element.rowSpan = this.__rowSpan;\n      }\n      element.style.width = `${this.getWidth() || COLUMN_WIDTH}px`;\n      element.style.verticalAlign = this.getVerticalAlign() || 'top';\n      element.style.textAlign = 'start';\n      if (this.__backgroundColor === null && this.hasHeader()) {\n        element.style.backgroundColor = '#f2f3f5';\n      }\n    }\n    return output;\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      ...(isValidVerticalAlign(this.__verticalAlign) && {\n        verticalAlign: this.__verticalAlign\n      }),\n      backgroundColor: this.getBackgroundColor(),\n      colSpan: this.__colSpan,\n      headerState: this.__headerState,\n      rowSpan: this.__rowSpan,\n      width: this.getWidth()\n    };\n  }\n  getColSpan() {\n    return this.getLatest().__colSpan;\n  }\n  setColSpan(colSpan) {\n    const self = this.getWritable();\n    self.__colSpan = colSpan;\n    return self;\n  }\n  getRowSpan() {\n    return this.getLatest().__rowSpan;\n  }\n  setRowSpan(rowSpan) {\n    const self = this.getWritable();\n    self.__rowSpan = rowSpan;\n    return self;\n  }\n  getTag() {\n    return this.hasHeader() ? 'th' : 'td';\n  }\n  setHeaderStyles(headerState, mask = TableCellHeaderStates.BOTH) {\n    const self = this.getWritable();\n    self.__headerState = headerState & mask | self.__headerState & ~mask;\n    return self;\n  }\n  getHeaderStyles() {\n    return this.getLatest().__headerState;\n  }\n  setWidth(width) {\n    const self = this.getWritable();\n    self.__width = width;\n    return self;\n  }\n  getWidth() {\n    return this.getLatest().__width;\n  }\n  getBackgroundColor() {\n    return this.getLatest().__backgroundColor;\n  }\n  setBackgroundColor(newBackgroundColor) {\n    const self = this.getWritable();\n    self.__backgroundColor = newBackgroundColor;\n    return self;\n  }\n  getVerticalAlign() {\n    return this.getLatest().__verticalAlign;\n  }\n  setVerticalAlign(newVerticalAlign) {\n    const self = this.getWritable();\n    self.__verticalAlign = newVerticalAlign || undefined;\n    return self;\n  }\n  toggleHeaderStyle(headerStateToToggle) {\n    const self = this.getWritable();\n    if ((self.__headerState & headerStateToToggle) === headerStateToToggle) {\n      self.__headerState -= headerStateToToggle;\n    } else {\n      self.__headerState += headerStateToToggle;\n    }\n    return self;\n  }\n  hasHeaderState(headerState) {\n    return (this.getHeaderStyles() & headerState) === headerState;\n  }\n  hasHeader() {\n    return this.getLatest().__headerState !== TableCellHeaderStates.NO_STATUS;\n  }\n  updateDOM(prevNode) {\n    return prevNode.__headerState !== this.__headerState || prevNode.__width !== this.__width || prevNode.__colSpan !== this.__colSpan || prevNode.__rowSpan !== this.__rowSpan || prevNode.__backgroundColor !== this.__backgroundColor || prevNode.__verticalAlign !== this.__verticalAlign;\n  }\n  isShadowRoot() {\n    return true;\n  }\n  collapseAtStart() {\n    return true;\n  }\n  canBeEmpty() {\n    return false;\n  }\n  canIndent() {\n    return false;\n  }\n}\nfunction isValidVerticalAlign(verticalAlign) {\n  return verticalAlign === 'middle' || verticalAlign === 'bottom';\n}\nfunction $convertTableCellNodeElement(domNode) {\n  const domNode_ = domNode;\n  const nodeName = domNode.nodeName.toLowerCase();\n  let width = undefined;\n  if (PIXEL_VALUE_REG_EXP.test(domNode_.style.width)) {\n    width = parseFloat(domNode_.style.width);\n  }\n  const tableCellNode = $createTableCellNode(nodeName === 'th' ? TableCellHeaderStates.ROW : TableCellHeaderStates.NO_STATUS, domNode_.colSpan, width);\n  tableCellNode.__rowSpan = domNode_.rowSpan;\n  const backgroundColor = domNode_.style.backgroundColor;\n  if (backgroundColor !== '') {\n    tableCellNode.__backgroundColor = backgroundColor;\n  }\n  const verticalAlign = domNode_.style.verticalAlign;\n  if (isValidVerticalAlign(verticalAlign)) {\n    tableCellNode.__verticalAlign = verticalAlign;\n  }\n  const style = domNode_.style;\n  const textDecoration = (style && style.textDecoration || '').split(' ');\n  const hasBoldFontWeight = style.fontWeight === '700' || style.fontWeight === 'bold';\n  const hasLinethroughTextDecoration = textDecoration.includes('line-through');\n  const hasItalicFontStyle = style.fontStyle === 'italic';\n  const hasUnderlineTextDecoration = textDecoration.includes('underline');\n  return {\n    after: childLexicalNodes => {\n      const result = [];\n      let paragraphNode = null;\n      const removeSingleLineBreakNode = () => {\n        if (paragraphNode) {\n          const firstChild = paragraphNode.getFirstChild();\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isLineBreakNode)(firstChild) && paragraphNode.getChildrenSize() === 1) {\n            firstChild.remove();\n          }\n        }\n      };\n      for (const child of childLexicalNodes) {\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isInlineElementOrDecoratorNode)(child) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(child) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isLineBreakNode)(child)) {\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(child)) {\n            if (hasBoldFontWeight) {\n              child.toggleFormat('bold');\n            }\n            if (hasLinethroughTextDecoration) {\n              child.toggleFormat('strikethrough');\n            }\n            if (hasItalicFontStyle) {\n              child.toggleFormat('italic');\n            }\n            if (hasUnderlineTextDecoration) {\n              child.toggleFormat('underline');\n            }\n          }\n          if (paragraphNode) {\n            paragraphNode.append(child);\n          } else {\n            paragraphNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)().append(child);\n            result.push(paragraphNode);\n          }\n        } else {\n          result.push(child);\n          removeSingleLineBreakNode();\n          paragraphNode = null;\n        }\n      }\n      removeSingleLineBreakNode();\n      if (result.length === 0) {\n        result.push((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n      }\n      return result;\n    },\n    node: tableCellNode\n  };\n}\nfunction $createTableCellNode(headerState = TableCellHeaderStates.NO_STATUS, colSpan = 1, width) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new TableCellNode(headerState, colSpan, width));\n}\nfunction $isTableCellNode(node) {\n  return node instanceof TableCellNode;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst INSERT_TABLE_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('INSERT_TABLE_COMMAND');\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/** @noInheritDoc */\nclass TableRowNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  static getType() {\n    return 'tablerow';\n  }\n  static clone(node) {\n    return new TableRowNode(node.__height, node.__key);\n  }\n  static importDOM() {\n    return {\n      tr: node => ({\n        conversion: $convertTableRowElement,\n        priority: 0\n      })\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createTableRowNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setHeight(serializedNode.height);\n  }\n  constructor(height, key) {\n    super(key);\n    this.__height = height;\n  }\n  exportJSON() {\n    const height = this.getHeight();\n    return {\n      ...super.exportJSON(),\n      ...(height === undefined ? undefined : {\n        height\n      })\n    };\n  }\n  createDOM(config) {\n    const element = document.createElement('tr');\n    if (this.__height) {\n      element.style.height = `${this.__height}px`;\n    }\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, config.theme.tableRow);\n    return element;\n  }\n  extractWithChild(child, selection, destination) {\n    return destination === 'html';\n  }\n  isShadowRoot() {\n    return true;\n  }\n  setHeight(height) {\n    const self = this.getWritable();\n    self.__height = height;\n    return self;\n  }\n  getHeight() {\n    return this.getLatest().__height;\n  }\n  updateDOM(prevNode) {\n    return prevNode.__height !== this.__height;\n  }\n  canBeEmpty() {\n    return false;\n  }\n  canIndent() {\n    return false;\n  }\n}\nfunction $convertTableRowElement(domNode) {\n  const domNode_ = domNode;\n  let height = undefined;\n  if (PIXEL_VALUE_REG_EXP.test(domNode_.style.height)) {\n    height = parseFloat(domNode_.style.height);\n  }\n  return {\n    after: children => (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$descendantsMatching)(children, $isTableCellNode),\n    node: $createTableRowNode(height)\n  };\n}\nfunction $createTableRowNode(height) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new TableRowNode(height));\n}\nfunction $isTableRowNode(node) {\n  return node instanceof TableRowNode;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst documentMode = CAN_USE_DOM && 'documentMode' in document ? document.documentMode : null;\nconst IS_FIREFOX = CAN_USE_DOM && /^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent);\nCAN_USE_DOM && 'InputEvent' in window && !documentMode ? 'getTargetRanges' in new window.InputEvent('input') : false;\n\nfunction $createTableNodeWithDimensions(rowCount, columnCount, includeHeaders = true) {\n  const tableNode = $createTableNode();\n  for (let iRow = 0; iRow < rowCount; iRow++) {\n    const tableRowNode = $createTableRowNode();\n    for (let iColumn = 0; iColumn < columnCount; iColumn++) {\n      let headerState = TableCellHeaderStates.NO_STATUS;\n      if (typeof includeHeaders === 'object') {\n        if (iRow === 0 && includeHeaders.rows) {\n          headerState |= TableCellHeaderStates.ROW;\n        }\n        if (iColumn === 0 && includeHeaders.columns) {\n          headerState |= TableCellHeaderStates.COLUMN;\n        }\n      } else if (includeHeaders) {\n        if (iRow === 0) {\n          headerState |= TableCellHeaderStates.ROW;\n        }\n        if (iColumn === 0) {\n          headerState |= TableCellHeaderStates.COLUMN;\n        }\n      }\n      const tableCellNode = $createTableCellNode(headerState);\n      const paragraphNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n      paragraphNode.append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)());\n      tableCellNode.append(paragraphNode);\n      tableRowNode.append(tableCellNode);\n    }\n    tableNode.append(tableRowNode);\n  }\n  return tableNode;\n}\nfunction $getTableCellNodeFromLexicalNode(startingNode) {\n  const node = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(startingNode, n => $isTableCellNode(n));\n  if ($isTableCellNode(node)) {\n    return node;\n  }\n  return null;\n}\nfunction $getTableRowNodeFromTableCellNodeOrThrow(startingNode) {\n  const node = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(startingNode, n => $isTableRowNode(n));\n  if ($isTableRowNode(node)) {\n    return node;\n  }\n  throw new Error('Expected table cell to be inside of table row.');\n}\nfunction $getTableNodeFromLexicalNodeOrThrow(startingNode) {\n  const node = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(startingNode, n => $isTableNode(n));\n  if ($isTableNode(node)) {\n    return node;\n  }\n  throw new Error('Expected table cell to be inside of table.');\n}\nfunction $getTableRowIndexFromTableCellNode(tableCellNode) {\n  const tableRowNode = $getTableRowNodeFromTableCellNodeOrThrow(tableCellNode);\n  const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableRowNode);\n  return tableNode.getChildren().findIndex(n => n.is(tableRowNode));\n}\nfunction $getTableColumnIndexFromTableCellNode(tableCellNode) {\n  const tableRowNode = $getTableRowNodeFromTableCellNodeOrThrow(tableCellNode);\n  return tableRowNode.getChildren().findIndex(n => n.is(tableCellNode));\n}\nfunction $getTableCellSiblingsFromTableCellNode(tableCellNode, table) {\n  const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);\n  const {\n    x,\n    y\n  } = tableNode.getCordsFromCellNode(tableCellNode, table);\n  return {\n    above: tableNode.getCellNodeFromCords(x, y - 1, table),\n    below: tableNode.getCellNodeFromCords(x, y + 1, table),\n    left: tableNode.getCellNodeFromCords(x - 1, y, table),\n    right: tableNode.getCellNodeFromCords(x + 1, y, table)\n  };\n}\nfunction $removeTableRowAtIndex(tableNode, indexToDelete) {\n  const tableRows = tableNode.getChildren();\n  if (indexToDelete >= tableRows.length || indexToDelete < 0) {\n    throw new Error('Expected table cell to be inside of table row.');\n  }\n  const targetRowNode = tableRows[indexToDelete];\n  targetRowNode.remove();\n  return tableNode;\n}\nfunction $insertTableRow(tableNode, targetIndex, shouldInsertAfter = true, rowCount, table) {\n  const tableRows = tableNode.getChildren();\n  if (targetIndex >= tableRows.length || targetIndex < 0) {\n    throw new Error('Table row target index out of range');\n  }\n  const targetRowNode = tableRows[targetIndex];\n  if ($isTableRowNode(targetRowNode)) {\n    for (let r = 0; r < rowCount; r++) {\n      const tableRowCells = targetRowNode.getChildren();\n      const tableColumnCount = tableRowCells.length;\n      const newTableRowNode = $createTableRowNode();\n      for (let c = 0; c < tableColumnCount; c++) {\n        const tableCellFromTargetRow = tableRowCells[c];\n        if (!$isTableCellNode(tableCellFromTargetRow)) {\n          formatDevErrorMessage(`Expected table cell`);\n        }\n        const {\n          above,\n          below\n        } = $getTableCellSiblingsFromTableCellNode(tableCellFromTargetRow, table);\n        let headerState = TableCellHeaderStates.NO_STATUS;\n        const width = above && above.getWidth() || below && below.getWidth() || undefined;\n        if (above && above.hasHeaderState(TableCellHeaderStates.COLUMN) || below && below.hasHeaderState(TableCellHeaderStates.COLUMN)) {\n          headerState |= TableCellHeaderStates.COLUMN;\n        }\n        const tableCellNode = $createTableCellNode(headerState, 1, width);\n        tableCellNode.append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n        newTableRowNode.append(tableCellNode);\n      }\n      if (shouldInsertAfter) {\n        targetRowNode.insertAfter(newTableRowNode);\n      } else {\n        targetRowNode.insertBefore(newTableRowNode);\n      }\n    }\n  } else {\n    throw new Error('Row before insertion index does not exist.');\n  }\n  return tableNode;\n}\nconst getHeaderState = (currentState, possibleState) => {\n  if (currentState === TableCellHeaderStates.BOTH || currentState === possibleState) {\n    return possibleState;\n  }\n  return TableCellHeaderStates.NO_STATUS;\n};\n\n/**\n * Inserts a table row before or after the current focus cell node,\n * taking into account any spans. If successful, returns the\n * inserted table row node.\n */\nfunction $insertTableRow__EXPERIMENTAL(insertAfter = true) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection))) {\n    formatDevErrorMessage(`Expected a RangeSelection or TableSelection`);\n  }\n  const anchor = selection.anchor.getNode();\n  const focus = selection.focus.getNode();\n  const [anchorCell] = $getNodeTriplet(anchor);\n  const [focusCell,, grid] = $getNodeTriplet(focus);\n  const [gridMap, focusCellMap, anchorCellMap] = $computeTableMap(grid, focusCell, anchorCell);\n  const columnCount = gridMap[0].length;\n  const {\n    startRow: anchorStartRow\n  } = anchorCellMap;\n  const {\n    startRow: focusStartRow\n  } = focusCellMap;\n  let insertedRow = null;\n  if (insertAfter) {\n    const insertAfterEndRow = Math.max(focusStartRow + focusCell.__rowSpan, anchorStartRow + anchorCell.__rowSpan) - 1;\n    const insertAfterEndRowMap = gridMap[insertAfterEndRow];\n    const newRow = $createTableRowNode();\n    for (let i = 0; i < columnCount; i++) {\n      const {\n        cell,\n        startRow\n      } = insertAfterEndRowMap[i];\n      if (startRow + cell.__rowSpan - 1 <= insertAfterEndRow) {\n        const currentCell = insertAfterEndRowMap[i].cell;\n        const currentCellHeaderState = currentCell.__headerState;\n        const headerState = getHeaderState(currentCellHeaderState, TableCellHeaderStates.COLUMN);\n        newRow.append($createTableCellNode(headerState).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)()));\n      } else {\n        cell.setRowSpan(cell.__rowSpan + 1);\n      }\n    }\n    const insertAfterEndRowNode = grid.getChildAtIndex(insertAfterEndRow);\n    if (!$isTableRowNode(insertAfterEndRowNode)) {\n      formatDevErrorMessage(`insertAfterEndRow is not a TableRowNode`);\n    }\n    insertAfterEndRowNode.insertAfter(newRow);\n    insertedRow = newRow;\n  } else {\n    const insertBeforeStartRow = Math.min(focusStartRow, anchorStartRow);\n    const insertBeforeStartRowMap = gridMap[insertBeforeStartRow];\n    const newRow = $createTableRowNode();\n    for (let i = 0; i < columnCount; i++) {\n      const {\n        cell,\n        startRow\n      } = insertBeforeStartRowMap[i];\n      if (startRow === insertBeforeStartRow) {\n        const currentCell = insertBeforeStartRowMap[i].cell;\n        const currentCellHeaderState = currentCell.__headerState;\n        const headerState = getHeaderState(currentCellHeaderState, TableCellHeaderStates.COLUMN);\n        newRow.append($createTableCellNode(headerState).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)()));\n      } else {\n        cell.setRowSpan(cell.__rowSpan + 1);\n      }\n    }\n    const insertBeforeStartRowNode = grid.getChildAtIndex(insertBeforeStartRow);\n    if (!$isTableRowNode(insertBeforeStartRowNode)) {\n      formatDevErrorMessage(`insertBeforeStartRow is not a TableRowNode`);\n    }\n    insertBeforeStartRowNode.insertBefore(newRow);\n    insertedRow = newRow;\n  }\n  return insertedRow;\n}\nfunction $insertTableColumn(tableNode, targetIndex, shouldInsertAfter = true, columnCount, table) {\n  const tableRows = tableNode.getChildren();\n  const tableCellsToBeInserted = [];\n  for (let r = 0; r < tableRows.length; r++) {\n    const currentTableRowNode = tableRows[r];\n    if ($isTableRowNode(currentTableRowNode)) {\n      for (let c = 0; c < columnCount; c++) {\n        const tableRowChildren = currentTableRowNode.getChildren();\n        if (targetIndex >= tableRowChildren.length || targetIndex < 0) {\n          throw new Error('Table column target index out of range');\n        }\n        const targetCell = tableRowChildren[targetIndex];\n        if (!$isTableCellNode(targetCell)) {\n          formatDevErrorMessage(`Expected table cell`);\n        }\n        const {\n          left,\n          right\n        } = $getTableCellSiblingsFromTableCellNode(targetCell, table);\n        let headerState = TableCellHeaderStates.NO_STATUS;\n        if (left && left.hasHeaderState(TableCellHeaderStates.ROW) || right && right.hasHeaderState(TableCellHeaderStates.ROW)) {\n          headerState |= TableCellHeaderStates.ROW;\n        }\n        const newTableCell = $createTableCellNode(headerState);\n        newTableCell.append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n        tableCellsToBeInserted.push({\n          newTableCell,\n          targetCell\n        });\n      }\n    }\n  }\n  tableCellsToBeInserted.forEach(({\n    newTableCell,\n    targetCell\n  }) => {\n    if (shouldInsertAfter) {\n      targetCell.insertAfter(newTableCell);\n    } else {\n      targetCell.insertBefore(newTableCell);\n    }\n  });\n  return tableNode;\n}\n\n/**\n * Inserts a column before or after the current focus cell node,\n * taking into account any spans. If successful, returns the\n * first inserted cell node.\n */\nfunction $insertTableColumn__EXPERIMENTAL(insertAfter = true) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection))) {\n    formatDevErrorMessage(`Expected a RangeSelection or TableSelection`);\n  }\n  const anchor = selection.anchor.getNode();\n  const focus = selection.focus.getNode();\n  const [anchorCell] = $getNodeTriplet(anchor);\n  const [focusCell,, grid] = $getNodeTriplet(focus);\n  const [gridMap, focusCellMap, anchorCellMap] = $computeTableMap(grid, focusCell, anchorCell);\n  const rowCount = gridMap.length;\n  const startColumn = insertAfter ? Math.max(focusCellMap.startColumn, anchorCellMap.startColumn) : Math.min(focusCellMap.startColumn, anchorCellMap.startColumn);\n  const insertAfterColumn = insertAfter ? startColumn + focusCell.__colSpan - 1 : startColumn - 1;\n  const gridFirstChild = grid.getFirstChild();\n  if (!$isTableRowNode(gridFirstChild)) {\n    formatDevErrorMessage(`Expected firstTable child to be a row`);\n  }\n  let firstInsertedCell = null;\n  function $createTableCellNodeForInsertTableColumn(headerState = TableCellHeaderStates.NO_STATUS) {\n    const cell = $createTableCellNode(headerState).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n    if (firstInsertedCell === null) {\n      firstInsertedCell = cell;\n    }\n    return cell;\n  }\n  let loopRow = gridFirstChild;\n  rowLoop: for (let i = 0; i < rowCount; i++) {\n    if (i !== 0) {\n      const currentRow = loopRow.getNextSibling();\n      if (!$isTableRowNode(currentRow)) {\n        formatDevErrorMessage(`Expected row nextSibling to be a row`);\n      }\n      loopRow = currentRow;\n    }\n    const rowMap = gridMap[i];\n    const currentCellHeaderState = rowMap[insertAfterColumn < 0 ? 0 : insertAfterColumn].cell.__headerState;\n    const headerState = getHeaderState(currentCellHeaderState, TableCellHeaderStates.ROW);\n    if (insertAfterColumn < 0) {\n      $insertFirst(loopRow, $createTableCellNodeForInsertTableColumn(headerState));\n      continue;\n    }\n    const {\n      cell: currentCell,\n      startColumn: currentStartColumn,\n      startRow: currentStartRow\n    } = rowMap[insertAfterColumn];\n    if (currentStartColumn + currentCell.__colSpan - 1 <= insertAfterColumn) {\n      let insertAfterCell = currentCell;\n      let insertAfterCellRowStart = currentStartRow;\n      let prevCellIndex = insertAfterColumn;\n      while (insertAfterCellRowStart !== i && insertAfterCell.__rowSpan > 1) {\n        prevCellIndex -= currentCell.__colSpan;\n        if (prevCellIndex >= 0) {\n          const {\n            cell: cell_,\n            startRow: startRow_\n          } = rowMap[prevCellIndex];\n          insertAfterCell = cell_;\n          insertAfterCellRowStart = startRow_;\n        } else {\n          loopRow.append($createTableCellNodeForInsertTableColumn(headerState));\n          continue rowLoop;\n        }\n      }\n      insertAfterCell.insertAfter($createTableCellNodeForInsertTableColumn(headerState));\n    } else {\n      currentCell.setColSpan(currentCell.__colSpan + 1);\n    }\n  }\n  if (firstInsertedCell !== null) {\n    $moveSelectionToCell(firstInsertedCell);\n  }\n  const colWidths = grid.getColWidths();\n  if (colWidths) {\n    const newColWidths = [...colWidths];\n    const columnIndex = insertAfterColumn < 0 ? 0 : insertAfterColumn;\n    const newWidth = newColWidths[columnIndex];\n    newColWidths.splice(columnIndex, 0, newWidth);\n    grid.setColWidths(newColWidths);\n  }\n  return firstInsertedCell;\n}\nfunction $deleteTableColumn(tableNode, targetIndex) {\n  const tableRows = tableNode.getChildren();\n  for (let i = 0; i < tableRows.length; i++) {\n    const currentTableRowNode = tableRows[i];\n    if ($isTableRowNode(currentTableRowNode)) {\n      const tableRowChildren = currentTableRowNode.getChildren();\n      if (targetIndex >= tableRowChildren.length || targetIndex < 0) {\n        throw new Error('Table column target index out of range');\n      }\n      tableRowChildren[targetIndex].remove();\n    }\n  }\n  return tableNode;\n}\nfunction $deleteTableRow__EXPERIMENTAL() {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection))) {\n    formatDevErrorMessage(`Expected a RangeSelection or TableSelection`);\n  }\n  const [anchor, focus] = selection.isBackward() ? [selection.focus.getNode(), selection.anchor.getNode()] : [selection.anchor.getNode(), selection.focus.getNode()];\n  const [anchorCell,, grid] = $getNodeTriplet(anchor);\n  const [focusCell] = $getNodeTriplet(focus);\n  const [gridMap, anchorCellMap, focusCellMap] = $computeTableMap(grid, anchorCell, focusCell);\n  const {\n    startRow: anchorStartRow\n  } = anchorCellMap;\n  const {\n    startRow: focusStartRow\n  } = focusCellMap;\n  const focusEndRow = focusStartRow + focusCell.__rowSpan - 1;\n  if (gridMap.length === focusEndRow - anchorStartRow + 1) {\n    // Empty grid\n    grid.remove();\n    return;\n  }\n  const columnCount = gridMap[0].length;\n  const selectedRowCount = anchorCell.__rowSpan;\n  const nextRow = gridMap[focusEndRow + 1];\n  const nextRowNode = grid.getChildAtIndex(focusEndRow + 1);\n  for (let row = focusEndRow; row >= anchorStartRow; row--) {\n    for (let column = columnCount - 1; column >= 0; column--) {\n      const {\n        cell,\n        startRow: cellStartRow,\n        startColumn: cellStartColumn\n      } = gridMap[row][column];\n      if (cellStartColumn !== column) {\n        // Don't repeat work for the same Cell\n        continue;\n      }\n      // Rows overflowing top have to be trimmed\n      if (row === anchorStartRow && cellStartRow < anchorStartRow) {\n        const overflowTop = anchorStartRow - cellStartRow;\n        cell.setRowSpan(cell.__rowSpan - Math.min(selectedRowCount, cell.__rowSpan - overflowTop));\n      }\n      // Rows overflowing bottom have to be trimmed and moved to the next row\n      if (cellStartRow >= anchorStartRow && cellStartRow + cell.__rowSpan - 1 > focusEndRow) {\n        cell.setRowSpan(cell.__rowSpan - (focusEndRow - cellStartRow + 1));\n        if (!(nextRowNode !== null)) {\n          formatDevErrorMessage(`Expected nextRowNode not to be null`);\n        }\n        let insertAfterCell = null;\n        for (let columnIndex = 0; columnIndex < column; columnIndex++) {\n          const currentCellMap = nextRow[columnIndex];\n          const currentCell = currentCellMap.cell;\n          // Checking the cell having startRow as same as nextRow\n          if (currentCellMap.startRow === row + 1) {\n            insertAfterCell = currentCell;\n          }\n          if (currentCell.__colSpan > 1) {\n            columnIndex += currentCell.__colSpan - 1;\n          }\n        }\n        if (insertAfterCell === null) {\n          $insertFirst(nextRowNode, cell);\n        } else {\n          insertAfterCell.insertAfter(cell);\n        }\n      }\n    }\n    const rowNode = grid.getChildAtIndex(row);\n    if (!$isTableRowNode(rowNode)) {\n      formatDevErrorMessage(`Expected TableNode childAtIndex(${String(row)}) to be RowNode`);\n    }\n    rowNode.remove();\n  }\n  if (nextRow !== undefined) {\n    const {\n      cell\n    } = nextRow[0];\n    $moveSelectionToCell(cell);\n  } else {\n    const previousRow = gridMap[anchorStartRow - 1];\n    const {\n      cell\n    } = previousRow[0];\n    $moveSelectionToCell(cell);\n  }\n}\nfunction $deleteTableColumn__EXPERIMENTAL() {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection))) {\n    formatDevErrorMessage(`Expected a RangeSelection or TableSelection`);\n  }\n  const anchor = selection.anchor.getNode();\n  const focus = selection.focus.getNode();\n  const [anchorCell,, grid] = $getNodeTriplet(anchor);\n  const [focusCell] = $getNodeTriplet(focus);\n  const [gridMap, anchorCellMap, focusCellMap] = $computeTableMap(grid, anchorCell, focusCell);\n  const {\n    startColumn: anchorStartColumn\n  } = anchorCellMap;\n  const {\n    startRow: focusStartRow,\n    startColumn: focusStartColumn\n  } = focusCellMap;\n  const startColumn = Math.min(anchorStartColumn, focusStartColumn);\n  const endColumn = Math.max(anchorStartColumn + anchorCell.__colSpan - 1, focusStartColumn + focusCell.__colSpan - 1);\n  const selectedColumnCount = endColumn - startColumn + 1;\n  const columnCount = gridMap[0].length;\n  if (columnCount === endColumn - startColumn + 1) {\n    // Empty grid\n    grid.selectPrevious();\n    grid.remove();\n    return;\n  }\n  const rowCount = gridMap.length;\n  for (let row = 0; row < rowCount; row++) {\n    for (let column = startColumn; column <= endColumn; column++) {\n      const {\n        cell,\n        startColumn: cellStartColumn\n      } = gridMap[row][column];\n      if (cellStartColumn < startColumn) {\n        if (column === startColumn) {\n          const overflowLeft = startColumn - cellStartColumn;\n          // Overflowing left\n          cell.setColSpan(cell.__colSpan -\n          // Possible overflow right too\n          Math.min(selectedColumnCount, cell.__colSpan - overflowLeft));\n        }\n      } else if (cellStartColumn + cell.__colSpan - 1 > endColumn) {\n        if (column === endColumn) {\n          // Overflowing right\n          const inSelectedArea = endColumn - cellStartColumn + 1;\n          cell.setColSpan(cell.__colSpan - inSelectedArea);\n        }\n      } else {\n        cell.remove();\n      }\n    }\n  }\n  const focusRowMap = gridMap[focusStartRow];\n  const nextColumn = anchorStartColumn > focusStartColumn ? focusRowMap[anchorStartColumn + anchorCell.__colSpan] : focusRowMap[focusStartColumn + focusCell.__colSpan];\n  if (nextColumn !== undefined) {\n    const {\n      cell\n    } = nextColumn;\n    $moveSelectionToCell(cell);\n  } else {\n    const previousRow = focusStartColumn < anchorStartColumn ? focusRowMap[focusStartColumn - 1] : focusRowMap[anchorStartColumn - 1];\n    const {\n      cell\n    } = previousRow;\n    $moveSelectionToCell(cell);\n  }\n  const colWidths = grid.getColWidths();\n  if (colWidths) {\n    const newColWidths = [...colWidths];\n    newColWidths.splice(startColumn, selectedColumnCount);\n    grid.setColWidths(newColWidths);\n  }\n}\nfunction $moveSelectionToCell(cell) {\n  const firstDescendant = cell.getFirstDescendant();\n  if (firstDescendant == null) {\n    cell.selectStart();\n  } else {\n    firstDescendant.getParentOrThrow().selectStart();\n  }\n}\nfunction $insertFirst(parent, node) {\n  const firstChild = parent.getFirstChild();\n  if (firstChild !== null) {\n    firstChild.insertBefore(node);\n  } else {\n    parent.append(node);\n  }\n}\nfunction $unmergeCell() {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection))) {\n    formatDevErrorMessage(`Expected a RangeSelection or TableSelection`);\n  }\n  const anchor = selection.anchor.getNode();\n  const [cell, row, grid] = $getNodeTriplet(anchor);\n  const colSpan = cell.__colSpan;\n  const rowSpan = cell.__rowSpan;\n  if (colSpan === 1 && rowSpan === 1) {\n    return;\n  }\n  const [map, cellMap] = $computeTableMap(grid, cell, cell);\n  const {\n    startColumn,\n    startRow\n  } = cellMap;\n  // Create a heuristic for what the style of the unmerged cells should be\n  // based on whether every row or column already had that state before the\n  // unmerge.\n  const baseColStyle = cell.__headerState & TableCellHeaderStates.COLUMN;\n  const colStyles = Array.from({\n    length: colSpan\n  }, (_v, i) => {\n    let colStyle = baseColStyle;\n    for (let rowIdx = 0; colStyle !== 0 && rowIdx < map.length; rowIdx++) {\n      colStyle &= map[rowIdx][i + startColumn].cell.__headerState;\n    }\n    return colStyle;\n  });\n  const baseRowStyle = cell.__headerState & TableCellHeaderStates.ROW;\n  const rowStyles = Array.from({\n    length: rowSpan\n  }, (_v, i) => {\n    let rowStyle = baseRowStyle;\n    for (let colIdx = 0; rowStyle !== 0 && colIdx < map[0].length; colIdx++) {\n      rowStyle &= map[i + startRow][colIdx].cell.__headerState;\n    }\n    return rowStyle;\n  });\n  if (colSpan > 1) {\n    for (let i = 1; i < colSpan; i++) {\n      cell.insertAfter($createTableCellNode(colStyles[i] | rowStyles[0]).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)()));\n    }\n    cell.setColSpan(1);\n  }\n  if (rowSpan > 1) {\n    let currentRowNode;\n    for (let i = 1; i < rowSpan; i++) {\n      const currentRow = startRow + i;\n      const currentRowMap = map[currentRow];\n      currentRowNode = (currentRowNode || row).getNextSibling();\n      if (!$isTableRowNode(currentRowNode)) {\n        formatDevErrorMessage(`Expected row next sibling to be a row`);\n      }\n      let insertAfterCell = null;\n      for (let column = 0; column < startColumn; column++) {\n        const currentCellMap = currentRowMap[column];\n        const currentCell = currentCellMap.cell;\n        if (currentCellMap.startRow === currentRow) {\n          insertAfterCell = currentCell;\n        }\n        if (currentCell.__colSpan > 1) {\n          column += currentCell.__colSpan - 1;\n        }\n      }\n      if (insertAfterCell === null) {\n        for (let j = colSpan - 1; j >= 0; j--) {\n          $insertFirst(currentRowNode, $createTableCellNode(colStyles[j] | rowStyles[i]).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)()));\n        }\n      } else {\n        for (let j = colSpan - 1; j >= 0; j--) {\n          insertAfterCell.insertAfter($createTableCellNode(colStyles[j] | rowStyles[i]).append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)()));\n        }\n      }\n    }\n    cell.setRowSpan(1);\n  }\n}\nfunction $computeTableMap(tableNode, cellA, cellB) {\n  const [tableMap, cellAValue, cellBValue] = $computeTableMapSkipCellCheck(tableNode, cellA, cellB);\n  if (!(cellAValue !== null)) {\n    formatDevErrorMessage(`Anchor not found in Table`);\n  }\n  if (!(cellBValue !== null)) {\n    formatDevErrorMessage(`Focus not found in Table`);\n  }\n  return [tableMap, cellAValue, cellBValue];\n}\nfunction $computeTableMapSkipCellCheck(tableNode, cellA, cellB) {\n  const tableMap = [];\n  let cellAValue = null;\n  let cellBValue = null;\n  function getMapRow(i) {\n    let row = tableMap[i];\n    if (row === undefined) {\n      tableMap[i] = row = [];\n    }\n    return row;\n  }\n  const gridChildren = tableNode.getChildren();\n  for (let rowIdx = 0; rowIdx < gridChildren.length; rowIdx++) {\n    const row = gridChildren[rowIdx];\n    if (!$isTableRowNode(row)) {\n      formatDevErrorMessage(`Expected TableNode children to be TableRowNode`);\n    }\n    const startMapRow = getMapRow(rowIdx);\n    for (let cell = row.getFirstChild(), colIdx = 0; cell != null; cell = cell.getNextSibling()) {\n      if (!$isTableCellNode(cell)) {\n        formatDevErrorMessage(`Expected TableRowNode children to be TableCellNode`);\n      } // Skip past any columns that were merged from a higher row\n      while (startMapRow[colIdx] !== undefined) {\n        colIdx++;\n      }\n      const value = {\n        cell,\n        startColumn: colIdx,\n        startRow: rowIdx\n      };\n      const {\n        __rowSpan: rowSpan,\n        __colSpan: colSpan\n      } = cell;\n      for (let j = 0; j < rowSpan; j++) {\n        if (rowIdx + j >= gridChildren.length) {\n          // The table is non-rectangular with a rowSpan\n          // below the last <tr> in the table.\n          // We should probably handle this with a node transform\n          // to ensure that tables are always rectangular but this\n          // will avoid crashes such as #6584\n          // Note that there are probably still latent bugs\n          // regarding colSpan or general cell count mismatches.\n          break;\n        }\n        const mapRow = getMapRow(rowIdx + j);\n        for (let i = 0; i < colSpan; i++) {\n          mapRow[colIdx + i] = value;\n        }\n      }\n      if (cellA !== null && cellAValue === null && cellA.is(cell)) {\n        cellAValue = value;\n      }\n      if (cellB !== null && cellBValue === null && cellB.is(cell)) {\n        cellBValue = value;\n      }\n    }\n  }\n  return [tableMap, cellAValue, cellBValue];\n}\nfunction $getNodeTriplet(source) {\n  let cell;\n  if (source instanceof TableCellNode) {\n    cell = source;\n  } else if ('__type' in source) {\n    const cell_ = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(source, $isTableCellNode);\n    if (!$isTableCellNode(cell_)) {\n      formatDevErrorMessage(`Expected to find a parent TableCellNode`);\n    }\n    cell = cell_;\n  } else {\n    const cell_ = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(source.getNode(), $isTableCellNode);\n    if (!$isTableCellNode(cell_)) {\n      formatDevErrorMessage(`Expected to find a parent TableCellNode`);\n    }\n    cell = cell_;\n  }\n  const row = cell.getParent();\n  if (!$isTableRowNode(row)) {\n    formatDevErrorMessage(`Expected TableCellNode to have a parent TableRowNode`);\n  }\n  const grid = row.getParent();\n  if (!$isTableNode(grid)) {\n    formatDevErrorMessage(`Expected TableRowNode to have a parent TableNode`);\n  }\n  return [cell, row, grid];\n}\nfunction $computeTableCellRectSpans(map, boundary) {\n  const {\n    minColumn,\n    maxColumn,\n    minRow,\n    maxRow\n  } = boundary;\n  let topSpan = 1;\n  let leftSpan = 1;\n  let rightSpan = 1;\n  let bottomSpan = 1;\n  const topRow = map[minRow];\n  const bottomRow = map[maxRow];\n  for (let col = minColumn; col <= maxColumn; col++) {\n    topSpan = Math.max(topSpan, topRow[col].cell.__rowSpan);\n    bottomSpan = Math.max(bottomSpan, bottomRow[col].cell.__rowSpan);\n  }\n  for (let row = minRow; row <= maxRow; row++) {\n    leftSpan = Math.max(leftSpan, map[row][minColumn].cell.__colSpan);\n    rightSpan = Math.max(rightSpan, map[row][maxColumn].cell.__colSpan);\n  }\n  return {\n    bottomSpan,\n    leftSpan,\n    rightSpan,\n    topSpan\n  };\n}\nfunction $computeTableCellRectBoundary(map, cellAMap, cellBMap) {\n  // Initial boundaries based on the anchor and focus cells\n  let minColumn = Math.min(cellAMap.startColumn, cellBMap.startColumn);\n  let minRow = Math.min(cellAMap.startRow, cellBMap.startRow);\n  let maxColumn = Math.max(cellAMap.startColumn + cellAMap.cell.__colSpan - 1, cellBMap.startColumn + cellBMap.cell.__colSpan - 1);\n  let maxRow = Math.max(cellAMap.startRow + cellAMap.cell.__rowSpan - 1, cellBMap.startRow + cellBMap.cell.__rowSpan - 1);\n\n  // Keep expanding until we have a complete rectangle\n  let hasChanges;\n  do {\n    hasChanges = false;\n\n    // Check all cells in the table\n    for (let row = 0; row < map.length; row++) {\n      for (let col = 0; col < map[0].length; col++) {\n        const cell = map[row][col];\n        if (!cell) {\n          continue;\n        }\n        const cellEndCol = cell.startColumn + cell.cell.__colSpan - 1;\n        const cellEndRow = cell.startRow + cell.cell.__rowSpan - 1;\n\n        // Check if this cell intersects with our current selection rectangle\n        const intersectsHorizontally = cell.startColumn <= maxColumn && cellEndCol >= minColumn;\n        const intersectsVertically = cell.startRow <= maxRow && cellEndRow >= minRow;\n\n        // If the cell intersects either horizontally or vertically\n        if (intersectsHorizontally && intersectsVertically) {\n          // Expand boundaries to include this cell completely\n          const newMinColumn = Math.min(minColumn, cell.startColumn);\n          const newMaxColumn = Math.max(maxColumn, cellEndCol);\n          const newMinRow = Math.min(minRow, cell.startRow);\n          const newMaxRow = Math.max(maxRow, cellEndRow);\n\n          // Check if boundaries changed\n          if (newMinColumn !== minColumn || newMaxColumn !== maxColumn || newMinRow !== minRow || newMaxRow !== maxRow) {\n            minColumn = newMinColumn;\n            maxColumn = newMaxColumn;\n            minRow = newMinRow;\n            maxRow = newMaxRow;\n            hasChanges = true;\n          }\n        }\n      }\n    }\n  } while (hasChanges);\n  return {\n    maxColumn,\n    maxRow,\n    minColumn,\n    minRow\n  };\n}\nfunction $getTableCellNodeRect(tableCellNode) {\n  const [cellNode,, gridNode] = $getNodeTriplet(tableCellNode);\n  const rows = gridNode.getChildren();\n  const rowCount = rows.length;\n  const columnCount = rows[0].getChildren().length;\n\n  // Create a matrix of the same size as the table to track the position of each cell\n  const cellMatrix = new Array(rowCount);\n  for (let i = 0; i < rowCount; i++) {\n    cellMatrix[i] = new Array(columnCount);\n  }\n  for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {\n    const row = rows[rowIndex];\n    const cells = row.getChildren();\n    let columnIndex = 0;\n    for (let cellIndex = 0; cellIndex < cells.length; cellIndex++) {\n      // Find the next available position in the matrix, skip the position of merged cells\n      while (cellMatrix[rowIndex][columnIndex]) {\n        columnIndex++;\n      }\n      const cell = cells[cellIndex];\n      const rowSpan = cell.__rowSpan || 1;\n      const colSpan = cell.__colSpan || 1;\n\n      // Put the cell into the corresponding position in the matrix\n      for (let i = 0; i < rowSpan; i++) {\n        for (let j = 0; j < colSpan; j++) {\n          cellMatrix[rowIndex + i][columnIndex + j] = cell;\n        }\n      }\n\n      // Return to the original index, row span and column span of the cell.\n      if (cellNode === cell) {\n        return {\n          colSpan,\n          columnIndex,\n          rowIndex,\n          rowSpan\n        };\n      }\n      columnIndex += colSpan;\n    }\n  }\n  return null;\n}\n\nfunction $getCellNodes(tableSelection) {\n  const [[anchorNode, anchorCell, anchorRow, anchorTable], [focusNode, focusCell, focusRow, focusTable]] = ['anchor', 'focus'].map(k => {\n    const node = tableSelection[k].getNode();\n    const cellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, $isTableCellNode);\n    if (!$isTableCellNode(cellNode)) {\n      formatDevErrorMessage(`Expected TableSelection ${k} to be (or a child of) TableCellNode, got key ${node.getKey()} of type ${node.getType()}`);\n    }\n    const rowNode = cellNode.getParent();\n    if (!$isTableRowNode(rowNode)) {\n      formatDevErrorMessage(`Expected TableSelection ${k} cell parent to be a TableRowNode`);\n    }\n    const tableNode = rowNode.getParent();\n    if (!$isTableNode(tableNode)) {\n      formatDevErrorMessage(`Expected TableSelection ${k} row parent to be a TableNode`);\n    }\n    return [node, cellNode, rowNode, tableNode];\n  });\n  // TODO: nested tables may violate this\n  if (!anchorTable.is(focusTable)) {\n    formatDevErrorMessage(`Expected TableSelection anchor and focus to be in the same table`);\n  }\n  return {\n    anchorCell,\n    anchorNode,\n    anchorRow,\n    anchorTable,\n    focusCell,\n    focusNode,\n    focusRow,\n    focusTable\n  };\n}\nclass TableSelection {\n  constructor(tableKey, anchor, focus) {\n    this.anchor = anchor;\n    this.focus = focus;\n    anchor._selection = this;\n    focus._selection = this;\n    this._cachedNodes = null;\n    this.dirty = false;\n    this.tableKey = tableKey;\n  }\n  getStartEndPoints() {\n    return [this.anchor, this.focus];\n  }\n\n  /**\n   * {@link $createTableSelection} unfortunately makes it very easy to create\n   * nonsense selections, so we have a method to see if the selection probably\n   * makes sense.\n   *\n   * @returns true if the TableSelection is (probably) valid\n   */\n  isValid() {\n    return this.tableKey !== 'root' && this.anchor.key !== 'root' && this.anchor.type === 'element' && this.focus.key !== 'root' && this.focus.type === 'element';\n  }\n\n  /**\n   * Returns whether the Selection is \"backwards\", meaning the focus\n   * logically precedes the anchor in the EditorState.\n   * @returns true if the Selection is backwards, false otherwise.\n   */\n  isBackward() {\n    return this.focus.isBefore(this.anchor);\n  }\n  getCachedNodes() {\n    return this._cachedNodes;\n  }\n  setCachedNodes(nodes) {\n    this._cachedNodes = nodes;\n  }\n  is(selection) {\n    return $isTableSelection(selection) && this.tableKey === selection.tableKey && this.anchor.is(selection.anchor) && this.focus.is(selection.focus);\n  }\n  set(tableKey, anchorCellKey, focusCellKey) {\n    // note: closure compiler's acorn does not support ||=\n    this.dirty = this.dirty || tableKey !== this.tableKey || anchorCellKey !== this.anchor.key || focusCellKey !== this.focus.key;\n    this.tableKey = tableKey;\n    this.anchor.key = anchorCellKey;\n    this.focus.key = focusCellKey;\n    this._cachedNodes = null;\n  }\n  clone() {\n    return new TableSelection(this.tableKey, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createPoint)(this.anchor.key, this.anchor.offset, this.anchor.type), (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createPoint)(this.focus.key, this.focus.offset, this.focus.type));\n  }\n  isCollapsed() {\n    return false;\n  }\n  extract() {\n    return this.getNodes();\n  }\n  insertRawText(text) {\n    // Do nothing?\n  }\n  insertText() {\n    // Do nothing?\n  }\n\n  /**\n   * Returns whether the provided TextFormatType is present on the Selection.\n   * This will be true if any paragraph in table cells has the specified format.\n   *\n   * @param type the TextFormatType to check for.\n   * @returns true if the provided format is currently toggled on on the Selection, false otherwise.\n   */\n  hasFormat(type) {\n    let format = 0;\n    const cellNodes = this.getNodes().filter($isTableCellNode);\n    cellNodes.forEach(cellNode => {\n      const paragraph = cellNode.getFirstChild();\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isParagraphNode)(paragraph)) {\n        format |= paragraph.getTextFormat();\n      }\n    });\n    const formatFlag = lexical__WEBPACK_IMPORTED_MODULE_0__.TEXT_TYPE_TO_FORMAT[type];\n    return (format & formatFlag) !== 0;\n  }\n  insertNodes(nodes) {\n    const focusNode = this.focus.getNode();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(focusNode)) {\n      formatDevErrorMessage(`Expected TableSelection focus to be an ElementNode`);\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeSelection__EXPERIMENTAL)(focusNode.select(0, focusNode.getChildrenSize()));\n    selection.insertNodes(nodes);\n  }\n\n  // TODO Deprecate this method. It's confusing when used with colspan|rowspan\n  getShape() {\n    const {\n      anchorCell,\n      focusCell\n    } = $getCellNodes(this);\n    const anchorCellNodeRect = $getTableCellNodeRect(anchorCell);\n    if (!(anchorCellNodeRect !== null)) {\n      formatDevErrorMessage(`getCellRect: expected to find AnchorNode`);\n    }\n    const focusCellNodeRect = $getTableCellNodeRect(focusCell);\n    if (!(focusCellNodeRect !== null)) {\n      formatDevErrorMessage(`getCellRect: expected to find focusCellNode`);\n    }\n    const startX = Math.min(anchorCellNodeRect.columnIndex, focusCellNodeRect.columnIndex);\n    const stopX = Math.max(anchorCellNodeRect.columnIndex + anchorCellNodeRect.colSpan - 1, focusCellNodeRect.columnIndex + focusCellNodeRect.colSpan - 1);\n    const startY = Math.min(anchorCellNodeRect.rowIndex, focusCellNodeRect.rowIndex);\n    const stopY = Math.max(anchorCellNodeRect.rowIndex + anchorCellNodeRect.rowSpan - 1, focusCellNodeRect.rowIndex + focusCellNodeRect.rowSpan - 1);\n    return {\n      fromX: Math.min(startX, stopX),\n      fromY: Math.min(startY, stopY),\n      toX: Math.max(startX, stopX),\n      toY: Math.max(startY, stopY)\n    };\n  }\n  getNodes() {\n    if (!this.isValid()) {\n      return [];\n    }\n    const cachedNodes = this._cachedNodes;\n    if (cachedNodes !== null) {\n      return cachedNodes;\n    }\n    const {\n      anchorTable: tableNode,\n      anchorCell,\n      focusCell\n    } = $getCellNodes(this);\n    const focusCellGrid = focusCell.getParents()[1];\n    if (focusCellGrid !== tableNode) {\n      if (!tableNode.isParentOf(focusCell)) {\n        // focus is on higher Grid level than anchor\n        const gridParent = tableNode.getParent();\n        if (!(gridParent != null)) {\n          formatDevErrorMessage(`Expected gridParent to have a parent`);\n        }\n        this.set(this.tableKey, gridParent.getKey(), focusCell.getKey());\n      } else {\n        // anchor is on higher Grid level than focus\n        const focusCellParent = focusCellGrid.getParent();\n        if (!(focusCellParent != null)) {\n          formatDevErrorMessage(`Expected focusCellParent to have a parent`);\n        }\n        this.set(this.tableKey, focusCell.getKey(), focusCellParent.getKey());\n      }\n      return this.getNodes();\n    }\n\n    // TODO Mapping the whole Grid every time not efficient. We need to compute the entire state only\n    // once (on load) and iterate on it as updates occur. However, to do this we need to have the\n    // ability to store a state. Killing TableSelection and moving the logic to the plugin would make\n    // this possible.\n    const [map, cellAMap, cellBMap] = $computeTableMap(tableNode, anchorCell, focusCell);\n    const {\n      minColumn,\n      maxColumn,\n      minRow,\n      maxRow\n    } = $computeTableCellRectBoundary(map, cellAMap, cellBMap);\n\n    // We use a Map here because merged cells in the grid would otherwise\n    // show up multiple times in the nodes array\n    const nodeMap = new Map([[tableNode.getKey(), tableNode]]);\n    let lastRow = null;\n    for (let i = minRow; i <= maxRow; i++) {\n      for (let j = minColumn; j <= maxColumn; j++) {\n        const {\n          cell\n        } = map[i][j];\n        const currentRow = cell.getParent();\n        if (!$isTableRowNode(currentRow)) {\n          formatDevErrorMessage(`Expected TableCellNode parent to be a TableRowNode`);\n        }\n        if (currentRow !== lastRow) {\n          nodeMap.set(currentRow.getKey(), currentRow);\n          lastRow = currentRow;\n        }\n        if (!nodeMap.has(cell.getKey())) {\n          $visitRecursively(cell, childNode => {\n            nodeMap.set(childNode.getKey(), childNode);\n          });\n        }\n      }\n    }\n    const nodes = Array.from(nodeMap.values());\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isCurrentlyReadOnlyMode)()) {\n      this._cachedNodes = nodes;\n    }\n    return nodes;\n  }\n  getTextContent() {\n    const nodes = this.getNodes().filter(node => $isTableCellNode(node));\n    let textContent = '';\n    for (let i = 0; i < nodes.length; i++) {\n      const node = nodes[i];\n      const row = node.__parent;\n      const nextRow = (nodes[i + 1] || {}).__parent;\n      textContent += node.getTextContent() + (nextRow !== row ? '\\n' : '\\t');\n    }\n    return textContent;\n  }\n}\nfunction $isTableSelection(x) {\n  return x instanceof TableSelection;\n}\nfunction $createTableSelection() {\n  // TODO this is a suboptimal design, it doesn't make sense to have\n  // a table selection that isn't associated with a table. This\n  // constructor should have required argumnets and in true we\n  // should check that they point to a table and are element points to\n  // cell nodes of that table.\n  const anchor = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createPoint)('root', 0, 'element');\n  const focus = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createPoint)('root', 0, 'element');\n  return new TableSelection('root', anchor, focus);\n}\nfunction $createTableSelectionFrom(tableNode, anchorCell, focusCell) {\n  const tableNodeKey = tableNode.getKey();\n  const anchorCellKey = anchorCell.getKey();\n  const focusCellKey = focusCell.getKey();\n  {\n    if (!tableNode.isAttached()) {\n      formatDevErrorMessage(`$createTableSelectionFrom: tableNode ${tableNodeKey} is not attached`);\n    }\n    if (!tableNode.is($findTableNode(anchorCell))) {\n      formatDevErrorMessage(`$createTableSelectionFrom: anchorCell ${anchorCellKey} is not in table ${tableNodeKey}`);\n    }\n    if (!tableNode.is($findTableNode(focusCell))) {\n      formatDevErrorMessage(`$createTableSelectionFrom: focusCell ${focusCellKey} is not in table ${tableNodeKey}`);\n    } // TODO: Check for rectangular grid\n  }\n  const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  const nextSelection = $isTableSelection(prevSelection) ? prevSelection.clone() : $createTableSelection();\n  nextSelection.set(tableNode.getKey(), anchorCell.getKey(), focusCell.getKey());\n  return nextSelection;\n}\n\n/**\n * Depth first visitor\n * @param node The starting node\n * @param $visit The function to call for each node. If the function returns false, then children of this node will not be explored\n */\nfunction $visitRecursively(node, $visit) {\n  const stack = [[node]];\n  for (let currentArray = stack.at(-1); currentArray !== undefined && stack.length > 0; currentArray = stack.at(-1)) {\n    const currentNode = currentArray.pop();\n    if (currentNode === undefined) {\n      stack.pop();\n    } else if ($visit(currentNode) !== false && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode)) {\n      stack.push(currentNode.getChildren());\n    }\n  }\n}\n\nfunction $getTableAndElementByKey(tableNodeKey, editor = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getEditor)()) {\n  const tableNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNodeByKey)(tableNodeKey);\n  if (!$isTableNode(tableNode)) {\n    formatDevErrorMessage(`TableObserver: Expected tableNodeKey ${tableNodeKey} to be a TableNode`);\n  }\n  const tableElement = getTableElement(tableNode, editor.getElementByKey(tableNodeKey));\n  if (!(tableElement !== null)) {\n    formatDevErrorMessage(`TableObserver: Expected to find TableElement in DOM for key ${tableNodeKey}`);\n  }\n  return {\n    tableElement,\n    tableNode\n  };\n}\nclass TableObserver {\n  constructor(editor, tableNodeKey) {\n    this.isHighlightingCells = false;\n    this.anchorX = -1;\n    this.anchorY = -1;\n    this.focusX = -1;\n    this.focusY = -1;\n    this.listenersToRemove = new Set();\n    this.tableNodeKey = tableNodeKey;\n    this.editor = editor;\n    this.table = {\n      columns: 0,\n      domRows: [],\n      rows: 0\n    };\n    this.tableSelection = null;\n    this.anchorCellNodeKey = null;\n    this.focusCellNodeKey = null;\n    this.anchorCell = null;\n    this.focusCell = null;\n    this.hasHijackedSelectionStyles = false;\n    this.isSelecting = false;\n    this.pointerType = null;\n    this.shouldCheckSelection = false;\n    this.abortController = new AbortController();\n    this.listenerOptions = {\n      signal: this.abortController.signal\n    };\n    this.nextFocus = null;\n    this.trackTable();\n  }\n  getTable() {\n    return this.table;\n  }\n  removeListeners() {\n    this.abortController.abort('removeListeners');\n    Array.from(this.listenersToRemove).forEach(removeListener => removeListener());\n    this.listenersToRemove.clear();\n  }\n  $lookup() {\n    return $getTableAndElementByKey(this.tableNodeKey, this.editor);\n  }\n  trackTable() {\n    const observer = new MutationObserver(records => {\n      this.editor.getEditorState().read(() => {\n        let gridNeedsRedraw = false;\n        for (let i = 0; i < records.length; i++) {\n          const record = records[i];\n          const target = record.target;\n          const nodeName = target.nodeName;\n          if (nodeName === 'TABLE' || nodeName === 'TBODY' || nodeName === 'THEAD' || nodeName === 'TR') {\n            gridNeedsRedraw = true;\n            break;\n          }\n        }\n        if (!gridNeedsRedraw) {\n          return;\n        }\n        const {\n          tableNode,\n          tableElement\n        } = this.$lookup();\n        this.table = getTable(tableNode, tableElement);\n      }, {\n        editor: this.editor\n      });\n    });\n    this.editor.getEditorState().read(() => {\n      const {\n        tableNode,\n        tableElement\n      } = this.$lookup();\n      this.table = getTable(tableNode, tableElement);\n      observer.observe(tableElement, {\n        attributes: true,\n        childList: true,\n        subtree: true\n      });\n    }, {\n      editor: this.editor\n    });\n  }\n  $clearHighlight() {\n    const editor = this.editor;\n    this.isHighlightingCells = false;\n    this.anchorX = -1;\n    this.anchorY = -1;\n    this.focusX = -1;\n    this.focusY = -1;\n    this.tableSelection = null;\n    this.anchorCellNodeKey = null;\n    this.focusCellNodeKey = null;\n    this.anchorCell = null;\n    this.focusCell = null;\n    this.hasHijackedSelectionStyles = false;\n    this.$enableHighlightStyle();\n    const {\n      tableNode,\n      tableElement\n    } = this.$lookup();\n    const grid = getTable(tableNode, tableElement);\n    $updateDOMForSelection(editor, grid, null);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)() !== null) {\n      (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(null);\n      editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, undefined);\n    }\n  }\n  $enableHighlightStyle() {\n    const editor = this.editor;\n    const {\n      tableElement\n    } = this.$lookup();\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(tableElement, editor._config.theme.tableSelection);\n    tableElement.classList.remove('disable-selection');\n    this.hasHijackedSelectionStyles = false;\n  }\n  $disableHighlightStyle() {\n    const {\n      tableElement\n    } = this.$lookup();\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(tableElement, this.editor._config.theme.tableSelection);\n    this.hasHijackedSelectionStyles = true;\n  }\n  $updateTableTableSelection(selection) {\n    if (selection !== null) {\n      if (!(selection.tableKey === this.tableNodeKey)) {\n        formatDevErrorMessage(`TableObserver.$updateTableTableSelection: selection.tableKey !== this.tableNodeKey ('${selection.tableKey}' !== '${this.tableNodeKey}')`);\n      }\n      const editor = this.editor;\n      this.tableSelection = selection;\n      this.isHighlightingCells = true;\n      this.$disableHighlightStyle();\n      this.updateDOMSelection();\n      $updateDOMForSelection(editor, this.table, this.tableSelection);\n    } else {\n      this.$clearHighlight();\n    }\n  }\n\n  /**\n   * @internal\n   * Firefox has a strange behavior where pressing the down arrow key from\n   * above the table will move the caret after the table and then lexical\n   * will select the last cell instead of the first.\n   * We do still want to let the browser handle caret movement but we will\n   * use this property to \"tag\" the update so that we can recheck the\n   * selection after the event is processed.\n   */\n  setShouldCheckSelection() {\n    this.shouldCheckSelection = true;\n  }\n  /**\n   * @internal\n   */\n  getAndClearShouldCheckSelection() {\n    if (this.shouldCheckSelection) {\n      this.shouldCheckSelection = false;\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * @internal\n   * When handling mousemove events we track what the focus cell should be, but\n   * the DOM selection may end up somewhere else entirely. We don't have an elegant\n   * way to handle this after the DOM selection has been resolved in a\n   * SELECTION_CHANGE_COMMAND callback.\n   */\n  setNextFocus(nextFocus) {\n    this.nextFocus = nextFocus;\n  }\n\n  /** @internal */\n  getAndClearNextFocus() {\n    const {\n      nextFocus\n    } = this;\n    if (nextFocus !== null) {\n      this.nextFocus = null;\n    }\n    return nextFocus;\n  }\n\n  /** @internal */\n  updateDOMSelection() {\n    if (this.anchorCell !== null && this.focusCell !== null) {\n      const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(this.editor._window);\n      // We are not using a native selection for tables, and if we\n      // set one then the reconciler will undo it.\n      // TODO - it would make sense to have one so that native\n      //        copy/paste worked. Right now we have to emulate with\n      //        keyboard events but it won't fire if trigged from the menu\n      if (domSelection && domSelection.rangeCount > 0) {\n        domSelection.removeAllRanges();\n      }\n    }\n  }\n  $setFocusCellForSelection(cell, ignoreStart = false) {\n    const editor = this.editor;\n    const {\n      tableNode\n    } = this.$lookup();\n    const cellX = cell.x;\n    const cellY = cell.y;\n    this.focusCell = cell;\n    if (!this.isHighlightingCells && (this.anchorX !== cellX || this.anchorY !== cellY || ignoreStart)) {\n      this.isHighlightingCells = true;\n      this.$disableHighlightStyle();\n    } else if (cellX === this.focusX && cellY === this.focusY) {\n      return false;\n    }\n    this.focusX = cellX;\n    this.focusY = cellY;\n    if (this.isHighlightingCells) {\n      const focusTableCellNode = $getNearestTableCellInTableFromDOMNode(tableNode, cell.elem);\n      if (this.tableSelection != null && this.anchorCellNodeKey != null && focusTableCellNode !== null) {\n        this.focusCellNodeKey = focusTableCellNode.getKey();\n        this.tableSelection = $createTableSelectionFrom(tableNode, this.$getAnchorTableCellOrThrow(), focusTableCellNode);\n        (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(this.tableSelection);\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, undefined);\n        $updateDOMForSelection(editor, this.table, this.tableSelection);\n        return true;\n      }\n    }\n    return false;\n  }\n  $getAnchorTableCell() {\n    return this.anchorCellNodeKey ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNodeByKey)(this.anchorCellNodeKey) : null;\n  }\n  $getAnchorTableCellOrThrow() {\n    const anchorTableCell = this.$getAnchorTableCell();\n    if (!(anchorTableCell !== null)) {\n      formatDevErrorMessage(`TableObserver anchorTableCell is null`);\n    }\n    return anchorTableCell;\n  }\n  $getFocusTableCell() {\n    return this.focusCellNodeKey ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNodeByKey)(this.focusCellNodeKey) : null;\n  }\n  $getFocusTableCellOrThrow() {\n    const focusTableCell = this.$getFocusTableCell();\n    if (!(focusTableCell !== null)) {\n      formatDevErrorMessage(`TableObserver focusTableCell is null`);\n    }\n    return focusTableCell;\n  }\n  $setAnchorCellForSelection(cell) {\n    this.isHighlightingCells = false;\n    this.anchorCell = cell;\n    this.anchorX = cell.x;\n    this.anchorY = cell.y;\n    const {\n      tableNode\n    } = this.$lookup();\n    const anchorTableCellNode = $getNearestTableCellInTableFromDOMNode(tableNode, cell.elem);\n    if (anchorTableCellNode !== null) {\n      const anchorNodeKey = anchorTableCellNode.getKey();\n      this.tableSelection = this.tableSelection != null ? this.tableSelection.clone() : $createTableSelection();\n      this.anchorCellNodeKey = anchorNodeKey;\n    }\n  }\n  $formatCells(type) {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isTableSelection(selection)) {\n      formatDevErrorMessage(`Expected Table selection`);\n    }\n    const formatSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createRangeSelection)();\n    const anchor = formatSelection.anchor;\n    const focus = formatSelection.focus;\n    const cellNodes = selection.getNodes().filter($isTableCellNode);\n    if (!(cellNodes.length > 0)) {\n      formatDevErrorMessage(`No table cells present`);\n    }\n    const paragraph = cellNodes[0].getFirstChild();\n    const alignFormatWith = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isParagraphNode)(paragraph) ? paragraph.getFormatFlags(type, null) : null;\n    cellNodes.forEach(cellNode => {\n      anchor.set(cellNode.getKey(), 0, 'element');\n      focus.set(cellNode.getKey(), cellNode.getChildrenSize(), 'element');\n      formatSelection.formatText(type, alignFormatWith);\n    });\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(selection);\n    this.editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, undefined);\n  }\n  $clearText() {\n    const {\n      editor\n    } = this;\n    const tableNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNodeByKey)(this.tableNodeKey);\n    if (!$isTableNode(tableNode)) {\n      throw new Error('Expected TableNode.');\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isTableSelection(selection)) {\n      formatDevErrorMessage(`Expected TableSelection`);\n    }\n    const selectedNodes = selection.getNodes().filter($isTableCellNode);\n    if (selectedNodes.length === this.table.columns * this.table.rows) {\n      tableNode.selectPrevious();\n      const parent = tableNode.getParent();\n      // Delete entire table\n      tableNode.remove();\n      // Handle case when table was the only node\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(parent) && parent.isEmpty()) {\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, undefined);\n      }\n      return;\n    }\n    selectedNodes.forEach(cellNode => {\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(cellNode)) {\n        const paragraphNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n        const textNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)();\n        paragraphNode.append(textNode);\n        cellNode.append(paragraphNode);\n        cellNode.getChildren().forEach(child => {\n          if (child !== paragraphNode) {\n            child.remove();\n          }\n        });\n      }\n    });\n    $updateDOMForSelection(editor, this.table, null);\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(null);\n    editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, undefined);\n  }\n}\n\nconst LEXICAL_ELEMENT_KEY = '__lexicalTableSelection';\nconst isPointerDownOnEvent = event => {\n  return (event.buttons & 1) === 1;\n};\nfunction isHTMLTableElement(el) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(el) && el.nodeName === 'TABLE';\n}\nfunction getTableElement(tableNode, dom) {\n  if (!dom) {\n    return dom;\n  }\n  const element = isHTMLTableElement(dom) ? dom : tableNode.getDOMSlot(dom).element;\n  if (!(element.nodeName === 'TABLE')) {\n    formatDevErrorMessage(`getTableElement: Expecting table in as DOM node for TableNode, not ${dom.nodeName}`);\n  }\n  return element;\n}\nfunction getEditorWindow(editor) {\n  return editor._window;\n}\nfunction $findParentTableCellNodeInTable(tableNode, node) {\n  for (let currentNode = node, lastTableCellNode = null; currentNode !== null; currentNode = currentNode.getParent()) {\n    if (tableNode.is(currentNode)) {\n      return lastTableCellNode;\n    } else if ($isTableCellNode(currentNode)) {\n      lastTableCellNode = currentNode;\n    }\n  }\n  return null;\n}\nconst ARROW_KEY_COMMANDS_WITH_DIRECTION = [[lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_DOWN_COMMAND, 'down'], [lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_UP_COMMAND, 'up'], [lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_LEFT_COMMAND, 'backward'], [lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_RIGHT_COMMAND, 'forward']];\nconst DELETE_TEXT_COMMANDS = [lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_WORD_COMMAND, lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_LINE_COMMAND, lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_CHARACTER_COMMAND];\nconst DELETE_KEY_COMMANDS = [lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_BACKSPACE_COMMAND, lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_DELETE_COMMAND];\nfunction applyTableHandlers(tableNode, element, editor, hasTabHandler) {\n  const rootElement = editor.getRootElement();\n  const editorWindow = getEditorWindow(editor);\n  if (!(rootElement !== null && editorWindow !== null)) {\n    formatDevErrorMessage(`applyTableHandlers: editor has no root element set`);\n  }\n  const tableObserver = new TableObserver(editor, tableNode.getKey());\n  const tableElement = getTableElement(tableNode, element);\n  attachTableObserverToTableElement(tableElement, tableObserver);\n  tableObserver.listenersToRemove.add(() => detatchTableObserverFromTableElement(tableElement, tableObserver));\n  const createPointerHandlers = () => {\n    if (tableObserver.isSelecting) {\n      return;\n    }\n    const onPointerUp = () => {\n      tableObserver.isSelecting = false;\n      editorWindow.removeEventListener('pointerup', onPointerUp);\n      editorWindow.removeEventListener('pointermove', onPointerMove);\n    };\n    const onPointerMove = moveEvent => {\n      if (!isPointerDownOnEvent(moveEvent) && tableObserver.isSelecting) {\n        tableObserver.isSelecting = false;\n        editorWindow.removeEventListener('pointerup', onPointerUp);\n        editorWindow.removeEventListener('pointermove', onPointerMove);\n        return;\n      }\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(moveEvent.target)) {\n        return;\n      }\n      let focusCell = null;\n      // In firefox the moveEvent.target may be captured so we must always\n      // consult the coordinates #7245\n      const override = !(IS_FIREFOX || tableElement.contains(moveEvent.target));\n      if (override) {\n        focusCell = getDOMCellInTableFromTarget(tableElement, moveEvent.target);\n      } else {\n        for (const el of document.elementsFromPoint(moveEvent.clientX, moveEvent.clientY)) {\n          focusCell = getDOMCellInTableFromTarget(tableElement, el);\n          if (focusCell) {\n            break;\n          }\n        }\n      }\n      if (focusCell && (tableObserver.focusCell === null || focusCell.elem !== tableObserver.focusCell.elem)) {\n        tableObserver.setNextFocus({\n          focusCell,\n          override\n        });\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, undefined);\n      }\n    };\n    tableObserver.isSelecting = true;\n    editorWindow.addEventListener('pointerup', onPointerUp, tableObserver.listenerOptions);\n    editorWindow.addEventListener('pointermove', onPointerMove, tableObserver.listenerOptions);\n  };\n  const onPointerDown = event => {\n    tableObserver.pointerType = event.pointerType;\n    if (event.button !== 0 || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(event.target) || !editorWindow) {\n      return;\n    }\n    const targetCell = getDOMCellFromTarget(event.target);\n    if (targetCell !== null) {\n      editor.update(() => {\n        const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n        // We can't trust Firefox to do the right thing with the selection and\n        // we don't have a proper state machine to do this \"correctly\" but\n        // if we go ahead and make the table selection now it will work\n        if (IS_FIREFOX && event.shiftKey && $isSelectionInTable(prevSelection, tableNode) && ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) || $isTableSelection(prevSelection))) {\n          const prevAnchorNode = prevSelection.anchor.getNode();\n          const prevAnchorCell = $findParentTableCellNodeInTable(tableNode, prevSelection.anchor.getNode());\n          if (prevAnchorCell) {\n            tableObserver.$setAnchorCellForSelection($getObserverCellFromCellNodeOrThrow(tableObserver, prevAnchorCell));\n            tableObserver.$setFocusCellForSelection(targetCell);\n            stopEvent(event);\n          } else {\n            const newSelection = tableNode.isBefore(prevAnchorNode) ? tableNode.selectStart() : tableNode.selectEnd();\n            newSelection.anchor.set(prevSelection.anchor.key, prevSelection.anchor.offset, prevSelection.anchor.type);\n          }\n        } else {\n          tableObserver.$setAnchorCellForSelection(targetCell);\n        }\n      });\n    }\n    createPointerHandlers();\n  };\n  tableElement.addEventListener('pointerdown', onPointerDown, tableObserver.listenerOptions);\n  tableObserver.listenersToRemove.add(() => {\n    tableElement.removeEventListener('pointerdown', onPointerDown);\n  });\n  const onTripleClick = event => {\n    if (event.detail >= 3 && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(event.target)) {\n      const targetCell = getDOMCellFromTarget(event.target);\n      if (targetCell !== null) {\n        event.preventDefault();\n      }\n    }\n  };\n  tableElement.addEventListener('mousedown', onTripleClick, tableObserver.listenerOptions);\n  tableObserver.listenersToRemove.add(() => {\n    tableElement.removeEventListener('mousedown', onTripleClick);\n  });\n\n  // Clear selection when clicking outside of dom.\n  const pointerDownCallback = event => {\n    const target = event.target;\n    if (event.button !== 0 || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(target)) {\n      return;\n    }\n    editor.update(() => {\n      const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n      if ($isTableSelection(selection) && selection.tableKey === tableObserver.tableNodeKey && rootElement.contains(target)) {\n        tableObserver.$clearHighlight();\n      }\n    });\n  };\n  editorWindow.addEventListener('pointerdown', pointerDownCallback, tableObserver.listenerOptions);\n  tableObserver.listenersToRemove.add(() => {\n    editorWindow.removeEventListener('pointerdown', pointerDownCallback);\n  });\n  for (const [command, direction] of ARROW_KEY_COMMANDS_WITH_DIRECTION) {\n    tableObserver.listenersToRemove.add(editor.registerCommand(command, event => $handleArrowKey(editor, event, direction, tableNode, tableObserver), lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_HIGH));\n  }\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ESCAPE_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ($isTableSelection(selection)) {\n      const focusCellNode = $findParentTableCellNodeInTable(tableNode, selection.focus.getNode());\n      if (focusCellNode !== null) {\n        stopEvent(event);\n        focusCellNode.selectEnd();\n        return true;\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_HIGH));\n  const deleteTextHandler = command => () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    if ($isTableSelection(selection)) {\n      tableObserver.$clearText();\n      return true;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const tableCellNode = $findParentTableCellNodeInTable(tableNode, selection.anchor.getNode());\n      if (!$isTableCellNode(tableCellNode)) {\n        return false;\n      }\n      const anchorNode = selection.anchor.getNode();\n      const focusNode = selection.focus.getNode();\n      const isAnchorInside = tableNode.isParentOf(anchorNode);\n      const isFocusInside = tableNode.isParentOf(focusNode);\n      const selectionContainsPartialTable = isAnchorInside && !isFocusInside || isFocusInside && !isAnchorInside;\n      if (selectionContainsPartialTable) {\n        tableObserver.$clearText();\n        return true;\n      }\n      const nearestElementNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.anchor.getNode(), n => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(n));\n      const topLevelCellElementNode = nearestElementNode && (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(nearestElementNode, n => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(n) && $isTableCellNode(n.getParent()));\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(topLevelCellElementNode) || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(nearestElementNode)) {\n        return false;\n      }\n      if (command === lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_LINE_COMMAND && topLevelCellElementNode.getPreviousSibling() === null) {\n        // TODO: Fix Delete Line in Table Cells.\n        return true;\n      }\n    }\n    return false;\n  };\n  for (const command of DELETE_TEXT_COMMANDS) {\n    tableObserver.listenersToRemove.add(editor.registerCommand(command, deleteTextHandler(command), lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  }\n  const $deleteCellHandler = event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!($isTableSelection(selection) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection))) {\n      return false;\n    }\n\n    // If the selection is inside the table but should remove the whole table\n    // we expand the selection so that both the anchor and focus are outside\n    // the table and the editor's command listener will handle the delete\n    const isAnchorInside = tableNode.isParentOf(selection.anchor.getNode());\n    const isFocusInside = tableNode.isParentOf(selection.focus.getNode());\n    if (isAnchorInside !== isFocusInside) {\n      const tablePoint = isAnchorInside ? 'anchor' : 'focus';\n      const outerPoint = isAnchorInside ? 'focus' : 'anchor';\n      // Preserve the outer point\n      const {\n        key,\n        offset,\n        type\n      } = selection[outerPoint];\n      // Expand the selection around the table\n      const newSelection = tableNode[selection[tablePoint].isBefore(selection[outerPoint]) ? 'selectPrevious' : 'selectNext']();\n      // Restore the outer point of the selection\n      newSelection[outerPoint].set(key, offset, type);\n      // Let the base implementation handle the rest\n      return false;\n    }\n    if (!$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    if ($isTableSelection(selection)) {\n      if (event) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n      tableObserver.$clearText();\n      return true;\n    }\n    return false;\n  };\n  for (const command of DELETE_KEY_COMMANDS) {\n    tableObserver.listenersToRemove.add(editor.registerCommand(command, $deleteCellHandler, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  }\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CUT_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (selection) {\n      if (!($isTableSelection(selection) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection))) {\n        return false;\n      }\n      // Copying to the clipboard is async so we must capture the data\n      // before we delete it\n      void (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.copyToClipboard)(editor, (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, ClipboardEvent) ? event : null, (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.$getClipboardDataFromSelection)(selection));\n      const intercepted = $deleteCellHandler(event);\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n        selection.removeText();\n        return true;\n      }\n      return intercepted;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FORMAT_TEXT_COMMAND, payload => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    if ($isTableSelection(selection)) {\n      tableObserver.$formatCells(payload);\n      return true;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const tableCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.anchor.getNode(), n => $isTableCellNode(n));\n      if (!$isTableCellNode(tableCellNode)) {\n        return false;\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FORMAT_ELEMENT_COMMAND, formatType => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isTableSelection(selection) || !$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    const anchorNode = selection.anchor.getNode();\n    const focusNode = selection.focus.getNode();\n    if (!$isTableCellNode(anchorNode) || !$isTableCellNode(focusNode)) {\n      return false;\n    }\n\n    // Align the table if the entire table is selected\n    if ($isFullTableSelection(selection, tableNode)) {\n      tableNode.setFormat(formatType);\n      return true;\n    }\n    const [tableMap, anchorCell, focusCell] = $computeTableMap(tableNode, anchorNode, focusNode);\n    const maxRow = Math.max(anchorCell.startRow + anchorCell.cell.__rowSpan - 1, focusCell.startRow + focusCell.cell.__rowSpan - 1);\n    const maxColumn = Math.max(anchorCell.startColumn + anchorCell.cell.__colSpan - 1, focusCell.startColumn + focusCell.cell.__colSpan - 1);\n    const minRow = Math.min(anchorCell.startRow, focusCell.startRow);\n    const minColumn = Math.min(anchorCell.startColumn, focusCell.startColumn);\n    const visited = new Set();\n    for (let i = minRow; i <= maxRow; i++) {\n      for (let j = minColumn; j <= maxColumn; j++) {\n        const cell = tableMap[i][j].cell;\n        if (visited.has(cell)) {\n          continue;\n        }\n        visited.add(cell);\n        cell.setFormat(formatType);\n        const cellChildren = cell.getChildren();\n        for (let k = 0; k < cellChildren.length; k++) {\n          const child = cellChildren[k];\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(child) && !child.isInline()) {\n            child.setFormat(formatType);\n          }\n        }\n      }\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CONTROLLED_TEXT_INSERTION_COMMAND, payload => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    if ($isTableSelection(selection)) {\n      tableObserver.$clearHighlight();\n      return false;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const tableCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.anchor.getNode(), n => $isTableCellNode(n));\n      if (!$isTableCellNode(tableCellNode)) {\n        return false;\n      }\n      if (typeof payload === 'string') {\n        const edgePosition = $getTableEdgeCursorPosition(editor, selection, tableNode);\n        if (edgePosition) {\n          $insertParagraphAtTableEdge(edgePosition, tableNode, [(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(payload)]);\n          return true;\n        }\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  if (hasTabHandler) {\n    tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_TAB_COMMAND, event => {\n      const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || !selection.isCollapsed() || !$isSelectionInTable(selection, tableNode)) {\n        return false;\n      }\n      const tableCellNode = $findCellNode(selection.anchor.getNode());\n      if (tableCellNode === null || !tableNode.is($findTableNode(tableCellNode))) {\n        return false;\n      }\n      stopEvent(event);\n      $selectAdjacentCell(tableCellNode, event.shiftKey ? 'previous' : 'next');\n      return true;\n    }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  }\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FOCUS_COMMAND, payload => {\n    return tableNode.isSelected();\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_HIGH));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_INSERT_CLIPBOARD_NODES_COMMAND, selectionPayload => {\n    const {\n      nodes,\n      selection\n    } = selectionPayload;\n    const anchorAndFocus = selection.getStartEndPoints();\n    const isTableSelection = $isTableSelection(selection);\n    const isRangeSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection);\n    const isSelectionInsideOfGrid = isRangeSelection && (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.anchor.getNode(), n => $isTableCellNode(n)) !== null && (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.focus.getNode(), n => $isTableCellNode(n)) !== null || isTableSelection;\n    if (nodes.length !== 1 || !$isTableNode(nodes[0]) || !isSelectionInsideOfGrid || anchorAndFocus === null) {\n      return false;\n    }\n    const [anchor] = anchorAndFocus;\n    const newGrid = nodes[0];\n    const newGridRows = newGrid.getChildren();\n    const newColumnCount = newGrid.getFirstChildOrThrow().getChildrenSize();\n    const newRowCount = newGrid.getChildrenSize();\n    const gridCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchor.getNode(), n => $isTableCellNode(n));\n    const gridRowNode = gridCellNode && (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(gridCellNode, n => $isTableRowNode(n));\n    const gridNode = gridRowNode && (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(gridRowNode, n => $isTableNode(n));\n    if (!$isTableCellNode(gridCellNode) || !$isTableRowNode(gridRowNode) || !$isTableNode(gridNode)) {\n      return false;\n    }\n    const startY = gridRowNode.getIndexWithinParent();\n    const stopY = Math.min(gridNode.getChildrenSize() - 1, startY + newRowCount - 1);\n    const startX = gridCellNode.getIndexWithinParent();\n    const stopX = Math.min(gridRowNode.getChildrenSize() - 1, startX + newColumnCount - 1);\n    const fromX = Math.min(startX, stopX);\n    const fromY = Math.min(startY, stopY);\n    const toX = Math.max(startX, stopX);\n    const toY = Math.max(startY, stopY);\n    const gridRowNodes = gridNode.getChildren();\n    let newRowIdx = 0;\n    for (let r = fromY; r <= toY; r++) {\n      const currentGridRowNode = gridRowNodes[r];\n      if (!$isTableRowNode(currentGridRowNode)) {\n        return false;\n      }\n      const newGridRowNode = newGridRows[newRowIdx];\n      if (!$isTableRowNode(newGridRowNode)) {\n        return false;\n      }\n      const gridCellNodes = currentGridRowNode.getChildren();\n      const newGridCellNodes = newGridRowNode.getChildren();\n      let newColumnIdx = 0;\n      for (let c = fromX; c <= toX; c++) {\n        const currentGridCellNode = gridCellNodes[c];\n        if (!$isTableCellNode(currentGridCellNode)) {\n          return false;\n        }\n        const newGridCellNode = newGridCellNodes[newColumnIdx];\n        if (!$isTableCellNode(newGridCellNode)) {\n          return false;\n        }\n        const originalChildren = currentGridCellNode.getChildren();\n        newGridCellNode.getChildren().forEach(child => {\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(child)) {\n            const paragraphNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n            paragraphNode.append(child);\n            currentGridCellNode.append(child);\n          } else {\n            currentGridCellNode.append(child);\n          }\n        });\n        originalChildren.forEach(n => n.remove());\n        newColumnIdx++;\n      }\n      newRowIdx++;\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_CHANGE_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n    const nextFocus = tableObserver.getAndClearNextFocus();\n    if (nextFocus !== null) {\n      const {\n        focusCell\n      } = nextFocus;\n      if ($isTableSelection(selection) && selection.tableKey === tableObserver.tableNodeKey) {\n        if (focusCell.x === tableObserver.focusX && focusCell.y === tableObserver.focusY) {\n          // The selection is already the correct table selection\n          return false;\n        } else {\n          tableObserver.$setFocusCellForSelection(focusCell);\n          return true;\n        }\n      } else if (focusCell !== tableObserver.anchorCell && $isSelectionInTable(selection, tableNode)) {\n        // The selection has crossed cells\n        tableObserver.$setFocusCellForSelection(focusCell);\n        return true;\n      }\n    }\n    const shouldCheckSelection = tableObserver.getAndClearShouldCheckSelection();\n    // If they pressed the down arrow with the selection outside of the\n    // table, and then the selection ends up in the table but not in the\n    // first cell, then move the selection to the first cell.\n    if (shouldCheckSelection && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed()) {\n      const anchor = selection.anchor.getNode();\n      const firstRow = tableNode.getFirstChild();\n      const anchorCell = $findCellNode(anchor);\n      if (anchorCell !== null && $isTableRowNode(firstRow)) {\n        const firstCell = firstRow.getFirstChild();\n        if ($isTableCellNode(firstCell) && tableNode.is((0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchorCell, node => node.is(tableNode) || node.is(firstCell)))) {\n          // The selection moved to the table, but not in the first cell\n          firstCell.selectStart();\n          return true;\n        }\n      }\n    }\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const {\n        anchor,\n        focus\n      } = selection;\n      const anchorNode = anchor.getNode();\n      const focusNode = focus.getNode();\n      // Using explicit comparison with table node to ensure it's not a nested table\n      // as in that case we'll leave selection resolving to that table\n      const anchorCellNode = $findCellNode(anchorNode);\n      const focusCellNode = $findCellNode(focusNode);\n      const isAnchorInside = !!(anchorCellNode && tableNode.is($findTableNode(anchorCellNode)));\n      const isFocusInside = !!(focusCellNode && tableNode.is($findTableNode(focusCellNode)));\n      const isPartiallyWithinTable = isAnchorInside !== isFocusInside;\n      const isWithinTable = isAnchorInside && isFocusInside;\n      const isBackward = selection.isBackward();\n      if (isPartiallyWithinTable) {\n        const newSelection = selection.clone();\n        if (isFocusInside) {\n          const [tableMap] = $computeTableMap(tableNode, focusCellNode, focusCellNode);\n          const firstCell = tableMap[0][0].cell;\n          const lastCell = tableMap[tableMap.length - 1].at(-1).cell;\n          newSelection.focus.set(isBackward ? firstCell.getKey() : lastCell.getKey(), isBackward ? firstCell.getChildrenSize() : lastCell.getChildrenSize(), 'element');\n        } else if (isAnchorInside) {\n          const [tableMap] = $computeTableMap(tableNode, anchorCellNode, anchorCellNode);\n          const firstCell = tableMap[0][0].cell;\n          const lastCell = tableMap[tableMap.length - 1].at(-1).cell;\n          /**\n           * If isBackward, set the anchor to be at the end of the table so that when the cursor moves outside of\n           * the table in the backward direction, the entire table will be selected from its end.\n           * Otherwise, if forward, set the anchor to be at the start of the table so that when the focus is dragged\n           * outside th end of the table, it will start from the beginning of the table.\n           */\n          newSelection.anchor.set(isBackward ? lastCell.getKey() : firstCell.getKey(), isBackward ? lastCell.getChildrenSize() : 0, 'element');\n        }\n        (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(newSelection);\n        $addHighlightStyleToTable(editor, tableObserver);\n      } else if (isWithinTable) {\n        // Handle case when selection spans across multiple cells but still\n        // has range selection, then we convert it into table selection\n        if (!anchorCellNode.is(focusCellNode)) {\n          tableObserver.$setAnchorCellForSelection($getObserverCellFromCellNodeOrThrow(tableObserver, anchorCellNode));\n          tableObserver.$setFocusCellForSelection($getObserverCellFromCellNodeOrThrow(tableObserver, focusCellNode), true);\n        }\n\n        // Handle case when the pointer type is touch and the current and\n        // previous selection are collapsed, and the previous anchor and current\n        // focus cell nodes are different, then we convert it into table selection\n        if (tableObserver.pointerType === 'touch' && selection.isCollapsed() && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && prevSelection.isCollapsed()) {\n          const prevAnchorCellNode = $findCellNode(prevSelection.anchor.getNode());\n          if (prevAnchorCellNode && !prevAnchorCellNode.is(focusCellNode)) {\n            tableObserver.$setAnchorCellForSelection($getObserverCellFromCellNodeOrThrow(tableObserver, prevAnchorCellNode));\n            tableObserver.$setFocusCellForSelection($getObserverCellFromCellNodeOrThrow(tableObserver, focusCellNode), true);\n            tableObserver.pointerType = null;\n          }\n        }\n      }\n    } else if (selection && $isTableSelection(selection) && selection.is(prevSelection) && selection.tableKey === tableNode.getKey()) {\n      // if selection goes outside of the table we need to change it to Range selection\n      const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(editorWindow);\n      if (domSelection && domSelection.anchorNode && domSelection.focusNode) {\n        const focusNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(domSelection.focusNode);\n        const isFocusOutside = focusNode && !tableNode.isParentOf(focusNode);\n        const anchorNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(domSelection.anchorNode);\n        const isAnchorInside = anchorNode && tableNode.isParentOf(anchorNode);\n        if (isFocusOutside && isAnchorInside && domSelection.rangeCount > 0) {\n          const newSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createRangeSelectionFromDom)(domSelection, editor);\n          if (newSelection) {\n            newSelection.anchor.set(tableNode.getKey(), selection.isBackward() ? tableNode.getChildrenSize() : 0, 'element');\n            domSelection.removeAllRanges();\n            (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(newSelection);\n          }\n        }\n      }\n    }\n    if (selection && !selection.is(prevSelection) && ($isTableSelection(selection) || $isTableSelection(prevSelection)) && tableObserver.tableSelection && !tableObserver.tableSelection.is(prevSelection)) {\n      if ($isTableSelection(selection) && selection.tableKey === tableObserver.tableNodeKey) {\n        tableObserver.$updateTableTableSelection(selection);\n      } else if (!$isTableSelection(selection) && $isTableSelection(prevSelection) && prevSelection.tableKey === tableObserver.tableNodeKey) {\n        tableObserver.$updateTableTableSelection(null);\n      }\n      return false;\n    }\n    if (tableObserver.hasHijackedSelectionStyles && !tableNode.isSelected()) {\n      $removeHighlightStyleToTable(editor, tableObserver);\n    } else if (!tableObserver.hasHijackedSelectionStyles && tableNode.isSelected()) {\n      $addHighlightStyleToTable(editor, tableObserver);\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  tableObserver.listenersToRemove.add(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || !selection.isCollapsed() || !$isSelectionInTable(selection, tableNode)) {\n      return false;\n    }\n    const edgePosition = $getTableEdgeCursorPosition(editor, selection, tableNode);\n    if (edgePosition) {\n      $insertParagraphAtTableEdge(edgePosition, tableNode);\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_CRITICAL));\n  return tableObserver;\n}\nfunction detatchTableObserverFromTableElement(tableElement, tableObserver) {\n  if (getTableObserverFromTableElement(tableElement) === tableObserver) {\n    delete tableElement[LEXICAL_ELEMENT_KEY];\n  }\n}\nfunction attachTableObserverToTableElement(tableElement, tableObserver) {\n  if (!(getTableObserverFromTableElement(tableElement) === null)) {\n    formatDevErrorMessage(`tableElement already has an attached TableObserver`);\n  }\n  tableElement[LEXICAL_ELEMENT_KEY] = tableObserver;\n}\nfunction getTableObserverFromTableElement(tableElement) {\n  return tableElement[LEXICAL_ELEMENT_KEY] || null;\n}\nfunction getDOMCellFromTarget(node) {\n  let currentNode = node;\n  while (currentNode != null) {\n    const nodeName = currentNode.nodeName;\n    if (nodeName === 'TD' || nodeName === 'TH') {\n      // @ts-expect-error: internal field\n      const cell = currentNode._cell;\n      if (cell === undefined) {\n        return null;\n      }\n      return cell;\n    }\n    currentNode = currentNode.parentNode;\n  }\n  return null;\n}\nfunction getDOMCellInTableFromTarget(table, node) {\n  if (!table.contains(node)) {\n    return null;\n  }\n  let cell = null;\n  for (let currentNode = node; currentNode != null; currentNode = currentNode.parentNode) {\n    if (currentNode === table) {\n      return cell;\n    }\n    const nodeName = currentNode.nodeName;\n    if (nodeName === 'TD' || nodeName === 'TH') {\n      // @ts-expect-error: internal field\n      cell = currentNode._cell || null;\n    }\n  }\n  return null;\n}\nfunction getTable(tableNode, dom) {\n  const tableElement = getTableElement(tableNode, dom);\n  const domRows = [];\n  const grid = {\n    columns: 0,\n    domRows,\n    rows: 0\n  };\n  let currentNode = tableElement.querySelector('tr');\n  let x = 0;\n  let y = 0;\n  domRows.length = 0;\n  while (currentNode != null) {\n    const nodeMame = currentNode.nodeName;\n    if (nodeMame === 'TD' || nodeMame === 'TH') {\n      const elem = currentNode;\n      const cell = {\n        elem,\n        hasBackgroundColor: elem.style.backgroundColor !== '',\n        highlighted: false,\n        x,\n        y\n      };\n\n      // @ts-expect-error: internal field\n      currentNode._cell = cell;\n      let row = domRows[y];\n      if (row === undefined) {\n        row = domRows[y] = [];\n      }\n      row[x] = cell;\n    } else {\n      const child = currentNode.firstChild;\n      if (child != null) {\n        currentNode = child;\n        continue;\n      }\n    }\n    const sibling = currentNode.nextSibling;\n    if (sibling != null) {\n      x++;\n      currentNode = sibling;\n      continue;\n    }\n    const parent = currentNode.parentNode;\n    if (parent != null) {\n      const parentSibling = parent.nextSibling;\n      if (parentSibling == null) {\n        break;\n      }\n      y++;\n      x = 0;\n      currentNode = parentSibling;\n    }\n  }\n  grid.columns = x + 1;\n  grid.rows = y + 1;\n  return grid;\n}\nfunction $updateDOMForSelection(editor, table, selection) {\n  const selectedCellNodes = new Set(selection ? selection.getNodes() : []);\n  $forEachTableCell(table, (cell, lexicalNode) => {\n    const elem = cell.elem;\n    if (selectedCellNodes.has(lexicalNode)) {\n      cell.highlighted = true;\n      $addHighlightToDOM(editor, cell);\n    } else {\n      cell.highlighted = false;\n      $removeHighlightFromDOM(editor, cell);\n      if (!elem.getAttribute('style')) {\n        elem.removeAttribute('style');\n      }\n    }\n  });\n}\nfunction $forEachTableCell(grid, cb) {\n  const {\n    domRows\n  } = grid;\n  for (let y = 0; y < domRows.length; y++) {\n    const row = domRows[y];\n    if (!row) {\n      continue;\n    }\n    for (let x = 0; x < row.length; x++) {\n      const cell = row[x];\n      if (!cell) {\n        continue;\n      }\n      const lexicalNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(cell.elem);\n      if (lexicalNode !== null) {\n        cb(cell, lexicalNode, {\n          x,\n          y\n        });\n      }\n    }\n  }\n}\nfunction $addHighlightStyleToTable(editor, tableSelection) {\n  tableSelection.$disableHighlightStyle();\n  $forEachTableCell(tableSelection.table, cell => {\n    cell.highlighted = true;\n    $addHighlightToDOM(editor, cell);\n  });\n}\nfunction $removeHighlightStyleToTable(editor, tableObserver) {\n  tableObserver.$enableHighlightStyle();\n  $forEachTableCell(tableObserver.table, cell => {\n    const elem = cell.elem;\n    cell.highlighted = false;\n    $removeHighlightFromDOM(editor, cell);\n    if (!elem.getAttribute('style')) {\n      elem.removeAttribute('style');\n    }\n  });\n}\nfunction $selectAdjacentCell(tableCellNode, direction) {\n  const siblingMethod = direction === 'next' ? 'getNextSibling' : 'getPreviousSibling';\n  const childMethod = direction === 'next' ? 'getFirstChild' : 'getLastChild';\n  const sibling = tableCellNode[siblingMethod]();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(sibling)) {\n    return sibling.selectEnd();\n  }\n  const parentRow = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(tableCellNode, $isTableRowNode);\n  if (!(parentRow !== null)) {\n    formatDevErrorMessage(`selectAdjacentCell: Cell not in table row`);\n  }\n  for (let nextRow = parentRow[siblingMethod](); $isTableRowNode(nextRow); nextRow = nextRow[siblingMethod]()) {\n    const child = nextRow[childMethod]();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(child)) {\n      return child.selectEnd();\n    }\n  }\n  const parentTable = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(parentRow, $isTableNode);\n  if (!(parentTable !== null)) {\n    formatDevErrorMessage(`selectAdjacentCell: Row not in table`);\n  }\n  return direction === 'next' ? parentTable.selectNext() : parentTable.selectPrevious();\n}\nconst selectTableNodeInDirection = (tableObserver, tableNode, x, y, direction) => {\n  const isForward = direction === 'forward';\n  switch (direction) {\n    case 'backward':\n    case 'forward':\n      if (x !== (isForward ? tableObserver.table.columns - 1 : 0)) {\n        selectTableCellNode(tableNode.getCellNodeFromCordsOrThrow(x + (isForward ? 1 : -1), y, tableObserver.table), isForward);\n      } else {\n        if (y !== (isForward ? tableObserver.table.rows - 1 : 0)) {\n          selectTableCellNode(tableNode.getCellNodeFromCordsOrThrow(isForward ? 0 : tableObserver.table.columns - 1, y + (isForward ? 1 : -1), tableObserver.table), isForward);\n        } else if (!isForward) {\n          tableNode.selectPrevious();\n        } else {\n          tableNode.selectNext();\n        }\n      }\n      return true;\n    case 'up':\n      if (y !== 0) {\n        selectTableCellNode(tableNode.getCellNodeFromCordsOrThrow(x, y - 1, tableObserver.table), false);\n      } else {\n        tableNode.selectPrevious();\n      }\n      return true;\n    case 'down':\n      if (y !== tableObserver.table.rows - 1) {\n        selectTableCellNode(tableNode.getCellNodeFromCordsOrThrow(x, y + 1, tableObserver.table), true);\n      } else {\n        tableNode.selectNext();\n      }\n      return true;\n    default:\n      return false;\n  }\n};\nfunction getCorner(rect, cellValue) {\n  let colName;\n  let rowName;\n  if (cellValue.startColumn === rect.minColumn) {\n    colName = 'minColumn';\n  } else if (cellValue.startColumn + cellValue.cell.__colSpan - 1 === rect.maxColumn) {\n    colName = 'maxColumn';\n  } else {\n    return null;\n  }\n  if (cellValue.startRow === rect.minRow) {\n    rowName = 'minRow';\n  } else if (cellValue.startRow + cellValue.cell.__rowSpan - 1 === rect.maxRow) {\n    rowName = 'maxRow';\n  } else {\n    return null;\n  }\n  return [colName, rowName];\n}\nfunction getCornerOrThrow(rect, cellValue) {\n  const corner = getCorner(rect, cellValue);\n  if (!(corner !== null)) {\n    formatDevErrorMessage(`getCornerOrThrow: cell ${cellValue.cell.getKey()} is not at a corner of rect`);\n  }\n  return corner;\n}\nfunction oppositeCorner([colName, rowName]) {\n  return [colName === 'minColumn' ? 'maxColumn' : 'minColumn', rowName === 'minRow' ? 'maxRow' : 'minRow'];\n}\nfunction cellAtCornerOrThrow(tableMap, rect, [colName, rowName]) {\n  const rowNum = rect[rowName];\n  const rowMap = tableMap[rowNum];\n  if (!(rowMap !== undefined)) {\n    formatDevErrorMessage(`cellAtCornerOrThrow: ${rowName} = ${String(rowNum)} missing in tableMap`);\n  }\n  const colNum = rect[colName];\n  const cell = rowMap[colNum];\n  if (!(cell !== undefined)) {\n    formatDevErrorMessage(`cellAtCornerOrThrow: ${colName} = ${String(colNum)} missing in tableMap`);\n  }\n  return cell;\n}\nfunction $extractRectCorners(tableMap, anchorCellValue, newFocusCellValue) {\n  // We are sure that the focus now either contracts or expands the rect\n  // but both the anchor and focus might be moved to ensure a rectangle\n  // given a potentially ragged merge shape\n  const rect = $computeTableCellRectBoundary(tableMap, anchorCellValue, newFocusCellValue);\n  const anchorCorner = getCorner(rect, anchorCellValue);\n  if (anchorCorner) {\n    return [cellAtCornerOrThrow(tableMap, rect, anchorCorner), cellAtCornerOrThrow(tableMap, rect, oppositeCorner(anchorCorner))];\n  }\n  const newFocusCorner = getCorner(rect, newFocusCellValue);\n  if (newFocusCorner) {\n    return [cellAtCornerOrThrow(tableMap, rect, oppositeCorner(newFocusCorner)), cellAtCornerOrThrow(tableMap, rect, newFocusCorner)];\n  }\n  // TODO this doesn't have to be arbitrary, use the closest corner instead\n  const newAnchorCorner = ['minColumn', 'minRow'];\n  return [cellAtCornerOrThrow(tableMap, rect, newAnchorCorner), cellAtCornerOrThrow(tableMap, rect, oppositeCorner(newAnchorCorner))];\n}\nfunction $adjustFocusInDirection(tableObserver, tableMap, anchorCellValue, focusCellValue, direction) {\n  const rect = $computeTableCellRectBoundary(tableMap, anchorCellValue, focusCellValue);\n  const spans = $computeTableCellRectSpans(tableMap, rect);\n  const {\n    topSpan,\n    leftSpan,\n    bottomSpan,\n    rightSpan\n  } = spans;\n  const anchorCorner = getCornerOrThrow(rect, anchorCellValue);\n  const [focusColumn, focusRow] = oppositeCorner(anchorCorner);\n  let fCol = rect[focusColumn];\n  let fRow = rect[focusRow];\n  if (direction === 'forward') {\n    fCol += focusColumn === 'maxColumn' ? 1 : leftSpan;\n  } else if (direction === 'backward') {\n    fCol -= focusColumn === 'minColumn' ? 1 : rightSpan;\n  } else if (direction === 'down') {\n    fRow += focusRow === 'maxRow' ? 1 : topSpan;\n  } else if (direction === 'up') {\n    fRow -= focusRow === 'minRow' ? 1 : bottomSpan;\n  }\n  const targetRowMap = tableMap[fRow];\n  if (targetRowMap === undefined) {\n    return false;\n  }\n  const newFocusCellValue = targetRowMap[fCol];\n  if (newFocusCellValue === undefined) {\n    return false;\n  }\n  // We can be certain that anchorCellValue and newFocusCellValue are\n  // contained within the desired selection, but we are not certain if\n  // they need to be expanded or not to maintain a rectangular shape\n  const [finalAnchorCell, finalFocusCell] = $extractRectCorners(tableMap, anchorCellValue, newFocusCellValue);\n  const anchorDOM = $getObserverCellFromCellNodeOrThrow(tableObserver, finalAnchorCell.cell);\n  const focusDOM = $getObserverCellFromCellNodeOrThrow(tableObserver, finalFocusCell.cell);\n  tableObserver.$setAnchorCellForSelection(anchorDOM);\n  tableObserver.$setFocusCellForSelection(focusDOM, true);\n  return true;\n}\nfunction $isSelectionInTable(selection, tableNode) {\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || $isTableSelection(selection)) {\n    // TODO this should probably return false if there's an unrelated\n    //      shadow root between the node and the table (e.g. another table,\n    //      collapsible, etc.)\n    const isAnchorInside = tableNode.isParentOf(selection.anchor.getNode());\n    const isFocusInside = tableNode.isParentOf(selection.focus.getNode());\n    return isAnchorInside && isFocusInside;\n  }\n  return false;\n}\nfunction $isFullTableSelection(selection, tableNode) {\n  if ($isTableSelection(selection)) {\n    const anchorNode = selection.anchor.getNode();\n    const focusNode = selection.focus.getNode();\n    if (tableNode && anchorNode && focusNode) {\n      const [map] = $computeTableMap(tableNode, anchorNode, focusNode);\n      return anchorNode.getKey() === map[0][0].cell.getKey() && focusNode.getKey() === map[map.length - 1].at(-1).cell.getKey();\n    }\n  }\n  return false;\n}\nfunction selectTableCellNode(tableCell, fromStart) {\n  if (fromStart) {\n    tableCell.selectStart();\n  } else {\n    tableCell.selectEnd();\n  }\n}\nfunction $addHighlightToDOM(editor, cell) {\n  const element = cell.elem;\n  const editorThemeClasses = editor._config.theme;\n  const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(element);\n  if (!$isTableCellNode(node)) {\n    formatDevErrorMessage(`Expected to find LexicalNode from Table Cell DOMNode`);\n  }\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, editorThemeClasses.tableCellSelected);\n}\nfunction $removeHighlightFromDOM(editor, cell) {\n  const element = cell.elem;\n  const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(element);\n  if (!$isTableCellNode(node)) {\n    formatDevErrorMessage(`Expected to find LexicalNode from Table Cell DOMNode`);\n  }\n  const editorThemeClasses = editor._config.theme;\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(element, editorThemeClasses.tableCellSelected);\n}\nfunction $findCellNode(node) {\n  const cellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, $isTableCellNode);\n  return $isTableCellNode(cellNode) ? cellNode : null;\n}\nfunction $findTableNode(node) {\n  const tableNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, $isTableNode);\n  return $isTableNode(tableNode) ? tableNode : null;\n}\nfunction $getBlockParentIfFirstNode(node) {\n  for (let prevNode = node, currentNode = node; currentNode !== null; prevNode = currentNode, currentNode = currentNode.getParent()) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode)) {\n      if (currentNode !== prevNode && currentNode.getFirstChild() !== prevNode) {\n        // Not the first child or the initial node\n        return null;\n      } else if (!currentNode.isInline()) {\n        return currentNode;\n      }\n    }\n  }\n  return null;\n}\nfunction $handleHorizontalArrowKeyRangeSelection(editor, event, selection, alter, isBackward, tableNode, tableObserver) {\n  const initialFocus = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$caretFromPoint)(selection.focus, isBackward ? 'previous' : 'next');\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isExtendableTextPointCaret)(initialFocus)) {\n    return false;\n  }\n  let lastCaret = initialFocus;\n  // TableCellNode is the only shadow root we are interested in piercing so\n  // we find the last internal caret and then check its parent\n  for (const nextCaret of (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$extendCaretToRange)(initialFocus).iterNodeCarets('shadowRoot')) {\n    if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isSiblingCaret)(nextCaret) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(nextCaret.origin))) {\n      return false;\n    }\n    lastCaret = nextCaret;\n  }\n  const lastCaretParent = lastCaret.getParentAtCaret();\n  if (!$isTableCellNode(lastCaretParent)) {\n    return false;\n  }\n  const anchorCell = lastCaretParent;\n  const focusCaret = $findNextTableCell((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(anchorCell, lastCaret.direction));\n  const anchorCellTable = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchorCell, $isTableNode);\n  if (!(anchorCellTable && anchorCellTable.is(tableNode))) {\n    return false;\n  }\n  const anchorCellDOM = editor.getElementByKey(anchorCell.getKey());\n  const anchorDOMCell = getDOMCellFromTarget(anchorCellDOM);\n  if (!anchorCellDOM || !anchorDOMCell) {\n    return false;\n  }\n  const anchorCellTableElement = $getElementForTableNode(editor, anchorCellTable);\n  tableObserver.table = anchorCellTableElement;\n  if (!focusCaret) {\n    if (alter === 'extend') {\n      // extend the selection from a range inside the cell to a table selection of the cell\n      tableObserver.$setAnchorCellForSelection(anchorDOMCell);\n      tableObserver.$setFocusCellForSelection(anchorDOMCell, true);\n    } else {\n      // exit the table\n      const outerFocusCaret = $getTableExitCaret((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(anchorCellTable, initialFocus.direction));\n      (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.anchor, outerFocusCaret);\n      (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.focus, outerFocusCaret);\n    }\n  } else if (alter === 'extend') {\n    const focusDOMCell = getDOMCellFromTarget(editor.getElementByKey(focusCaret.origin.getKey()));\n    if (!focusDOMCell) {\n      return false;\n    }\n    tableObserver.$setAnchorCellForSelection(anchorDOMCell);\n    tableObserver.$setFocusCellForSelection(focusDOMCell, true);\n  } else {\n    // alter === 'move'\n    const innerFocusCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeCaret)(focusCaret);\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.anchor, innerFocusCaret);\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setPointFromCaret)(selection.focus, innerFocusCaret);\n  }\n  stopEvent(event);\n  return true;\n}\nfunction $getTableExitCaret(initialCaret) {\n  const adjacent = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)(initialCaret);\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(adjacent) ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeCaret)(adjacent) : initialCaret;\n}\nfunction $findNextTableCell(initialCaret) {\n  for (const nextCaret of (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$extendCaretToRange)(initialCaret).iterNodeCarets('root')) {\n    const {\n      origin\n    } = nextCaret;\n    if ($isTableCellNode(origin)) {\n      // not sure why ts isn't narrowing here (even if the guard is on nextCaret.origin)\n      // but returning a new caret is fine\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(nextCaret)) {\n        return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(origin, initialCaret.direction);\n      }\n    } else if (!$isTableRowNode(origin)) {\n      break;\n    }\n  }\n  return null;\n}\nfunction $handleArrowKey(editor, event, direction, tableNode, tableObserver) {\n  if ((direction === 'up' || direction === 'down') && isTypeaheadMenuInView(editor)) {\n    return false;\n  }\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!$isSelectionInTable(selection, tableNode)) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      if (direction === 'backward') {\n        if (selection.focus.offset > 0) {\n          return false;\n        }\n        const parentNode = $getBlockParentIfFirstNode(selection.focus.getNode());\n        if (!parentNode) {\n          return false;\n        }\n        const siblingNode = parentNode.getPreviousSibling();\n        if (!$isTableNode(siblingNode)) {\n          return false;\n        }\n        stopEvent(event);\n        if (event.shiftKey) {\n          selection.focus.set(siblingNode.getParentOrThrow().getKey(), siblingNode.getIndexWithinParent(), 'element');\n        } else {\n          siblingNode.selectEnd();\n        }\n        return true;\n      } else if (event.shiftKey && (direction === 'up' || direction === 'down')) {\n        const focusNode = selection.focus.getNode();\n        const isTableUnselect = !selection.isCollapsed() && (direction === 'up' && !selection.isBackward() || direction === 'down' && selection.isBackward());\n        if (isTableUnselect) {\n          let focusParentNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focusNode, n => $isTableNode(n));\n          if ($isTableCellNode(focusParentNode)) {\n            focusParentNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focusParentNode, $isTableNode);\n          }\n          if (focusParentNode !== tableNode) {\n            return false;\n          }\n          if (!focusParentNode) {\n            return false;\n          }\n          const sibling = direction === 'down' ? focusParentNode.getNextSibling() : focusParentNode.getPreviousSibling();\n          if (!sibling) {\n            return false;\n          }\n          let newOffset = 0;\n          if (direction === 'up') {\n            if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(sibling)) {\n              newOffset = sibling.getChildrenSize();\n            }\n          }\n          let newFocusNode = sibling;\n          if (direction === 'up') {\n            if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(sibling)) {\n              const lastCell = sibling.getLastChild();\n              newFocusNode = lastCell ? lastCell : sibling;\n              newOffset = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(newFocusNode) ? newFocusNode.getTextContentSize() : 0;\n            }\n          }\n          const newSelection = selection.clone();\n          newSelection.focus.set(newFocusNode.getKey(), newOffset, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(newFocusNode) ? 'text' : 'element');\n          (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(newSelection);\n          stopEvent(event);\n          return true;\n        } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(focusNode)) {\n          const selectedNode = direction === 'up' ? selection.getNodes()[selection.getNodes().length - 1] : selection.getNodes()[0];\n          if (selectedNode) {\n            const tableCellNode = $findParentTableCellNodeInTable(tableNode, selectedNode);\n            if (tableCellNode !== null) {\n              const firstDescendant = tableNode.getFirstDescendant();\n              const lastDescendant = tableNode.getLastDescendant();\n              if (!firstDescendant || !lastDescendant) {\n                return false;\n              }\n              const [firstCellNode] = $getNodeTriplet(firstDescendant);\n              const [lastCellNode] = $getNodeTriplet(lastDescendant);\n              const firstCellCoords = tableNode.getCordsFromCellNode(firstCellNode, tableObserver.table);\n              const lastCellCoords = tableNode.getCordsFromCellNode(lastCellNode, tableObserver.table);\n              const firstCellDOM = tableNode.getDOMCellFromCordsOrThrow(firstCellCoords.x, firstCellCoords.y, tableObserver.table);\n              const lastCellDOM = tableNode.getDOMCellFromCordsOrThrow(lastCellCoords.x, lastCellCoords.y, tableObserver.table);\n              tableObserver.$setAnchorCellForSelection(firstCellDOM);\n              tableObserver.$setFocusCellForSelection(lastCellDOM, true);\n              return true;\n            }\n          }\n          return false;\n        } else {\n          let focusParentNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focusNode, n => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(n) && !n.isInline());\n          if ($isTableCellNode(focusParentNode)) {\n            focusParentNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focusParentNode, $isTableNode);\n          }\n          if (!focusParentNode) {\n            return false;\n          }\n          const sibling = direction === 'down' ? focusParentNode.getNextSibling() : focusParentNode.getPreviousSibling();\n          if ($isTableNode(sibling) && tableObserver.tableNodeKey === sibling.getKey()) {\n            const firstDescendant = sibling.getFirstDescendant();\n            const lastDescendant = sibling.getLastDescendant();\n            if (!firstDescendant || !lastDescendant) {\n              return false;\n            }\n            const [firstCellNode] = $getNodeTriplet(firstDescendant);\n            const [lastCellNode] = $getNodeTriplet(lastDescendant);\n            const newSelection = selection.clone();\n            newSelection.focus.set((direction === 'up' ? firstCellNode : lastCellNode).getKey(), direction === 'up' ? 0 : lastCellNode.getChildrenSize(), 'element');\n            stopEvent(event);\n            (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(newSelection);\n            return true;\n          }\n        }\n      }\n    }\n    if (direction === 'down' && $isScrollableTablesActive(editor)) {\n      // Enable Firefox workaround\n      tableObserver.setShouldCheckSelection();\n    }\n    return false;\n  }\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    if (direction === 'backward' || direction === 'forward') {\n      const alter = event.shiftKey ? 'extend' : 'move';\n      return $handleHorizontalArrowKeyRangeSelection(editor, event, selection, alter, direction === 'backward', tableNode, tableObserver);\n    }\n    if (selection.isCollapsed()) {\n      const {\n        anchor,\n        focus\n      } = selection;\n      const anchorCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchor.getNode(), $isTableCellNode);\n      const focusCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focus.getNode(), $isTableCellNode);\n      if (!$isTableCellNode(anchorCellNode) || !anchorCellNode.is(focusCellNode)) {\n        return false;\n      }\n      const anchorCellTable = $findTableNode(anchorCellNode);\n      if (anchorCellTable !== tableNode && anchorCellTable != null) {\n        const anchorCellTableElement = getTableElement(anchorCellTable, editor.getElementByKey(anchorCellTable.getKey()));\n        if (anchorCellTableElement != null) {\n          tableObserver.table = getTable(anchorCellTable, anchorCellTableElement);\n          return $handleArrowKey(editor, event, direction, anchorCellTable, tableObserver);\n        }\n      }\n      const anchorCellDom = editor.getElementByKey(anchorCellNode.__key);\n      const anchorDOM = editor.getElementByKey(anchor.key);\n      if (anchorDOM == null || anchorCellDom == null) {\n        return false;\n      }\n      let edgeSelectionRect;\n      if (anchor.type === 'element') {\n        edgeSelectionRect = anchorDOM.getBoundingClientRect();\n      } else {\n        const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(getEditorWindow(editor));\n        if (domSelection === null || domSelection.rangeCount === 0) {\n          return false;\n        }\n        const range = domSelection.getRangeAt(0);\n        edgeSelectionRect = range.getBoundingClientRect();\n      }\n      const edgeChild = direction === 'up' ? anchorCellNode.getFirstChild() : anchorCellNode.getLastChild();\n      if (edgeChild == null) {\n        return false;\n      }\n      const edgeChildDOM = editor.getElementByKey(edgeChild.__key);\n      if (edgeChildDOM == null) {\n        return false;\n      }\n      const edgeRect = edgeChildDOM.getBoundingClientRect();\n      const isExiting = direction === 'up' ? edgeRect.top > edgeSelectionRect.top - edgeSelectionRect.height : edgeSelectionRect.bottom + edgeSelectionRect.height > edgeRect.bottom;\n      if (isExiting) {\n        stopEvent(event);\n        const cords = tableNode.getCordsFromCellNode(anchorCellNode, tableObserver.table);\n        if (event.shiftKey) {\n          const cell = tableNode.getDOMCellFromCordsOrThrow(cords.x, cords.y, tableObserver.table);\n          tableObserver.$setAnchorCellForSelection(cell);\n          tableObserver.$setFocusCellForSelection(cell, true);\n        } else {\n          return selectTableNodeInDirection(tableObserver, tableNode, cords.x, cords.y, direction);\n        }\n        return true;\n      }\n    }\n  } else if ($isTableSelection(selection)) {\n    const {\n      anchor,\n      focus\n    } = selection;\n    const anchorCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchor.getNode(), $isTableCellNode);\n    const focusCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(focus.getNode(), $isTableCellNode);\n    const [tableNodeFromSelection] = selection.getNodes();\n    if (!$isTableNode(tableNodeFromSelection)) {\n      formatDevErrorMessage(`$handleArrowKey: TableSelection.getNodes()[0] expected to be TableNode`);\n    }\n    const tableElement = getTableElement(tableNodeFromSelection, editor.getElementByKey(tableNodeFromSelection.getKey()));\n    if (!$isTableCellNode(anchorCellNode) || !$isTableCellNode(focusCellNode) || !$isTableNode(tableNodeFromSelection) || tableElement == null) {\n      return false;\n    }\n    tableObserver.$updateTableTableSelection(selection);\n    const grid = getTable(tableNodeFromSelection, tableElement);\n    const cordsAnchor = tableNode.getCordsFromCellNode(anchorCellNode, grid);\n    const anchorCell = tableNode.getDOMCellFromCordsOrThrow(cordsAnchor.x, cordsAnchor.y, grid);\n    tableObserver.$setAnchorCellForSelection(anchorCell);\n    stopEvent(event);\n    if (event.shiftKey) {\n      const [tableMap, anchorValue, focusValue] = $computeTableMap(tableNode, anchorCellNode, focusCellNode);\n      return $adjustFocusInDirection(tableObserver, tableMap, anchorValue, focusValue, direction);\n    } else {\n      focusCellNode.selectEnd();\n    }\n    return true;\n  }\n  return false;\n}\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n  event.stopPropagation();\n}\nfunction isTypeaheadMenuInView(editor) {\n  // There is no inbuilt way to check if the component picker is in view\n  // but we can check if the root DOM element has the aria-controls attribute \"typeahead-menu\".\n  const root = editor.getRootElement();\n  if (!root) {\n    return false;\n  }\n  return root.hasAttribute('aria-controls') && root.getAttribute('aria-controls') === 'typeahead-menu';\n}\nfunction $insertParagraphAtTableEdge(edgePosition, tableNode, children) {\n  const paragraphNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n  if (edgePosition === 'first') {\n    tableNode.insertBefore(paragraphNode);\n  } else {\n    tableNode.insertAfter(paragraphNode);\n  }\n  paragraphNode.append(...(children || []));\n  paragraphNode.selectEnd();\n}\nfunction $getTableEdgeCursorPosition(editor, selection, tableNode) {\n  const tableNodeParent = tableNode.getParent();\n  if (!tableNodeParent) {\n    return undefined;\n  }\n\n  // TODO: Add support for nested tables\n  const domSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMSelection)(getEditorWindow(editor));\n  if (!domSelection) {\n    return undefined;\n  }\n  const domAnchorNode = domSelection.anchorNode;\n  const tableNodeParentDOM = editor.getElementByKey(tableNodeParent.getKey());\n  const tableElement = getTableElement(tableNode, editor.getElementByKey(tableNode.getKey()));\n  // We are only interested in the scenario where the\n  // native selection anchor is:\n  // - at or inside the table's parent DOM\n  // - and NOT at or inside the table DOM\n  // It may be adjacent to the table DOM (e.g. in a wrapper)\n  if (!domAnchorNode || !tableNodeParentDOM || !tableElement || !tableNodeParentDOM.contains(domAnchorNode) || tableElement.contains(domAnchorNode)) {\n    return undefined;\n  }\n  const anchorCellNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(selection.anchor.getNode(), n => $isTableCellNode(n));\n  if (!anchorCellNode) {\n    return undefined;\n  }\n  const parentTable = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(anchorCellNode, n => $isTableNode(n));\n  if (!$isTableNode(parentTable) || !parentTable.is(tableNode)) {\n    return undefined;\n  }\n  const [tableMap, cellValue] = $computeTableMap(tableNode, anchorCellNode, anchorCellNode);\n  const firstCell = tableMap[0][0];\n  const lastCell = tableMap[tableMap.length - 1][tableMap[0].length - 1];\n  const {\n    startRow,\n    startColumn\n  } = cellValue;\n  const isAtFirstCell = startRow === firstCell.startRow && startColumn === firstCell.startColumn;\n  const isAtLastCell = startRow === lastCell.startRow && startColumn === lastCell.startColumn;\n  if (isAtFirstCell) {\n    return 'first';\n  } else if (isAtLastCell) {\n    return 'last';\n  } else {\n    return undefined;\n  }\n}\nfunction $getObserverCellFromCellNodeOrThrow(tableObserver, tableCellNode) {\n  const {\n    tableNode\n  } = tableObserver.$lookup();\n  const currentCords = tableNode.getCordsFromCellNode(tableCellNode, tableObserver.table);\n  return tableNode.getDOMCellFromCordsOrThrow(currentCords.x, currentCords.y, tableObserver.table);\n}\nfunction $getNearestTableCellInTableFromDOMNode(tableNode, startingDOM, editorState) {\n  return $findParentTableCellNodeInTable(tableNode, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(startingDOM, editorState));\n}\n\nfunction updateColgroup(dom, config, colCount, colWidths) {\n  const colGroup = dom.querySelector('colgroup');\n  if (!colGroup) {\n    return;\n  }\n  const cols = [];\n  for (let i = 0; i < colCount; i++) {\n    const col = document.createElement('col');\n    const width = colWidths && colWidths[i];\n    if (width) {\n      col.style.width = `${width}px`;\n    }\n    cols.push(col);\n  }\n  colGroup.replaceChildren(...cols);\n}\nfunction setRowStriping(dom, config, rowStriping) {\n  if (rowStriping) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, config.theme.tableRowStriping);\n    dom.setAttribute('data-lexical-row-striping', 'true');\n  } else {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, config.theme.tableRowStriping);\n    dom.removeAttribute('data-lexical-row-striping');\n  }\n}\nfunction setFrozenColumns(dom, config, frozenColumnCount) {\n  if (frozenColumnCount > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, config.theme.tableFrozenColumn);\n    dom.setAttribute('data-lexical-frozen-column', 'true');\n  } else {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, config.theme.tableFrozenColumn);\n    dom.removeAttribute('data-lexical-frozen-column');\n  }\n}\nfunction setFrozenRows(dom, config, frozenRowCount) {\n  if (frozenRowCount > 0) {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, config.theme.tableFrozenRow);\n    dom.setAttribute('data-lexical-frozen-row', 'true');\n  } else {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, config.theme.tableFrozenRow);\n    dom.removeAttribute('data-lexical-frozen-row');\n  }\n}\nfunction alignTableElement(dom, config, formatType) {\n  if (!config.theme.tableAlignment) {\n    return;\n  }\n  const removeClasses = [];\n  const addClasses = [];\n  for (const format of ['center', 'right']) {\n    const classes = config.theme.tableAlignment[format];\n    if (!classes) {\n      continue;\n    }\n    (format === formatType ? addClasses : removeClasses).push(classes);\n  }\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(dom, ...removeClasses);\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(dom, ...addClasses);\n}\nconst scrollableEditors = new WeakSet();\nfunction $isScrollableTablesActive(editor = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getEditor)()) {\n  return scrollableEditors.has(editor);\n}\nfunction setScrollableTablesActive(editor, active) {\n  if (active) {\n    if (!editor._config.theme.tableScrollableWrapper) {\n      console.warn('TableNode: hasHorizontalScroll is active but theme.tableScrollableWrapper is not defined.');\n    }\n    scrollableEditors.add(editor);\n  } else {\n    scrollableEditors.delete(editor);\n  }\n}\n\n/** @noInheritDoc */\nclass TableNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  static getType() {\n    return 'table';\n  }\n  getColWidths() {\n    const self = this.getLatest();\n    return self.__colWidths;\n  }\n  setColWidths(colWidths) {\n    const self = this.getWritable();\n    // NOTE: Node properties should be immutable. Freeze to prevent accidental mutation.\n    self.__colWidths = colWidths !== undefined && true ? Object.freeze(colWidths) : colWidths;\n    return self;\n  }\n  static clone(node) {\n    return new TableNode(node.__key);\n  }\n  afterCloneFrom(prevNode) {\n    super.afterCloneFrom(prevNode);\n    this.__colWidths = prevNode.__colWidths;\n    this.__rowStriping = prevNode.__rowStriping;\n    this.__frozenColumnCount = prevNode.__frozenColumnCount;\n    this.__frozenRowCount = prevNode.__frozenRowCount;\n  }\n  static importDOM() {\n    return {\n      table: _node => ({\n        conversion: $convertTableElement,\n        priority: 1\n      })\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createTableNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setRowStriping(serializedNode.rowStriping || false).setFrozenColumns(serializedNode.frozenColumnCount || 0).setFrozenRows(serializedNode.frozenRowCount || 0).setColWidths(serializedNode.colWidths);\n  }\n  constructor(key) {\n    super(key);\n    this.__rowStriping = false;\n    this.__frozenColumnCount = 0;\n    this.__frozenRowCount = 0;\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      colWidths: this.getColWidths(),\n      frozenColumnCount: this.__frozenColumnCount ? this.__frozenColumnCount : undefined,\n      frozenRowCount: this.__frozenRowCount ? this.__frozenRowCount : undefined,\n      rowStriping: this.__rowStriping ? this.__rowStriping : undefined\n    };\n  }\n  extractWithChild(child, selection, destination) {\n    return destination === 'html';\n  }\n  getDOMSlot(element) {\n    const tableElement = !isHTMLTableElement(element) ? element.querySelector('table') : element;\n    if (!isHTMLTableElement(tableElement)) {\n      formatDevErrorMessage(`TableNode.getDOMSlot: createDOM() did not return a table`);\n    }\n    return super.getDOMSlot(element).withElement(tableElement).withAfter(tableElement.querySelector('colgroup'));\n  }\n  createDOM(config, editor) {\n    const tableElement = document.createElement('table');\n    if (this.__style) {\n      tableElement.style.cssText = this.__style;\n    }\n    const colGroup = document.createElement('colgroup');\n    tableElement.appendChild(colGroup);\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.setDOMUnmanaged)(colGroup);\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(tableElement, config.theme.table);\n    this.updateTableElement(null, tableElement, config);\n    if ($isScrollableTablesActive(editor)) {\n      const wrapperElement = document.createElement('div');\n      const classes = config.theme.tableScrollableWrapper;\n      if (classes) {\n        (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(wrapperElement, classes);\n      } else {\n        wrapperElement.style.cssText = 'overflow-x: auto;';\n      }\n      wrapperElement.appendChild(tableElement);\n      return wrapperElement;\n    }\n    return tableElement;\n  }\n  updateTableElement(prevNode, tableElement, config) {\n    if (this.__style !== (prevNode ? prevNode.__style : '')) {\n      tableElement.style.cssText = this.__style;\n    }\n    if (this.__rowStriping !== (prevNode ? prevNode.__rowStriping : false)) {\n      setRowStriping(tableElement, config, this.__rowStriping);\n    }\n    if (this.__frozenColumnCount !== (prevNode ? prevNode.__frozenColumnCount : 0)) {\n      setFrozenColumns(tableElement, config, this.__frozenColumnCount);\n    }\n    if (this.__frozenRowCount !== (prevNode ? prevNode.__frozenRowCount : 0)) {\n      setFrozenRows(tableElement, config, this.__frozenRowCount);\n    }\n    updateColgroup(tableElement, config, this.getColumnCount(), this.getColWidths());\n    alignTableElement(tableElement, config, this.getFormatType());\n  }\n  updateDOM(prevNode, dom, config) {\n    this.updateTableElement(prevNode, this.getDOMSlot(dom).element, config);\n    return false;\n  }\n  exportDOM(editor) {\n    const superExport = super.exportDOM(editor);\n    const {\n      element\n    } = superExport;\n    return {\n      after: tableElement => {\n        if (superExport.after) {\n          tableElement = superExport.after(tableElement);\n        }\n        if (!isHTMLTableElement(tableElement) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(tableElement)) {\n          tableElement = tableElement.querySelector('table');\n        }\n        if (!isHTMLTableElement(tableElement)) {\n          return null;\n        }\n        alignTableElement(tableElement, editor._config, this.getFormatType());\n\n        // Scan the table map to build a map of table cell key to the columns it needs\n        const [tableMap] = $computeTableMapSkipCellCheck(this, null, null);\n        const cellValues = new Map();\n        for (const mapRow of tableMap) {\n          for (const mapValue of mapRow) {\n            const key = mapValue.cell.getKey();\n            if (!cellValues.has(key)) {\n              cellValues.set(key, {\n                colSpan: mapValue.cell.getColSpan(),\n                startColumn: mapValue.startColumn\n              });\n            }\n          }\n        }\n\n        // scan the DOM to find the table cell keys that were used and mark those columns\n        const knownColumns = new Set();\n        for (const cellDOM of tableElement.querySelectorAll(':scope > tr > [data-temporary-table-cell-lexical-key]')) {\n          const key = cellDOM.getAttribute('data-temporary-table-cell-lexical-key');\n          if (key) {\n            const cellSpan = cellValues.get(key);\n            cellDOM.removeAttribute('data-temporary-table-cell-lexical-key');\n            if (cellSpan) {\n              cellValues.delete(key);\n              for (let i = 0; i < cellSpan.colSpan; i++) {\n                knownColumns.add(i + cellSpan.startColumn);\n              }\n            }\n          }\n        }\n\n        // Compute the colgroup and columns in the export\n        const colGroup = tableElement.querySelector(':scope > colgroup');\n        if (colGroup) {\n          // Only include the <col /> for rows that are in the output\n          const cols = Array.from(tableElement.querySelectorAll(':scope > colgroup > col')).filter((dom, i) => knownColumns.has(i));\n          colGroup.replaceChildren(...cols);\n        }\n\n        // Wrap direct descendant rows in a tbody for export\n        const rows = tableElement.querySelectorAll(':scope > tr');\n        if (rows.length > 0) {\n          const tBody = document.createElement('tbody');\n          for (const row of rows) {\n            tBody.appendChild(row);\n          }\n          tableElement.append(tBody);\n        }\n        return tableElement;\n      },\n      element: !isHTMLTableElement(element) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) ? element.querySelector('table') : element\n    };\n  }\n  canBeEmpty() {\n    return false;\n  }\n  isShadowRoot() {\n    return true;\n  }\n  getCordsFromCellNode(tableCellNode, table) {\n    const {\n      rows,\n      domRows\n    } = table;\n    for (let y = 0; y < rows; y++) {\n      const row = domRows[y];\n      if (row == null) {\n        continue;\n      }\n      for (let x = 0; x < row.length; x++) {\n        const cell = row[x];\n        if (cell == null) {\n          continue;\n        }\n        const {\n          elem\n        } = cell;\n        const cellNode = $getNearestTableCellInTableFromDOMNode(this, elem);\n        if (cellNode !== null && tableCellNode.is(cellNode)) {\n          return {\n            x,\n            y\n          };\n        }\n      }\n    }\n    throw new Error('Cell not found in table.');\n  }\n  getDOMCellFromCords(x, y, table) {\n    const {\n      domRows\n    } = table;\n    const row = domRows[y];\n    if (row == null) {\n      return null;\n    }\n    const index = x < row.length ? x : row.length - 1;\n    const cell = row[index];\n    if (cell == null) {\n      return null;\n    }\n    return cell;\n  }\n  getDOMCellFromCordsOrThrow(x, y, table) {\n    const cell = this.getDOMCellFromCords(x, y, table);\n    if (!cell) {\n      throw new Error('Cell not found at cords.');\n    }\n    return cell;\n  }\n  getCellNodeFromCords(x, y, table) {\n    const cell = this.getDOMCellFromCords(x, y, table);\n    if (cell == null) {\n      return null;\n    }\n    const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(cell.elem);\n    if ($isTableCellNode(node)) {\n      return node;\n    }\n    return null;\n  }\n  getCellNodeFromCordsOrThrow(x, y, table) {\n    const node = this.getCellNodeFromCords(x, y, table);\n    if (!node) {\n      throw new Error('Node at cords not TableCellNode.');\n    }\n    return node;\n  }\n  getRowStriping() {\n    return Boolean(this.getLatest().__rowStriping);\n  }\n  setRowStriping(newRowStriping) {\n    const self = this.getWritable();\n    self.__rowStriping = newRowStriping;\n    return self;\n  }\n  setFrozenColumns(columnCount) {\n    const self = this.getWritable();\n    self.__frozenColumnCount = columnCount;\n    return self;\n  }\n  getFrozenColumns() {\n    return this.getLatest().__frozenColumnCount;\n  }\n  setFrozenRows(rowCount) {\n    const self = this.getWritable();\n    self.__frozenRowCount = rowCount;\n    return self;\n  }\n  getFrozenRows() {\n    return this.getLatest().__frozenRowCount;\n  }\n  canSelectBefore() {\n    return true;\n  }\n  canIndent() {\n    return false;\n  }\n  getColumnCount() {\n    const firstRow = this.getFirstChild();\n    if (!firstRow) {\n      return 0;\n    }\n    let columnCount = 0;\n    firstRow.getChildren().forEach(cell => {\n      if ($isTableCellNode(cell)) {\n        columnCount += cell.getColSpan();\n      }\n    });\n    return columnCount;\n  }\n}\nfunction $getElementForTableNode(editor, tableNode) {\n  const tableElement = editor.getElementByKey(tableNode.getKey());\n  if (!(tableElement !== null)) {\n    formatDevErrorMessage(`$getElementForTableNode: Table Element Not Found`);\n  }\n  return getTable(tableNode, tableElement);\n}\nfunction $convertTableElement(domNode) {\n  const tableNode = $createTableNode();\n  if (domNode.hasAttribute('data-lexical-row-striping')) {\n    tableNode.setRowStriping(true);\n  }\n  const colGroup = domNode.querySelector(':scope > colgroup');\n  if (colGroup) {\n    let columns = [];\n    for (const col of colGroup.querySelectorAll(':scope > col')) {\n      let width = col.style.width || '';\n      if (!PIXEL_VALUE_REG_EXP.test(width)) {\n        // Also support deprecated width attribute for google docs\n        width = col.getAttribute('width') || '';\n        if (!/^\\d+$/.test(width)) {\n          columns = undefined;\n          break;\n        }\n      }\n      columns.push(parseFloat(width));\n    }\n    if (columns) {\n      tableNode.setColWidths(columns);\n    }\n  }\n  return {\n    after: children => (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$descendantsMatching)(children, $isTableRowNode),\n    node: tableNode\n  };\n}\nfunction $createTableNode() {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new TableNode());\n}\nfunction $isTableNode(node) {\n  return node instanceof TableNode;\n}\n\nfunction $insertTableCommandListener({\n  rows,\n  columns,\n  includeHeaders\n}) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!selection || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    return false;\n  }\n\n  // Prevent nested tables by checking if we're already inside a table\n  if ($findTableNode(selection.anchor.getNode())) {\n    return false;\n  }\n  const tableNode = $createTableNodeWithDimensions(Number(rows), Number(columns), includeHeaders);\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$insertNodeToNearestRoot)(tableNode);\n  const firstDescendant = tableNode.getFirstDescendant();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(firstDescendant)) {\n    firstDescendant.select();\n  }\n  return true;\n}\nfunction $tableCellTransform(node) {\n  if (!$isTableRowNode(node.getParent())) {\n    // TableCellNode must be a child of TableRowNode.\n    node.remove();\n  } else if (node.isEmpty()) {\n    // TableCellNode should never be empty\n    node.append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n  }\n}\nfunction $tableRowTransform(node) {\n  if (!$isTableNode(node.getParent())) {\n    // TableRowNode must be a child of TableNode.\n    // TODO: Future support of tbody/thead/tfoot may change this\n    node.remove();\n  } else {\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$unwrapAndFilterDescendants)(node, $isTableCellNode);\n  }\n}\nfunction $tableTransform(node) {\n  // TableRowNode is the only valid child for TableNode\n  // TODO: Future support of tbody/thead/tfoot/caption may change this\n  (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$unwrapAndFilterDescendants)(node, $isTableRowNode);\n  const [gridMap] = $computeTableMapSkipCellCheck(node, null, null);\n  const maxRowLength = gridMap.reduce((curLength, row) => {\n    return Math.max(curLength, row.length);\n  }, 0);\n  const rowNodes = node.getChildren();\n  for (let i = 0; i < gridMap.length; ++i) {\n    const rowNode = rowNodes[i];\n    if (!rowNode) {\n      continue;\n    }\n    if (!$isTableRowNode(rowNode)) {\n      formatDevErrorMessage(`TablePlugin: Expecting all children of TableNode to be TableRowNode, found ${rowNode.constructor.name} (type ${rowNode.getType()})`);\n    }\n    const rowLength = gridMap[i].reduce((acc, cell) => cell ? 1 + acc : acc, 0);\n    if (rowLength === maxRowLength) {\n      continue;\n    }\n    for (let j = rowLength; j < maxRowLength; ++j) {\n      // TODO: inherit header state from another header or body\n      const newCell = $createTableCellNode();\n      newCell.append((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n      rowNode.append(newCell);\n    }\n  }\n}\nfunction $tableClickCommand(event) {\n  if (event.detail < 3 || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(event.target)) {\n    return false;\n  }\n  const startNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(event.target);\n  if (startNode === null) {\n    return false;\n  }\n  const blockNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(startNode, node => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && !node.isInline());\n  if (blockNode === null) {\n    return false;\n  }\n  const rootNode = blockNode.getParent();\n  if (!$isTableCellNode(rootNode)) {\n    return false;\n  }\n  blockNode.select(0);\n  return true;\n}\n\n/**\n * Register a transform to ensure that all TableCellNode have a colSpan and rowSpan of 1.\n * This should only be registered when you do not want to support merged cells.\n *\n * @param editor The editor\n * @returns An unregister callback\n */\nfunction registerTableCellUnmergeTransform(editor) {\n  return editor.registerNodeTransform(TableCellNode, node => {\n    if (node.getColSpan() > 1 || node.getRowSpan() > 1) {\n      // When we have rowSpan we have to map the entire Table to understand where the new Cells\n      // fit best; let's analyze all Cells at once to save us from further transform iterations\n      const [,, gridNode] = $getNodeTriplet(node);\n      const [gridMap] = $computeTableMap(gridNode, node, node);\n      // TODO this function expects Tables to be normalized. Look into this once it exists\n      const rowsCount = gridMap.length;\n      const columnsCount = gridMap[0].length;\n      let row = gridNode.getFirstChild();\n      if (!$isTableRowNode(row)) {\n        formatDevErrorMessage(`Expected TableNode first child to be a RowNode`);\n      }\n      const unmerged = [];\n      for (let i = 0; i < rowsCount; i++) {\n        if (i !== 0) {\n          row = row.getNextSibling();\n          if (!$isTableRowNode(row)) {\n            formatDevErrorMessage(`Expected TableNode first child to be a RowNode`);\n          }\n        }\n        let lastRowCell = null;\n        for (let j = 0; j < columnsCount; j++) {\n          const cellMap = gridMap[i][j];\n          const cell = cellMap.cell;\n          if (cellMap.startRow === i && cellMap.startColumn === j) {\n            lastRowCell = cell;\n            unmerged.push(cell);\n          } else if (cell.getColSpan() > 1 || cell.getRowSpan() > 1) {\n            if (!$isTableCellNode(cell)) {\n              formatDevErrorMessage(`Expected TableNode cell to be a TableCellNode`);\n            }\n            const newCell = $createTableCellNode(cell.__headerState);\n            if (lastRowCell !== null) {\n              lastRowCell.insertAfter(newCell);\n            } else {\n              (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$insertFirst)(row, newCell);\n            }\n          }\n        }\n      }\n      for (const cell of unmerged) {\n        cell.setColSpan(1);\n        cell.setRowSpan(1);\n      }\n    }\n  });\n}\nfunction registerTableSelectionObserver(editor, hasTabHandler = true) {\n  const tableSelections = new Map();\n  const initializeTableNode = (tableNode, nodeKey, dom) => {\n    const tableElement = getTableElement(tableNode, dom);\n    const tableSelection = applyTableHandlers(tableNode, tableElement, editor, hasTabHandler);\n    tableSelections.set(nodeKey, [tableSelection, tableElement]);\n  };\n  const unregisterMutationListener = editor.registerMutationListener(TableNode, nodeMutations => {\n    editor.getEditorState().read(() => {\n      for (const [nodeKey, mutation] of nodeMutations) {\n        const tableSelection = tableSelections.get(nodeKey);\n        if (mutation === 'created' || mutation === 'updated') {\n          const {\n            tableNode,\n            tableElement\n          } = $getTableAndElementByKey(nodeKey);\n          if (tableSelection === undefined) {\n            initializeTableNode(tableNode, nodeKey, tableElement);\n          } else if (tableElement !== tableSelection[1]) {\n            // The update created a new DOM node, destroy the existing TableObserver\n            tableSelection[0].removeListeners();\n            tableSelections.delete(nodeKey);\n            initializeTableNode(tableNode, nodeKey, tableElement);\n          }\n        } else if (mutation === 'destroyed') {\n          if (tableSelection !== undefined) {\n            tableSelection[0].removeListeners();\n            tableSelections.delete(nodeKey);\n          }\n        }\n      }\n    }, {\n      editor\n    });\n  }, {\n    skipInitialization: false\n  });\n  return () => {\n    unregisterMutationListener();\n    // Hook might be called multiple times so cleaning up tables listeners as well,\n    // as it'll be reinitialized during recurring call\n    for (const [, [tableSelection]] of tableSelections) {\n      tableSelection.removeListeners();\n    }\n  };\n}\n\n/**\n * Register the INSERT_TABLE_COMMAND listener and the table integrity transforms. The\n * table selection observer should be registered separately after this with\n * {@link registerTableSelectionObserver}.\n *\n * @param editor The editor\n * @returns An unregister callback\n */\nfunction registerTablePlugin(editor) {\n  if (!editor.hasNodes([TableNode])) {\n    {\n      formatDevErrorMessage(`TablePlugin: TableNode is not registered on editor`);\n    }\n  }\n  return (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.mergeRegister)(editor.registerCommand(INSERT_TABLE_COMMAND, $insertTableCommandListener, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECTION_INSERT_CLIPBOARD_NODES_COMMAND, ({\n    nodes,\n    selection\n  }) => {\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const isInsideTableCell = $findTableNode(selection.anchor.getNode()) !== null;\n    return isInsideTableCell && nodes.some($isTableNode);\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CLICK_COMMAND, $tableClickCommand, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerNodeTransform(TableNode, $tableTransform), editor.registerNodeTransform(TableRowNode, $tableRowTransform), editor.registerNodeTransform(TableCellNode, $tableCellTransform));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+table@0.28.0/node_modules/@lexical/table/LexicalTable.dev.mjs\n");

/***/ })

};
;