"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+utils@0.28.0";
exports.ids = ["vendor-chunks/@lexical+utils@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs":
/*!***************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $descendantsMatching: () => (/* binding */ $descendantsMatching),\n/* harmony export */   $dfs: () => (/* binding */ $dfs),\n/* harmony export */   $dfsIterator: () => (/* binding */ $dfsIterator),\n/* harmony export */   $filter: () => (/* binding */ $filter),\n/* harmony export */   $findMatchingParent: () => (/* binding */ $findMatchingParent),\n/* harmony export */   $firstToLastIterator: () => (/* binding */ $firstToLastIterator),\n/* harmony export */   $getAdjacentCaret: () => (/* binding */ $getAdjacentCaret),\n/* harmony export */   $getAdjacentSiblingOrParentSiblingCaret: () => (/* binding */ $getAdjacentSiblingOrParentSiblingCaret),\n/* harmony export */   $getDepth: () => (/* binding */ $getDepth),\n/* harmony export */   $getNearestBlockElementAncestorOrThrow: () => (/* binding */ $getNearestBlockElementAncestorOrThrow),\n/* harmony export */   $getNearestNodeOfType: () => (/* binding */ $getNearestNodeOfType),\n/* harmony export */   $getNextRightPreorderNode: () => (/* binding */ $getNextRightPreorderNode),\n/* harmony export */   $getNextSiblingOrParentSibling: () => (/* binding */ $getNextSiblingOrParentSibling),\n/* harmony export */   $insertFirst: () => (/* binding */ $insertFirst),\n/* harmony export */   $insertNodeToNearestRoot: () => (/* binding */ $insertNodeToNearestRoot),\n/* harmony export */   $insertNodeToNearestRootAtCaret: () => (/* binding */ $insertNodeToNearestRootAtCaret),\n/* harmony export */   $isEditorIsNestedEditor: () => (/* binding */ $isEditorIsNestedEditor),\n/* harmony export */   $lastToFirstIterator: () => (/* binding */ $lastToFirstIterator),\n/* harmony export */   $restoreEditorState: () => (/* binding */ $restoreEditorState),\n/* harmony export */   $reverseDfs: () => (/* binding */ $reverseDfs),\n/* harmony export */   $reverseDfsIterator: () => (/* binding */ $reverseDfsIterator),\n/* harmony export */   $splitNode: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.$splitNode),\n/* harmony export */   $unwrapAndFilterDescendants: () => (/* binding */ $unwrapAndFilterDescendants),\n/* harmony export */   $unwrapNode: () => (/* binding */ $unwrapNode),\n/* harmony export */   $wrapNodeInElement: () => (/* binding */ $wrapNodeInElement),\n/* harmony export */   CAN_USE_BEFORE_INPUT: () => (/* binding */ CAN_USE_BEFORE_INPUT),\n/* harmony export */   CAN_USE_DOM: () => (/* binding */ CAN_USE_DOM),\n/* harmony export */   IS_ANDROID: () => (/* binding */ IS_ANDROID),\n/* harmony export */   IS_ANDROID_CHROME: () => (/* binding */ IS_ANDROID_CHROME),\n/* harmony export */   IS_APPLE: () => (/* binding */ IS_APPLE),\n/* harmony export */   IS_APPLE_WEBKIT: () => (/* binding */ IS_APPLE_WEBKIT),\n/* harmony export */   IS_CHROME: () => (/* binding */ IS_CHROME),\n/* harmony export */   IS_FIREFOX: () => (/* binding */ IS_FIREFOX),\n/* harmony export */   IS_IOS: () => (/* binding */ IS_IOS),\n/* harmony export */   IS_SAFARI: () => (/* binding */ IS_SAFARI),\n/* harmony export */   addClassNamesToElement: () => (/* binding */ addClassNamesToElement),\n/* harmony export */   calculateZoomLevel: () => (/* binding */ calculateZoomLevel),\n/* harmony export */   isBlockDomNode: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isBlockDomNode),\n/* harmony export */   isHTMLAnchorElement: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLAnchorElement),\n/* harmony export */   isHTMLElement: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement),\n/* harmony export */   isInlineDomNode: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isInlineDomNode),\n/* harmony export */   isMimeType: () => (/* binding */ isMimeType),\n/* harmony export */   makeStateWrapper: () => (/* binding */ makeStateWrapper),\n/* harmony export */   markSelection: () => (/* binding */ markSelection),\n/* harmony export */   mediaFileReader: () => (/* binding */ mediaFileReader),\n/* harmony export */   mergeRegister: () => (/* binding */ mergeRegister),\n/* harmony export */   objectKlassEquals: () => (/* binding */ objectKlassEquals),\n/* harmony export */   positionNodeOnRange: () => (/* binding */ mlcPositionNodeOnRange),\n/* harmony export */   registerNestedElementResolver: () => (/* binding */ registerNestedElementResolver),\n/* harmony export */   removeClassNamesFromElement: () => (/* binding */ removeClassNamesFromElement),\n/* harmony export */   selectionAlwaysOnDisplay: () => (/* binding */ selectionAlwaysOnDisplay)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/selection */ \"(rsc)/./node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM$1 = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst documentMode = CAN_USE_DOM$1 && 'documentMode' in document ? document.documentMode : null;\nconst IS_APPLE$1 = CAN_USE_DOM$1 && /Mac|iPod|iPhone|iPad/.test(navigator.platform);\nconst IS_FIREFOX$1 = CAN_USE_DOM$1 && /^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent);\nconst CAN_USE_BEFORE_INPUT$1 = CAN_USE_DOM$1 && 'InputEvent' in window && !documentMode ? 'getTargetRanges' in new window.InputEvent('input') : false;\nconst IS_SAFARI$1 = CAN_USE_DOM$1 && /Version\\/[\\d.]+.*Safari/.test(navigator.userAgent);\nconst IS_IOS$1 = CAN_USE_DOM$1 && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;\nconst IS_ANDROID$1 = CAN_USE_DOM$1 && /Android/.test(navigator.userAgent);\n\n// Keep these in case we need to use them in the future.\n// export const IS_WINDOWS: boolean = CAN_USE_DOM && /Win/.test(navigator.platform);\nconst IS_CHROME$1 = CAN_USE_DOM$1 && /^(?=.*Chrome).*/i.test(navigator.userAgent);\n// export const canUseTextInputEvent: boolean = CAN_USE_DOM && 'TextEvent' in window && !documentMode;\n\nconst IS_ANDROID_CHROME$1 = CAN_USE_DOM$1 && IS_ANDROID$1 && IS_CHROME$1;\nconst IS_APPLE_WEBKIT$1 = CAN_USE_DOM$1 && /AppleWebKit\\/[\\d.]+/.test(navigator.userAgent) && !IS_CHROME$1;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction normalizeClassNames(...classNames) {\n  const rval = [];\n  for (const className of classNames) {\n    if (className && typeof className === 'string') {\n      for (const [s] of className.matchAll(/\\S+/g)) {\n        rval.push(s);\n      }\n    }\n  }\n  return rval;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Returns a function that will execute all functions passed when called. It is generally used\n * to register multiple lexical listeners and then tear them down with a single function call, such\n * as React's useEffect hook.\n * @example\n * ```ts\n * useEffect(() => {\n *   return mergeRegister(\n *     editor.registerCommand(...registerCommand1 logic),\n *     editor.registerCommand(...registerCommand2 logic),\n *     editor.registerCommand(...registerCommand3 logic)\n *   )\n * }, [editor])\n * ```\n * In this case, useEffect is returning the function returned by mergeRegister as a cleanup\n * function to be executed after either the useEffect runs again (due to one of its dependencies\n * updating) or the component it resides in unmounts.\n * Note the functions don't neccesarily need to be in an array as all arguments\n * are considered to be the func argument and spread from there.\n * The order of cleanup is the reverse of the argument order. Generally it is\n * expected that the first \"acquire\" will be \"released\" last (LIFO order),\n * because a later step may have some dependency on an earlier one.\n * @param func - An array of cleanup functions meant to be executed by the returned function.\n * @returns the function which executes all the passed cleanup functions.\n */\nfunction mergeRegister(...func) {\n  return () => {\n    for (let i = func.length - 1; i >= 0; i--) {\n      func[i]();\n    }\n    // Clean up the references and make future calls a no-op\n    func.length = 0;\n  };\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction px(value) {\n  return `${value}px`;\n}\n\nconst mutationObserverConfig = {\n  attributes: true,\n  characterData: true,\n  childList: true,\n  subtree: true\n};\nfunction prependDOMNode(parent, node) {\n  parent.insertBefore(node, parent.firstChild);\n}\n\n/**\n * Place one or multiple newly created Nodes at the passed Range's position.\n * Multiple nodes will only be created when the Range spans multiple lines (aka\n * client rects).\n *\n * This function can come particularly useful to highlight particular parts of\n * the text without interfering with the EditorState, that will often replicate\n * the state across collab and clipboard.\n *\n * This function accounts for DOM updates which can modify the passed Range.\n * Hence, the function return to remove the listener.\n */\nfunction mlcPositionNodeOnRange(editor, range, onReposition) {\n  let rootDOMNode = null;\n  let parentDOMNode = null;\n  let observer = null;\n  let lastNodes = [];\n  const wrapperNode = document.createElement('div');\n  wrapperNode.style.position = 'relative';\n  function position() {\n    if (!(rootDOMNode !== null)) {\n      formatDevErrorMessage(`Unexpected null rootDOMNode`);\n    }\n    if (!(parentDOMNode !== null)) {\n      formatDevErrorMessage(`Unexpected null parentDOMNode`);\n    }\n    const {\n      left: parentLeft,\n      top: parentTop\n    } = parentDOMNode.getBoundingClientRect();\n    const rects = (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_1__.createRectsFromDOMRange)(editor, range);\n    if (!wrapperNode.isConnected) {\n      prependDOMNode(parentDOMNode, wrapperNode);\n    }\n    let hasRepositioned = false;\n    for (let i = 0; i < rects.length; i++) {\n      const rect = rects[i];\n      // Try to reuse the previously created Node when possible, no need to\n      // remove/create on the most common case reposition case\n      const rectNode = lastNodes[i] || document.createElement('div');\n      const rectNodeStyle = rectNode.style;\n      if (rectNodeStyle.position !== 'absolute') {\n        rectNodeStyle.position = 'absolute';\n        hasRepositioned = true;\n      }\n      const left = px(rect.left - parentLeft);\n      if (rectNodeStyle.left !== left) {\n        rectNodeStyle.left = left;\n        hasRepositioned = true;\n      }\n      const top = px(rect.top - parentTop);\n      if (rectNodeStyle.top !== top) {\n        rectNode.style.top = top;\n        hasRepositioned = true;\n      }\n      const width = px(rect.width);\n      if (rectNodeStyle.width !== width) {\n        rectNode.style.width = width;\n        hasRepositioned = true;\n      }\n      const height = px(rect.height);\n      if (rectNodeStyle.height !== height) {\n        rectNode.style.height = height;\n        hasRepositioned = true;\n      }\n      if (rectNode.parentNode !== wrapperNode) {\n        wrapperNode.append(rectNode);\n        hasRepositioned = true;\n      }\n      lastNodes[i] = rectNode;\n    }\n    while (lastNodes.length > rects.length) {\n      lastNodes.pop();\n    }\n    if (hasRepositioned) {\n      onReposition(lastNodes);\n    }\n  }\n  function stop() {\n    parentDOMNode = null;\n    rootDOMNode = null;\n    if (observer !== null) {\n      observer.disconnect();\n    }\n    observer = null;\n    wrapperNode.remove();\n    for (const node of lastNodes) {\n      node.remove();\n    }\n    lastNodes = [];\n  }\n  function restart() {\n    const currentRootDOMNode = editor.getRootElement();\n    if (currentRootDOMNode === null) {\n      return stop();\n    }\n    const currentParentDOMNode = currentRootDOMNode.parentElement;\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(currentParentDOMNode)) {\n      return stop();\n    }\n    stop();\n    rootDOMNode = currentRootDOMNode;\n    parentDOMNode = currentParentDOMNode;\n    observer = new MutationObserver(mutations => {\n      const nextRootDOMNode = editor.getRootElement();\n      const nextParentDOMNode = nextRootDOMNode && nextRootDOMNode.parentElement;\n      if (nextRootDOMNode !== rootDOMNode || nextParentDOMNode !== parentDOMNode) {\n        return restart();\n      }\n      for (const mutation of mutations) {\n        if (!wrapperNode.contains(mutation.target)) {\n          // TODO throttle\n          return position();\n        }\n      }\n    });\n    observer.observe(currentParentDOMNode, mutationObserverConfig);\n    position();\n  }\n  const removeRootListener = editor.registerRootListener(restart);\n  return () => {\n    removeRootListener();\n    stop();\n  };\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction rangeTargetFromPoint(point, node, dom) {\n  if (point.type === 'text' || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n    const textDOM = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMTextNode)(dom) || dom;\n    return [textDOM, point.offset];\n  } else {\n    const slot = node.getDOMSlot(dom);\n    return [slot.element, slot.getFirstChildOffset() + point.offset];\n  }\n}\nfunction rangeFromPoints(editor, anchor, anchorNode, anchorDOM, focus, focusNode, focusDOM) {\n  const editorDocument = editor._window ? editor._window.document : document;\n  const range = editorDocument.createRange();\n  if (focusNode.isBefore(anchorNode)) {\n    range.setStart(...rangeTargetFromPoint(focus, focusNode, focusDOM));\n    range.setEnd(...rangeTargetFromPoint(anchor, anchorNode, anchorDOM));\n  } else {\n    range.setStart(...rangeTargetFromPoint(anchor, anchorNode, anchorDOM));\n    range.setEnd(...rangeTargetFromPoint(focus, focusNode, focusDOM));\n  }\n  return range;\n}\n/**\n * Place one or multiple newly created Nodes at the current selection. Multiple\n * nodes will only be created when the selection spans multiple lines (aka\n * client rects).\n *\n * This function can come useful when you want to show the selection but the\n * editor has been focused away.\n */\nfunction markSelection(editor, onReposition) {\n  let previousAnchorNode = null;\n  let previousAnchorNodeDOM = null;\n  let previousAnchorOffset = null;\n  let previousFocusNode = null;\n  let previousFocusNodeDOM = null;\n  let previousFocusOffset = null;\n  let removeRangeListener = () => {};\n  function compute(editorState) {\n    editorState.read(() => {\n      const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n        // TODO\n        previousAnchorNode = null;\n        previousAnchorOffset = null;\n        previousFocusNode = null;\n        previousFocusOffset = null;\n        removeRangeListener();\n        removeRangeListener = () => {};\n        return;\n      }\n      const {\n        anchor,\n        focus\n      } = selection;\n      const currentAnchorNode = anchor.getNode();\n      const currentAnchorNodeKey = currentAnchorNode.getKey();\n      const currentAnchorOffset = anchor.offset;\n      const currentFocusNode = focus.getNode();\n      const currentFocusNodeKey = currentFocusNode.getKey();\n      const currentFocusOffset = focus.offset;\n      const currentAnchorNodeDOM = editor.getElementByKey(currentAnchorNodeKey);\n      const currentFocusNodeDOM = editor.getElementByKey(currentFocusNodeKey);\n      const differentAnchorDOM = previousAnchorNode === null || currentAnchorNodeDOM !== previousAnchorNodeDOM || currentAnchorOffset !== previousAnchorOffset || currentAnchorNodeKey !== previousAnchorNode.getKey();\n      const differentFocusDOM = previousFocusNode === null || currentFocusNodeDOM !== previousFocusNodeDOM || currentFocusOffset !== previousFocusOffset || currentFocusNodeKey !== previousFocusNode.getKey();\n      if ((differentAnchorDOM || differentFocusDOM) && currentAnchorNodeDOM !== null && currentFocusNodeDOM !== null) {\n        const range = rangeFromPoints(editor, anchor, currentAnchorNode, currentAnchorNodeDOM, focus, currentFocusNode, currentFocusNodeDOM);\n        removeRangeListener();\n        removeRangeListener = mlcPositionNodeOnRange(editor, range, domNodes => {\n          if (onReposition === undefined) {\n            for (const domNode of domNodes) {\n              const domNodeStyle = domNode.style;\n              if (domNodeStyle.background !== 'Highlight') {\n                domNodeStyle.background = 'Highlight';\n              }\n              if (domNodeStyle.color !== 'HighlightText') {\n                domNodeStyle.color = 'HighlightText';\n              }\n              if (domNodeStyle.marginTop !== px(-1.5)) {\n                domNodeStyle.marginTop = px(-1.5);\n              }\n              if (domNodeStyle.paddingTop !== px(4)) {\n                domNodeStyle.paddingTop = px(4);\n              }\n              if (domNodeStyle.paddingBottom !== px(0)) {\n                domNodeStyle.paddingBottom = px(0);\n              }\n            }\n          } else {\n            onReposition(domNodes);\n          }\n        });\n      }\n      previousAnchorNode = currentAnchorNode;\n      previousAnchorNodeDOM = currentAnchorNodeDOM;\n      previousAnchorOffset = currentAnchorOffset;\n      previousFocusNode = currentFocusNode;\n      previousFocusNodeDOM = currentFocusNodeDOM;\n      previousFocusOffset = currentFocusOffset;\n    });\n  }\n  compute(editor.getEditorState());\n  return mergeRegister(editor.registerUpdateListener(({\n    editorState\n  }) => compute(editorState)), () => {\n    removeRangeListener();\n  });\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction selectionAlwaysOnDisplay(editor) {\n  let removeSelectionMark = null;\n  const onSelectionChange = () => {\n    const domSelection = getSelection();\n    const domAnchorNode = domSelection && domSelection.anchorNode;\n    const editorRootElement = editor.getRootElement();\n    const isSelectionInsideEditor = domAnchorNode !== null && editorRootElement !== null && editorRootElement.contains(domAnchorNode);\n    if (isSelectionInsideEditor) {\n      if (removeSelectionMark !== null) {\n        removeSelectionMark();\n        removeSelectionMark = null;\n      }\n    } else {\n      if (removeSelectionMark === null) {\n        removeSelectionMark = markSelection(editor);\n      }\n    }\n  };\n  document.addEventListener('selectionchange', onSelectionChange);\n  return () => {\n    if (removeSelectionMark !== null) {\n      removeSelectionMark();\n    }\n    document.removeEventListener('selectionchange', onSelectionChange);\n  };\n}\n\n// Hotfix to export these with inlined types #5918\nconst CAN_USE_BEFORE_INPUT = CAN_USE_BEFORE_INPUT$1;\nconst CAN_USE_DOM = CAN_USE_DOM$1;\nconst IS_ANDROID = IS_ANDROID$1;\nconst IS_ANDROID_CHROME = IS_ANDROID_CHROME$1;\nconst IS_APPLE = IS_APPLE$1;\nconst IS_APPLE_WEBKIT = IS_APPLE_WEBKIT$1;\nconst IS_CHROME = IS_CHROME$1;\nconst IS_FIREFOX = IS_FIREFOX$1;\nconst IS_IOS = IS_IOS$1;\nconst IS_SAFARI = IS_SAFARI$1;\n\n/**\n * Takes an HTML element and adds the classNames passed within an array,\n * ignoring any non-string types. A space can be used to add multiple classes\n * eg. addClassNamesToElement(element, ['element-inner active', true, null])\n * will add both 'element-inner' and 'active' as classes to that element.\n * @param element - The element in which the classes are added\n * @param classNames - An array defining the class names to add to the element\n */\nfunction addClassNamesToElement(element, ...classNames) {\n  const classesToAdd = normalizeClassNames(...classNames);\n  if (classesToAdd.length > 0) {\n    element.classList.add(...classesToAdd);\n  }\n}\n\n/**\n * Takes an HTML element and removes the classNames passed within an array,\n * ignoring any non-string types. A space can be used to remove multiple classes\n * eg. removeClassNamesFromElement(element, ['active small', true, null])\n * will remove both the 'active' and 'small' classes from that element.\n * @param element - The element in which the classes are removed\n * @param classNames - An array defining the class names to remove from the element\n */\nfunction removeClassNamesFromElement(element, ...classNames) {\n  const classesToRemove = normalizeClassNames(...classNames);\n  if (classesToRemove.length > 0) {\n    element.classList.remove(...classesToRemove);\n  }\n}\n\n/**\n * Returns true if the file type matches the types passed within the acceptableMimeTypes array, false otherwise.\n * The types passed must be strings and are CASE-SENSITIVE.\n * eg. if file is of type 'text' and acceptableMimeTypes = ['TEXT', 'IMAGE'] the function will return false.\n * @param file - The file you want to type check.\n * @param acceptableMimeTypes - An array of strings of types which the file is checked against.\n * @returns true if the file is an acceptable mime type, false otherwise.\n */\nfunction isMimeType(file, acceptableMimeTypes) {\n  for (const acceptableType of acceptableMimeTypes) {\n    if (file.type.startsWith(acceptableType)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Lexical File Reader with:\n *  1. MIME type support\n *  2. batched results (HistoryPlugin compatibility)\n *  3. Order aware (respects the order when multiple Files are passed)\n *\n * const filesResult = await mediaFileReader(files, ['image/']);\n * filesResult.forEach(file => editor.dispatchCommand('INSERT_IMAGE', \\\\{\n *   src: file.result,\n * \\\\}));\n */\nfunction mediaFileReader(files, acceptableMimeTypes) {\n  const filesIterator = files[Symbol.iterator]();\n  return new Promise((resolve, reject) => {\n    const processed = [];\n    const handleNextFile = () => {\n      const {\n        done,\n        value: file\n      } = filesIterator.next();\n      if (done) {\n        return resolve(processed);\n      }\n      const fileReader = new FileReader();\n      fileReader.addEventListener('error', reject);\n      fileReader.addEventListener('load', () => {\n        const result = fileReader.result;\n        if (typeof result === 'string') {\n          processed.push({\n            file,\n            result\n          });\n        }\n        handleNextFile();\n      });\n      if (isMimeType(file, acceptableMimeTypes)) {\n        fileReader.readAsDataURL(file);\n      } else {\n        handleNextFile();\n      }\n    };\n    handleNextFile();\n  });\n}\n/**\n * \"Depth-First Search\" starts at the root/top node of a tree and goes as far as it can down a branch end\n * before backtracking and finding a new path. Consider solving a maze by hugging either wall, moving down a\n * branch until you hit a dead-end (leaf) and backtracking to find the nearest branching path and repeat.\n * It will then return all the nodes found in the search in an array of objects.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An array of objects of all the nodes found by the search, including their depth into the tree.\n * \\\\{depth: number, node: LexicalNode\\\\} It will always return at least 1 node (the start node).\n */\nfunction $dfs(startNode, endNode) {\n  return Array.from($dfsIterator(startNode, endNode));\n}\n\n/**\n * Get the adjacent caret in the same direction\n *\n * @param caret A caret or null\n * @returns `caret.getAdjacentCaret()` or `null`\n */\nfunction $getAdjacentCaret(caret) {\n  return caret ? caret.getAdjacentCaret() : null;\n}\n\n/**\n * $dfs iterator (right to left). Tree traversal is done on the fly as new values are requested with O(1) memory.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).\n */\nfunction $reverseDfs(startNode, endNode) {\n  return Array.from($reverseDfsIterator(startNode, endNode));\n}\n\n/**\n * $dfs iterator (left to right). Tree traversal is done on the fly as new values are requested with O(1) memory.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).\n */\nfunction $dfsIterator(startNode, endNode) {\n  return $dfsCaretIterator('next', startNode, endNode);\n}\nfunction $getEndCaret(startNode, direction) {\n  const rval = $getAdjacentSiblingOrParentSiblingCaret((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(startNode, direction));\n  return rval && rval[0];\n}\nfunction $dfsCaretIterator(direction, startNode, endNode) {\n  const root = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)();\n  const start = startNode || root;\n  const startCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(start) ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(start, direction) : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(start, direction);\n  const startDepth = $getDepth(start);\n  const endCaret = endNode ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaretOrSelf)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(endNode, direction))) : $getEndCaret(start, direction);\n  let depth = startDepth;\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.makeStepwiseIterator)({\n    hasNext: state => state !== null,\n    initial: startCaret,\n    map: state => ({\n      depth,\n      node: state.origin\n    }),\n    step: state => {\n      if (state.isSameNodeCaret(endCaret)) {\n        return null;\n      }\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(state)) {\n        depth++;\n      }\n      const rval = $getAdjacentSiblingOrParentSiblingCaret(state);\n      if (!rval || rval[0].isSameNodeCaret(endCaret)) {\n        return null;\n      }\n      depth += rval[1];\n      return rval[0];\n    }\n  });\n}\n\n/**\n * Returns the Node sibling when this exists, otherwise the closest parent sibling. For example\n * R -> P -> T1, T2\n *   -> P2\n * returns T2 for node T1, P2 for node T2, and null for node P2.\n * @param node LexicalNode.\n * @returns An array (tuple) containing the found Lexical node and the depth difference, or null, if this node doesn't exist.\n */\nfunction $getNextSiblingOrParentSibling(node) {\n  const rval = $getAdjacentSiblingOrParentSiblingCaret((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(node, 'next'));\n  return rval && [rval[0].origin, rval[1]];\n}\nfunction $getDepth(node) {\n  let depth = -1;\n  for (let innerNode = node; innerNode !== null; innerNode = innerNode.getParent()) {\n    depth++;\n  }\n  return depth;\n}\n\n/**\n * Performs a right-to-left preorder tree traversal.\n * From the starting node it goes to the rightmost child, than backtracks to parent and finds new rightmost path.\n * It will return the next node in traversal sequence after the startingNode.\n * The traversal is similar to $dfs functions above, but the nodes are visited right-to-left, not left-to-right.\n * @param startingNode - The node to start the search.\n * @returns The next node in pre-order right to left traversal sequence or `null`, if the node does not exist\n */\nfunction $getNextRightPreorderNode(startingNode) {\n  const startCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaretOrSelf)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(startingNode, 'previous'));\n  const next = $getAdjacentSiblingOrParentSiblingCaret(startCaret, 'root');\n  return next && next[0].origin;\n}\n\n/**\n * $dfs iterator (right to left). Tree traversal is done on the fly as new values are requested with O(1) memory.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).\n */\nfunction $reverseDfsIterator(startNode, endNode) {\n  return $dfsCaretIterator('previous', startNode, endNode);\n}\n\n/**\n * Takes a node and traverses up its ancestors (toward the root node)\n * in order to find a specific type of node.\n * @param node - the node to begin searching.\n * @param klass - an instance of the type of node to look for.\n * @returns the node of type klass that was passed, or null if none exist.\n */\nfunction $getNearestNodeOfType(node, klass) {\n  let parent = node;\n  while (parent != null) {\n    if (parent instanceof klass) {\n      return parent;\n    }\n    parent = parent.getParent();\n  }\n  return null;\n}\n\n/**\n * Returns the element node of the nearest ancestor, otherwise throws an error.\n * @param startNode - The starting node of the search\n * @returns The ancestor node found\n */\nfunction $getNearestBlockElementAncestorOrThrow(startNode) {\n  const blockNode = $findMatchingParent(startNode, node => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && !node.isInline());\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(blockNode)) {\n    {\n      formatDevErrorMessage(`Expected node ${startNode.__key} to have closest block element node.`);\n    }\n  }\n  return blockNode;\n}\n/**\n * Starts with a node and moves up the tree (toward the root node) to find a matching node based on\n * the search parameters of the findFn. (Consider JavaScripts' .find() function where a testing function must be\n * passed as an argument. eg. if( (node) => node.__type === 'div') ) return true; otherwise return false\n * @param startingNode - The node where the search starts.\n * @param findFn - A testing function that returns true if the current node satisfies the testing parameters.\n * @returns A parent node that matches the findFn parameters, or null if one wasn't found.\n */\nconst $findMatchingParent = (startingNode, findFn) => {\n  let curr = startingNode;\n  while (curr !== (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)() && curr != null) {\n    if (findFn(curr)) {\n      return curr;\n    }\n    curr = curr.getParent();\n  }\n  return null;\n};\n\n/**\n * Attempts to resolve nested element nodes of the same type into a single node of that type.\n * It is generally used for marks/commenting\n * @param editor - The lexical editor\n * @param targetNode - The target for the nested element to be extracted from.\n * @param cloneNode - See {@link $createMarkNode}\n * @param handleOverlap - Handles any overlap between the node to extract and the targetNode\n * @returns The lexical editor\n */\nfunction registerNestedElementResolver(editor, targetNode, cloneNode, handleOverlap) {\n  const $isTargetNode = node => {\n    return node instanceof targetNode;\n  };\n  const $findMatch = node => {\n    // First validate we don't have any children that are of the target,\n    // as we need to handle them first.\n    const children = node.getChildren();\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if ($isTargetNode(child)) {\n        return null;\n      }\n    }\n    let parentNode = node;\n    let childNode = node;\n    while (parentNode !== null) {\n      childNode = parentNode;\n      parentNode = parentNode.getParent();\n      if ($isTargetNode(parentNode)) {\n        return {\n          child: childNode,\n          parent: parentNode\n        };\n      }\n    }\n    return null;\n  };\n  const $elementNodeTransform = node => {\n    const match = $findMatch(node);\n    if (match !== null) {\n      const {\n        child,\n        parent\n      } = match;\n\n      // Simple path, we can move child out and siblings into a new parent.\n\n      if (child.is(node)) {\n        handleOverlap(parent, node);\n        const nextSiblings = child.getNextSiblings();\n        const nextSiblingsLength = nextSiblings.length;\n        parent.insertAfter(child);\n        if (nextSiblingsLength !== 0) {\n          const newParent = cloneNode(parent);\n          child.insertAfter(newParent);\n          for (let i = 0; i < nextSiblingsLength; i++) {\n            newParent.append(nextSiblings[i]);\n          }\n        }\n        if (!parent.canBeEmpty() && parent.getChildrenSize() === 0) {\n          parent.remove();\n        }\n      }\n    }\n  };\n  return editor.registerNodeTransform(targetNode, $elementNodeTransform);\n}\n\n/**\n * Clones the editor and marks it as dirty to be reconciled. If there was a selection,\n * it would be set back to its previous state, or null otherwise.\n * @param editor - The lexical editor\n * @param editorState - The editor's state\n */\nfunction $restoreEditorState(editor, editorState) {\n  const FULL_RECONCILE = 2;\n  const nodeMap = new Map();\n  const activeEditorState = editor._pendingEditorState;\n  for (const [key, node] of editorState._nodeMap) {\n    nodeMap.set(key, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$cloneWithProperties)(node));\n  }\n  if (activeEditorState) {\n    activeEditorState._nodeMap = nodeMap;\n  }\n  editor._dirtyType = FULL_RECONCILE;\n  const selection = editorState._selection;\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(selection === null ? null : selection.clone());\n}\n\n/**\n * If the selected insertion area is the root/shadow root node (see {@link lexical!$isRootOrShadowRoot}),\n * the node will be appended there, otherwise, it will be inserted before the insertion area.\n * If there is no selection where the node is to be inserted, it will be appended after any current nodes\n * within the tree, as a child of the root node. A paragraph will then be added after the inserted node and selected.\n * @param node - The node to be inserted\n * @returns The node after its insertion\n */\nfunction $insertNodeToNearestRoot(node) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)() || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n  let initialCaret;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    initialCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$caretFromPoint)(selection.focus, 'next');\n  } else {\n    if (selection != null) {\n      const nodes = selection.getNodes();\n      const lastNode = nodes[nodes.length - 1];\n      if (lastNode) {\n        initialCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(lastNode, 'next');\n      }\n    }\n    initialCaret = initialCaret || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)(), 'previous').getFlipped().insert((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n  }\n  const insertCaret = $insertNodeToNearestRootAtCaret(node, initialCaret);\n  const adjacent = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)(insertCaret);\n  const selectionCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(adjacent) ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeCaret)(adjacent) : insertCaret;\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelectionFromCaretRange)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCollapsedCaretRange)(selectionCaret));\n  return node.getLatest();\n}\n\n/**\n * If the insertion caret is the root/shadow root node (see {@link lexical!$isRootOrShadowRoot}),\n * the node will be inserted there, otherwise the parent nodes will be split according to the\n * given options.\n * @param node - The node to be inserted\n * @param caret - The location to insert or split from\n * @returns The node after its insertion\n */\nfunction $insertNodeToNearestRootAtCaret(node, caret, options) {\n  let insertCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCaretInDirection)(caret, 'next');\n  for (let nextCaret = insertCaret; nextCaret; nextCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$splitAtPointCaretNext)(nextCaret, options)) {\n    insertCaret = nextCaret;\n  }\n  if (!!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextPointCaret)(insertCaret)) {\n    formatDevErrorMessage(`$insertNodeToNearestRootAtCaret: An unattached TextNode can not be split`);\n  }\n  insertCaret.insert(node.isInline() ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)().append(node) : node);\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCaretInDirection)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(node.getLatest(), 'next'), caret.direction);\n}\n\n/**\n * Wraps the node into another node created from a createElementNode function, eg. $createParagraphNode\n * @param node - Node to be wrapped.\n * @param createElementNode - Creates a new lexical element to wrap the to-be-wrapped node and returns it.\n * @returns A new lexical element with the previous node appended within (as a child, including its children).\n */\nfunction $wrapNodeInElement(node, createElementNode) {\n  const elementNode = createElementNode();\n  node.replace(elementNode);\n  elementNode.append(node);\n  return elementNode;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n/**\n * @param object = The instance of the type\n * @param objectClass = The class of the type\n * @returns Whether the object is has the same Klass of the objectClass, ignoring the difference across window (e.g. different iframs)\n */\nfunction objectKlassEquals(object, objectClass) {\n  return object !== null ? Object.getPrototypeOf(object).constructor.name === objectClass.name : false;\n}\n\n/**\n * Filter the nodes\n * @param nodes Array of nodes that needs to be filtered\n * @param filterFn A filter function that returns node if the current node satisfies the condition otherwise null\n * @returns Array of filtered nodes\n */\n\nfunction $filter(nodes, filterFn) {\n  const result = [];\n  for (let i = 0; i < nodes.length; i++) {\n    const node = filterFn(nodes[i]);\n    if (node !== null) {\n      result.push(node);\n    }\n  }\n  return result;\n}\n/**\n * Appends the node before the first child of the parent node\n * @param parent A parent node\n * @param node Node that needs to be appended\n */\nfunction $insertFirst(parent, node) {\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(parent, 'next').insert(node);\n}\nlet NEEDS_MANUAL_ZOOM = IS_FIREFOX || !CAN_USE_DOM ? false : undefined;\nfunction needsManualZoom() {\n  if (NEEDS_MANUAL_ZOOM === undefined) {\n    // If the browser implements standardized CSS zoom, then the client rect\n    // will be wider after zoom is applied\n    // https://chromestatus.com/feature/5198254868529152\n    // https://github.com/facebook/lexical/issues/6863\n    const div = document.createElement('div');\n    div.style.cssText = 'position: absolute; opacity: 0; width: 100px; left: -1000px;';\n    document.body.appendChild(div);\n    const noZoom = div.getBoundingClientRect();\n    div.style.setProperty('zoom', '2');\n    NEEDS_MANUAL_ZOOM = div.getBoundingClientRect().width === noZoom.width;\n    document.body.removeChild(div);\n  }\n  return NEEDS_MANUAL_ZOOM;\n}\n\n/**\n * Calculates the zoom level of an element as a result of using\n * css zoom property. For browsers that implement standardized CSS\n * zoom (Firefox, Chrome >= 128), this will always return 1.\n * @param element\n */\nfunction calculateZoomLevel(element) {\n  let zoom = 1;\n  if (needsManualZoom()) {\n    while (element) {\n      zoom *= Number(window.getComputedStyle(element).getPropertyValue('zoom'));\n      element = element.parentElement;\n    }\n  }\n  return zoom;\n}\n\n/**\n * Checks if the editor is a nested editor created by LexicalNestedComposer\n */\nfunction $isEditorIsNestedEditor(editor) {\n  return editor._parentEditor !== null;\n}\n\n/**\n * A depth first last-to-first traversal of root that stops at each node that matches\n * $predicate and ensures that its parent is root. This is typically used to discard\n * invalid or unsupported wrapping nodes. For example, a TableNode must only have\n * TableRowNode as children, but an importer might add invalid nodes based on\n * caption, tbody, thead, etc. and this will unwrap and discard those.\n *\n * @param root The root to start the traversal\n * @param $predicate Should return true for nodes that are permitted to be children of root\n * @returns true if this unwrapped or removed any nodes\n */\nfunction $unwrapAndFilterDescendants(root, $predicate) {\n  return $unwrapAndFilterDescendantsImpl(root, $predicate, null);\n}\nfunction $unwrapAndFilterDescendantsImpl(root, $predicate, $onSuccess) {\n  let didMutate = false;\n  for (const node of $lastToFirstIterator(root)) {\n    if ($predicate(node)) {\n      if ($onSuccess !== null) {\n        $onSuccess(node);\n      }\n      continue;\n    }\n    didMutate = true;\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n      $unwrapAndFilterDescendantsImpl(node, $predicate, $onSuccess || (child => node.insertAfter(child)));\n    }\n    node.remove();\n  }\n  return didMutate;\n}\n\n/**\n * A depth first traversal of the children array that stops at and collects\n * each node that `$predicate` matches. This is typically used to discard\n * invalid or unsupported wrapping nodes on a children array in the `after`\n * of an {@link lexical!DOMConversionOutput}. For example, a TableNode must only have\n * TableRowNode as children, but an importer might add invalid nodes based on\n * caption, tbody, thead, etc. and this will unwrap and discard those.\n *\n * This function is read-only and performs no mutation operations, which makes\n * it suitable for import and export purposes but likely not for any in-place\n * mutation. You should use {@link $unwrapAndFilterDescendants} for in-place\n * mutations such as node transforms.\n *\n * @param children The children to traverse\n * @param $predicate Should return true for nodes that are permitted to be children of root\n * @returns The children or their descendants that match $predicate\n */\n\nfunction $descendantsMatching(children, $predicate) {\n  const result = [];\n  const stack = Array.from(children).reverse();\n  for (let child = stack.pop(); child !== undefined; child = stack.pop()) {\n    if ($predicate(child)) {\n      result.push(child);\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(child)) {\n      for (const grandchild of $lastToFirstIterator(child)) {\n        stack.push(grandchild);\n      }\n    }\n  }\n  return result;\n}\n\n/**\n * Return an iterator that yields each child of node from first to last, taking\n * care to preserve the next sibling before yielding the value in case the caller\n * removes the yielded node.\n *\n * @param node The node whose children to iterate\n * @returns An iterator of the node's children\n */\nfunction $firstToLastIterator(node) {\n  return $childIterator((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(node, 'next'));\n}\n\n/**\n * Return an iterator that yields each child of node from last to first, taking\n * care to preserve the previous sibling before yielding the value in case the caller\n * removes the yielded node.\n *\n * @param node The node whose children to iterate\n * @returns An iterator of the node's children\n */\nfunction $lastToFirstIterator(node) {\n  return $childIterator((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(node, 'previous'));\n}\nfunction $childIterator(startCaret) {\n  const seen = new Set() ;\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.makeStepwiseIterator)({\n    hasNext: lexical__WEBPACK_IMPORTED_MODULE_0__.$isSiblingCaret,\n    initial: startCaret.getAdjacentCaret(),\n    map: caret => {\n      const origin = caret.origin.getLatest();\n      if (seen !== null) {\n        const key = origin.getKey();\n        if (!!seen.has(key)) {\n          formatDevErrorMessage(`$childIterator: Cycle detected, node with key ${String(key)} has already been traversed`);\n        }\n        seen.add(key);\n      }\n      return origin;\n    },\n    step: caret => caret.getAdjacentCaret()\n  });\n}\n\n/**\n * Replace this node with its children\n *\n * @param node The ElementNode to unwrap and remove\n */\nfunction $unwrapNode(node) {\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$rewindSiblingCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(node, 'next')).splice(1, node.getChildren());\n}\n\n/**\n * Returns the Node sibling when this exists, otherwise the closest parent sibling. For example\n * R -> P -> T1, T2\n *   -> P2\n * returns T2 for node T1, P2 for node T2, and null for node P2.\n * @param node LexicalNode.\n * @returns An array (tuple) containing the found Lexical node and the depth difference, or null, if this node doesn't exist.\n */\nfunction $getAdjacentSiblingOrParentSiblingCaret(startCaret, rootMode = 'root') {\n  let depthDiff = 0;\n  let caret = startCaret;\n  let nextCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)(caret);\n  while (nextCaret === null) {\n    depthDiff--;\n    nextCaret = caret.getParentCaret(rootMode);\n    if (!nextCaret) {\n      return null;\n    }\n    caret = nextCaret;\n    nextCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)(caret);\n  }\n  return nextCaret && [nextCaret, depthDiff];\n}\n\n/**\n * A wrapper that creates bound functions and methods for the\n * StateConfig to save some boilerplate when defining methods\n * or exporting only the accessors from your modules rather\n * than exposing the StateConfig directly.\n */\n\n/**\n * EXPERIMENTAL\n *\n * A convenience interface for working with {@link $getState} and\n * {@link $setState}.\n *\n * @param stateConfig The stateConfig to wrap with convenience functionality\n * @returns a StateWrapper\n */\nfunction makeStateWrapper(stateConfig) {\n  const $get = node => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getState)(node, stateConfig);\n  const $set = (node, valueOrUpdater) => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setState)(node, stateConfig, valueOrUpdater);\n  return {\n    $get,\n    $set,\n    accessors: [$get, $set],\n    makeGetterMethod: () => function $getter() {\n      return $get(this);\n    },\n    makeSetterMethod: () => function $setter(valueOrUpdater) {\n      return $set(this, valueOrUpdater);\n    },\n    stateConfig\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\n");

/***/ })

};
;