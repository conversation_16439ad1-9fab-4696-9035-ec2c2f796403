/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/bills/route";
exports.ids = ["app/api/bills/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbills%2Froute&page=%2Fapi%2Fbills%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbills%2Froute.ts&appDir=C%3A%5CUsers%5Cjimmy%5CDesktop%5Cnord-coast%5Cbackend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjimmy%5CDesktop%5Cnord-coast%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbills%2Froute&page=%2Fapi%2Fbills%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbills%2Froute.ts&appDir=C%3A%5CUsers%5Cjimmy%5CDesktop%5Cnord-coast%5Cbackend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjimmy%5CDesktop%5Cnord-coast%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jimmy_Desktop_nord_coast_backend_src_app_api_bills_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/bills/route.ts */ \"(rsc)/./src/app/api/bills/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_jimmy_Desktop_nord_coast_backend_src_app_api_bills_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_jimmy_Desktop_nord_coast_backend_src_app_api_bills_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/bills/route\",\n        pathname: \"/api/bills\",\n        filename: \"route\",\n        bundlePath: \"app/api/bills/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\backend\\\\src\\\\app\\\\api\\\\bills\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jimmy_Desktop_nord_coast_backend_src_app_api_bills_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbills%2Froute&page=%2Fapi%2Fbills%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbills%2Froute.ts&appDir=C%3A%5CUsers%5Cjimmy%5CDesktop%5Cnord-coast%5Cbackend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjimmy%5CDesktop%5Cnord-coast%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/bills/route.ts":
/*!************************************!*\
  !*** ./src/app/api/bills/route.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_payload_auth_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/payload-auth-middleware */ \"(rsc)/./src/lib/payload-auth-middleware.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_payload_auth_middleware__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_payload_auth_middleware__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n/**\n * GET /api/bills - Get all bills\n */ async function GET(request) {\n    try {\n        // Authenticate with Payload CMS\n        const authContext = await (0,_lib_payload_auth_middleware__WEBPACK_IMPORTED_MODULE_1__.authenticateWithPayload)(request);\n        if (!authContext) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        // Extract query parameters\n        const url = new URL(request.url);\n        const limit = parseInt(url.searchParams.get('limit') || '10');\n        const page = parseInt(url.searchParams.get('page') || '1');\n        const depth = parseInt(url.searchParams.get('depth') || '2');\n        const status = url.searchParams.get('status');\n        const billType = url.searchParams.get('billType');\n        const patientId = url.searchParams.get('patientId');\n        // Build where clause for filtering\n        const where = {};\n        if (status) {\n            where.status = {\n                equals: status\n            };\n        }\n        if (billType) {\n            where.billType = {\n                equals: billType\n            };\n        }\n        if (patientId) {\n            where.patient = {\n                equals: patientId\n            };\n        }\n        // Fetch bills from Payload CMS with relationships\n        const result = await (0,_lib_payload_auth_middleware__WEBPACK_IMPORTED_MODULE_1__.makeAuthenticatedPayloadRequest)(authContext, 'bills', 'find', {\n            limit,\n            page,\n            depth,\n            where: Object.keys(where).length > 0 ? where : undefined,\n            sort: '-createdAt'\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('Error fetching bills:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch bills'\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * POST /api/bills - Create a new bill\n */ async function POST(request) {\n    try {\n        // Authenticate with Payload CMS\n        const authContext = await (0,_lib_payload_auth_middleware__WEBPACK_IMPORTED_MODULE_1__.authenticateWithPayload)(request);\n        if (!authContext) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        // Parse request body\n        const billData = await request.json();\n        // Add the current user as the creator\n        billData.createdBy = authContext.user.id;\n        // Validate required fields\n        if (!billData.patient) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Patient is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!billData.subtotal || billData.subtotal < 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Valid subtotal is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!billData.totalAmount || billData.totalAmount < 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Valid total amount is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!billData.description) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Description is required'\n            }, {\n                status: 400\n            });\n        }\n        // Set default due date if not provided (30 days from now)\n        if (!billData.dueDate) {\n            const dueDate = new Date();\n            dueDate.setDate(dueDate.getDate() + 30);\n            billData.dueDate = dueDate.toISOString();\n        }\n        // Create bill in Payload CMS\n        const result = await (0,_lib_payload_auth_middleware__WEBPACK_IMPORTED_MODULE_1__.makeAuthenticatedPayloadRequest)(authContext, 'bills', 'create', {\n            data: billData\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('Error creating bill:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create bill'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/bills/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Appointments.ts":
/*!*****************************************!*\
  !*** ./src/collections/Appointments.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Appointments: () => (/* binding */ Appointments)\n/* harmony export */ });\nconst Appointments = {\n    slug: 'appointments',\n    admin: {\n        defaultColumns: [\n            'appointmentDate',\n            'appointmentType',\n            'treatment',\n            'patient',\n            'status'\n        ],\n        listSearchableFields: [\n            'treatment.name',\n            'patient.fullName'\n        ]\n    },\n    access: {\n        // Read: Admin and Front-desk see all, Doctors see only their own appointments\n        read: ({ req: { user } })=>{\n            if (!user) return false;\n            if (user.role === 'admin' || user.role === 'front-desk') {\n                return true; // Can see all appointments\n            }\n            if (user.role === 'doctor') {\n                return {\n                    practitioner: {\n                        equals: user.id\n                    }\n                };\n            }\n            return false;\n        },\n        // Create: Admin and Front-desk can create appointments, Doctors cannot\n        create: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        },\n        // Update: Admin and Front-desk can update all, Doctors can update only their own\n        update: ({ req: { user } })=>{\n            if (!user) return false;\n            if (user.role === 'admin' || user.role === 'front-desk') {\n                return true; // Can update all appointments\n            }\n            if (user.role === 'doctor') {\n                return {\n                    practitioner: {\n                        equals: user.id\n                    }\n                };\n            }\n            return false;\n        },\n        // Delete: Only Admin and Front-desk can delete appointments\n        delete: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        }\n    },\n    fields: [\n        // 预约分类字段\n        {\n            name: 'appointmentType',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '咨询预约',\n                    value: 'consultation'\n                },\n                {\n                    label: '治疗预约',\n                    value: 'treatment'\n                }\n            ],\n            defaultValue: 'consultation',\n            label: '预约类型',\n            admin: {\n                description: '区分咨询预约和治疗预约'\n            }\n        },\n        {\n            name: 'appointmentDate',\n            type: 'date',\n            required: true,\n            label: 'Appointment Date & Time',\n            admin: {\n                date: {\n                    pickerAppearance: 'dayAndTime'\n                }\n            }\n        },\n        {\n            name: 'status',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '已预约',\n                    value: 'scheduled'\n                },\n                {\n                    label: '已确认',\n                    value: 'confirmed'\n                },\n                {\n                    label: '已完成',\n                    value: 'completed'\n                },\n                {\n                    label: '已取消',\n                    value: 'cancelled'\n                },\n                {\n                    label: '未到诊',\n                    value: 'no-show'\n                }\n            ],\n            defaultValue: 'scheduled',\n            label: '预约状态'\n        },\n        {\n            name: 'treatment',\n            type: 'relationship',\n            relationTo: 'treatments',\n            required: false,\n            hasMany: false,\n            label: 'Treatment',\n            admin: {\n                description: '治疗预约必填，咨询预约可选',\n                condition: (data)=>data.appointmentType === 'treatment'\n            }\n        },\n        {\n            name: 'price',\n            type: 'number',\n            required: false,\n            label: 'Final Price',\n            min: 0,\n            admin: {\n                description: '最终价格（咨询预约可能为0）'\n            }\n        },\n        {\n            name: 'durationInMinutes',\n            type: 'number',\n            required: true,\n            label: 'Duration (minutes)',\n            min: 1\n        },\n        {\n            name: 'patient',\n            type: 'relationship',\n            relationTo: 'patients',\n            required: true,\n            hasMany: false,\n            label: 'Patient'\n        },\n        {\n            name: 'practitioner',\n            type: 'relationship',\n            relationTo: 'users',\n            required: true,\n            hasMany: false,\n            label: 'Practitioner'\n        },\n        // 咨询预约专用字段\n        {\n            name: 'consultationType',\n            type: 'select',\n            options: [\n                {\n                    label: '初次咨询',\n                    value: 'initial'\n                },\n                {\n                    label: '复诊咨询',\n                    value: 'follow-up'\n                },\n                {\n                    label: '价格咨询',\n                    value: 'price-inquiry'\n                }\n            ],\n            label: '咨询类型',\n            admin: {\n                description: '仅咨询预约需要填写',\n                condition: (data)=>data.appointmentType === 'consultation'\n            }\n        },\n        {\n            name: 'interestedTreatments',\n            type: 'relationship',\n            relationTo: 'treatments',\n            hasMany: true,\n            label: '感兴趣的治疗项目',\n            admin: {\n                description: '咨询用户感兴趣的治疗项目',\n                condition: (data)=>data.appointmentType === 'consultation'\n            }\n        },\n        // 支付状态字段\n        {\n            name: 'paymentStatus',\n            type: 'select',\n            options: [\n                {\n                    label: '待支付',\n                    value: 'pending'\n                },\n                {\n                    label: '部分支付',\n                    value: 'partial'\n                },\n                {\n                    label: '已支付',\n                    value: 'paid'\n                },\n                {\n                    label: '逾期',\n                    value: 'overdue'\n                }\n            ],\n            label: '支付状态',\n            admin: {\n                description: '治疗预约的支付状态',\n                condition: (data)=>data.appointmentType === 'treatment'\n            }\n        },\n        // 结果追踪字段\n        {\n            name: 'outcome',\n            type: 'select',\n            options: [\n                {\n                    label: '已转换',\n                    value: 'converted'\n                },\n                {\n                    label: '已预约治疗',\n                    value: 'scheduled-treatment'\n                },\n                {\n                    label: '无兴趣',\n                    value: 'no-interest'\n                },\n                {\n                    label: '需要后续跟进',\n                    value: 'follow-up-needed'\n                }\n            ],\n            label: '咨询结果',\n            admin: {\n                description: '咨询预约的结果追踪',\n                condition: (data)=>data.appointmentType === 'consultation' && data.status === 'completed'\n            }\n        },\n        // 备注字段\n        {\n            name: 'notes',\n            type: 'textarea',\n            label: '预约备注',\n            admin: {\n                description: '预约相关的备注信息'\n            }\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Appointments.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/BillItems.ts":
/*!**************************************!*\
  !*** ./src/collections/BillItems.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BillItems: () => (/* binding */ BillItems)\n/* harmony export */ });\nconst BillItems = {\n    slug: 'bill-items',\n    admin: {\n        useAsTitle: 'itemName',\n        defaultColumns: [\n            'bill',\n            'itemName',\n            'quantity',\n            'unitPrice',\n            'totalPrice'\n        ],\n        listSearchableFields: [\n            'itemName',\n            'description'\n        ]\n    },\n    access: {\n        // Read: Same as Bills - Admin and Front-desk see all, Doctors see only related items\n        read: ({ req: { user } })=>{\n            if (!user) return false;\n            if (user.role === 'admin' || user.role === 'front-desk') {\n                return true;\n            }\n            if (user.role === 'doctor') {\n                return {\n                    or: [\n                        {\n                            'bill.appointment.practitioner': {\n                                equals: user.id\n                            }\n                        },\n                        {\n                            'bill.createdBy': {\n                                equals: user.id\n                            }\n                        }\n                    ]\n                };\n            }\n            return false;\n        },\n        // Create: Admin and Front-desk can create bill items\n        create: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        },\n        // Update: Admin and Front-desk can update bill items\n        update: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        },\n        // Delete: Admin and Front-desk can delete bill items\n        delete: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        }\n    },\n    hooks: {\n        beforeChange: [\n            ({ data })=>{\n                // Calculate total price based on quantity, unit price, and discount\n                const subtotal = (data.quantity || 0) * (data.unitPrice || 0);\n                const discountAmount = subtotal * ((data.discountRate || 0) / 100);\n                data.totalPrice = subtotal - discountAmount;\n                return data;\n            }\n        ]\n    },\n    fields: [\n        // 关联账单\n        {\n            name: 'bill',\n            type: 'relationship',\n            relationTo: 'bills',\n            required: true,\n            hasMany: false,\n            label: '所属账单'\n        },\n        // 项目信息\n        {\n            name: 'itemType',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '治疗项目',\n                    value: 'treatment'\n                },\n                {\n                    label: '咨询服务',\n                    value: 'consultation'\n                },\n                {\n                    label: '材料费用',\n                    value: 'material'\n                },\n                {\n                    label: '其他服务',\n                    value: 'service'\n                }\n            ],\n            defaultValue: 'treatment',\n            label: '项目类型'\n        },\n        {\n            name: 'itemId',\n            type: 'text',\n            label: '项目ID',\n            admin: {\n                description: '关联的治疗或服务的ID（可选）'\n            }\n        },\n        {\n            name: 'itemName',\n            type: 'text',\n            required: true,\n            label: '项目名称',\n            admin: {\n                description: '账单项目的名称'\n            }\n        },\n        {\n            name: 'description',\n            type: 'textarea',\n            label: '项目描述',\n            admin: {\n                description: '项目的详细描述'\n            }\n        },\n        // 数量和价格\n        {\n            name: 'quantity',\n            type: 'number',\n            required: true,\n            defaultValue: 1,\n            label: '数量',\n            min: 0.01,\n            admin: {\n                description: '项目数量'\n            }\n        },\n        {\n            name: 'unitPrice',\n            type: 'number',\n            required: true,\n            label: '单价',\n            min: 0,\n            admin: {\n                description: '项目单价'\n            }\n        },\n        {\n            name: 'discountRate',\n            type: 'number',\n            defaultValue: 0,\n            label: '折扣率 (%)',\n            min: 0,\n            max: 100,\n            admin: {\n                description: '折扣率，0-100之间的数值'\n            }\n        },\n        {\n            name: 'totalPrice',\n            type: 'number',\n            label: '小计金额',\n            admin: {\n                description: '该项目的总金额（自动计算）',\n                readOnly: true\n            }\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29sbGVjdGlvbnMvQmlsbEl0ZW1zLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFFTyxNQUFNQSxZQUE4QjtJQUN6Q0MsTUFBTTtJQUNOQyxPQUFPO1FBQ0xDLFlBQVk7UUFDWkMsZ0JBQWdCO1lBQUM7WUFBUTtZQUFZO1lBQVk7WUFBYTtTQUFhO1FBQzNFQyxzQkFBc0I7WUFBQztZQUFZO1NBQWM7SUFDbkQ7SUFDQUMsUUFBUTtRQUNOLHFGQUFxRjtRQUNyRkMsTUFBTyxDQUFDLEVBQUVDLEtBQUssRUFBRUMsSUFBSSxFQUFFLEVBQUU7WUFDdkIsSUFBSSxDQUFDQSxNQUFNLE9BQU87WUFDbEIsSUFBSUEsS0FBS0MsSUFBSSxLQUFLLFdBQVdELEtBQUtDLElBQUksS0FBSyxjQUFjO2dCQUN2RCxPQUFPO1lBQ1Q7WUFDQSxJQUFJRCxLQUFLQyxJQUFJLEtBQUssVUFBVTtnQkFDMUIsT0FBTztvQkFDTEMsSUFBSTt3QkFDRjs0QkFDRSxpQ0FBaUM7Z0NBQy9CQyxRQUFRSCxLQUFLSSxFQUFFOzRCQUNqQjt3QkFDRjt3QkFDQTs0QkFDRSxrQkFBa0I7Z0NBQ2hCRCxRQUFRSCxLQUFLSSxFQUFFOzRCQUNqQjt3QkFDRjtxQkFDRDtnQkFDSDtZQUNGO1lBQ0EsT0FBTztRQUNUO1FBRUEscURBQXFEO1FBQ3JEQyxRQUFRLENBQUMsRUFBRU4sS0FBSyxFQUFFQyxJQUFJLEVBQUUsRUFBRTtZQUN4QixJQUFJLENBQUNBLE1BQU0sT0FBTztZQUNsQixPQUFPQSxLQUFLQyxJQUFJLEtBQUssV0FBV0QsS0FBS0MsSUFBSSxLQUFLO1FBQ2hEO1FBRUEscURBQXFEO1FBQ3JESyxRQUFRLENBQUMsRUFBRVAsS0FBSyxFQUFFQyxJQUFJLEVBQUUsRUFBRTtZQUN4QixJQUFJLENBQUNBLE1BQU0sT0FBTztZQUNsQixPQUFPQSxLQUFLQyxJQUFJLEtBQUssV0FBV0QsS0FBS0MsSUFBSSxLQUFLO1FBQ2hEO1FBRUEscURBQXFEO1FBQ3JETSxRQUFRLENBQUMsRUFBRVIsS0FBSyxFQUFFQyxJQUFJLEVBQUUsRUFBRTtZQUN4QixJQUFJLENBQUNBLE1BQU0sT0FBTztZQUNsQixPQUFPQSxLQUFLQyxJQUFJLEtBQUssV0FBV0QsS0FBS0MsSUFBSSxLQUFLO1FBQ2hEO0lBQ0Y7SUFDQU8sT0FBTztRQUNMQyxjQUFjO1lBQ1osQ0FBQyxFQUFFQyxJQUFJLEVBQUU7Z0JBQ1Asb0VBQW9FO2dCQUNwRSxNQUFNQyxXQUFXLENBQUNELEtBQUtFLFFBQVEsSUFBSSxLQUFNRixDQUFBQSxLQUFLRyxTQUFTLElBQUk7Z0JBQzNELE1BQU1DLGlCQUFpQkgsV0FBWSxFQUFDRCxLQUFLSyxZQUFZLElBQUksS0FBSyxHQUFFO2dCQUNoRUwsS0FBS00sVUFBVSxHQUFHTCxXQUFXRztnQkFFN0IsT0FBT0o7WUFDVDtTQUNEO0lBQ0g7SUFDQU8sUUFBUTtRQUNOLE9BQU87UUFDUDtZQUNFQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsWUFBWTtZQUNaQyxVQUFVO1lBQ1ZDLFNBQVM7WUFDVEMsT0FBTztRQUNUO1FBRUEsT0FBTztRQUNQO1lBQ0VMLE1BQU07WUFDTkMsTUFBTTtZQUNORSxVQUFVO1lBQ1ZHLFNBQVM7Z0JBQ1A7b0JBQ0VELE9BQU87b0JBQ1BFLE9BQU87Z0JBQ1Q7Z0JBQ0E7b0JBQ0VGLE9BQU87b0JBQ1BFLE9BQU87Z0JBQ1Q7Z0JBQ0E7b0JBQ0VGLE9BQU87b0JBQ1BFLE9BQU87Z0JBQ1Q7Z0JBQ0E7b0JBQ0VGLE9BQU87b0JBQ1BFLE9BQU87Z0JBQ1Q7YUFDRDtZQUNEQyxjQUFjO1lBQ2RILE9BQU87UUFDVDtRQUNBO1lBQ0VMLE1BQU07WUFDTkMsTUFBTTtZQUNOSSxPQUFPO1lBQ1A5QixPQUFPO2dCQUNMa0MsYUFBYTtZQUNmO1FBQ0Y7UUFDQTtZQUNFVCxNQUFNO1lBQ05DLE1BQU07WUFDTkUsVUFBVTtZQUNWRSxPQUFPO1lBQ1A5QixPQUFPO2dCQUNMa0MsYUFBYTtZQUNmO1FBQ0Y7UUFDQTtZQUNFVCxNQUFNO1lBQ05DLE1BQU07WUFDTkksT0FBTztZQUNQOUIsT0FBTztnQkFDTGtDLGFBQWE7WUFDZjtRQUNGO1FBRUEsUUFBUTtRQUNSO1lBQ0VULE1BQU07WUFDTkMsTUFBTTtZQUNORSxVQUFVO1lBQ1ZLLGNBQWM7WUFDZEgsT0FBTztZQUNQSyxLQUFLO1lBQ0xuQyxPQUFPO2dCQUNMa0MsYUFBYTtZQUNmO1FBQ0Y7UUFDQTtZQUNFVCxNQUFNO1lBQ05DLE1BQU07WUFDTkUsVUFBVTtZQUNWRSxPQUFPO1lBQ1BLLEtBQUs7WUFDTG5DLE9BQU87Z0JBQ0xrQyxhQUFhO1lBQ2Y7UUFDRjtRQUNBO1lBQ0VULE1BQU07WUFDTkMsTUFBTTtZQUNOTyxjQUFjO1lBQ2RILE9BQU87WUFDUEssS0FBSztZQUNMQyxLQUFLO1lBQ0xwQyxPQUFPO2dCQUNMa0MsYUFBYTtZQUNmO1FBQ0Y7UUFDQTtZQUNFVCxNQUFNO1lBQ05DLE1BQU07WUFDTkksT0FBTztZQUNQOUIsT0FBTztnQkFDTGtDLGFBQWE7Z0JBQ2JHLFVBQVU7WUFDWjtRQUNGO0tBQ0Q7QUFDSCxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGppbW15XFxEZXNrdG9wXFxub3JkLWNvYXN0XFxiYWNrZW5kXFxzcmNcXGNvbGxlY3Rpb25zXFxCaWxsSXRlbXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBDb2xsZWN0aW9uQ29uZmlnLCBBY2Nlc3MgfSBmcm9tICdwYXlsb2FkJ1xuXG5leHBvcnQgY29uc3QgQmlsbEl0ZW1zOiBDb2xsZWN0aW9uQ29uZmlnID0ge1xuICBzbHVnOiAnYmlsbC1pdGVtcycsXG4gIGFkbWluOiB7XG4gICAgdXNlQXNUaXRsZTogJ2l0ZW1OYW1lJyxcbiAgICBkZWZhdWx0Q29sdW1uczogWydiaWxsJywgJ2l0ZW1OYW1lJywgJ3F1YW50aXR5JywgJ3VuaXRQcmljZScsICd0b3RhbFByaWNlJ10sXG4gICAgbGlzdFNlYXJjaGFibGVGaWVsZHM6IFsnaXRlbU5hbWUnLCAnZGVzY3JpcHRpb24nXSxcbiAgfSxcbiAgYWNjZXNzOiB7XG4gICAgLy8gUmVhZDogU2FtZSBhcyBCaWxscyAtIEFkbWluIGFuZCBGcm9udC1kZXNrIHNlZSBhbGwsIERvY3RvcnMgc2VlIG9ubHkgcmVsYXRlZCBpdGVtc1xuICAgIHJlYWQ6ICgoeyByZXE6IHsgdXNlciB9IH0pID0+IHtcbiAgICAgIGlmICghdXNlcikgcmV0dXJuIGZhbHNlO1xuICAgICAgaWYgKHVzZXIucm9sZSA9PT0gJ2FkbWluJyB8fCB1c2VyLnJvbGUgPT09ICdmcm9udC1kZXNrJykge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgIH1cbiAgICAgIGlmICh1c2VyLnJvbGUgPT09ICdkb2N0b3InKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgb3I6IFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgJ2JpbGwuYXBwb2ludG1lbnQucHJhY3RpdGlvbmVyJzoge1xuICAgICAgICAgICAgICAgIGVxdWFsczogdXNlci5pZCxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgICdiaWxsLmNyZWF0ZWRCeSc6IHtcbiAgICAgICAgICAgICAgICBlcXVhbHM6IHVzZXIuaWQsXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9LFxuICAgICAgICAgIF0sXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfSkgYXMgQWNjZXNzLFxuXG4gICAgLy8gQ3JlYXRlOiBBZG1pbiBhbmQgRnJvbnQtZGVzayBjYW4gY3JlYXRlIGJpbGwgaXRlbXNcbiAgICBjcmVhdGU6ICh7IHJlcTogeyB1c2VyIH0gfSkgPT4ge1xuICAgICAgaWYgKCF1c2VyKSByZXR1cm4gZmFsc2U7XG4gICAgICByZXR1cm4gdXNlci5yb2xlID09PSAnYWRtaW4nIHx8IHVzZXIucm9sZSA9PT0gJ2Zyb250LWRlc2snO1xuICAgIH0sXG5cbiAgICAvLyBVcGRhdGU6IEFkbWluIGFuZCBGcm9udC1kZXNrIGNhbiB1cGRhdGUgYmlsbCBpdGVtc1xuICAgIHVwZGF0ZTogKHsgcmVxOiB7IHVzZXIgfSB9KSA9PiB7XG4gICAgICBpZiAoIXVzZXIpIHJldHVybiBmYWxzZTtcbiAgICAgIHJldHVybiB1c2VyLnJvbGUgPT09ICdhZG1pbicgfHwgdXNlci5yb2xlID09PSAnZnJvbnQtZGVzayc7XG4gICAgfSxcblxuICAgIC8vIERlbGV0ZTogQWRtaW4gYW5kIEZyb250LWRlc2sgY2FuIGRlbGV0ZSBiaWxsIGl0ZW1zXG4gICAgZGVsZXRlOiAoeyByZXE6IHsgdXNlciB9IH0pID0+IHtcbiAgICAgIGlmICghdXNlcikgcmV0dXJuIGZhbHNlO1xuICAgICAgcmV0dXJuIHVzZXIucm9sZSA9PT0gJ2FkbWluJyB8fCB1c2VyLnJvbGUgPT09ICdmcm9udC1kZXNrJztcbiAgICB9LFxuICB9LFxuICBob29rczoge1xuICAgIGJlZm9yZUNoYW5nZTogW1xuICAgICAgKHsgZGF0YSB9KSA9PiB7XG4gICAgICAgIC8vIENhbGN1bGF0ZSB0b3RhbCBwcmljZSBiYXNlZCBvbiBxdWFudGl0eSwgdW5pdCBwcmljZSwgYW5kIGRpc2NvdW50XG4gICAgICAgIGNvbnN0IHN1YnRvdGFsID0gKGRhdGEucXVhbnRpdHkgfHwgMCkgKiAoZGF0YS51bml0UHJpY2UgfHwgMCk7XG4gICAgICAgIGNvbnN0IGRpc2NvdW50QW1vdW50ID0gc3VidG90YWwgKiAoKGRhdGEuZGlzY291bnRSYXRlIHx8IDApIC8gMTAwKTtcbiAgICAgICAgZGF0YS50b3RhbFByaWNlID0gc3VidG90YWwgLSBkaXNjb3VudEFtb3VudDtcbiAgICAgICAgXG4gICAgICAgIHJldHVybiBkYXRhO1xuICAgICAgfSxcbiAgICBdLFxuICB9LFxuICBmaWVsZHM6IFtcbiAgICAvLyDlhbPogZTotKbljZVcbiAgICB7XG4gICAgICBuYW1lOiAnYmlsbCcsXG4gICAgICB0eXBlOiAncmVsYXRpb25zaGlwJyxcbiAgICAgIHJlbGF0aW9uVG86ICdiaWxscycsXG4gICAgICByZXF1aXJlZDogdHJ1ZSxcbiAgICAgIGhhc01hbnk6IGZhbHNlLFxuICAgICAgbGFiZWw6ICfmiYDlsZ7otKbljZUnLFxuICAgIH0sXG4gICAgXG4gICAgLy8g6aG555uu5L+h5oGvXG4gICAge1xuICAgICAgbmFtZTogJ2l0ZW1UeXBlJyxcbiAgICAgIHR5cGU6ICdzZWxlY3QnLFxuICAgICAgcmVxdWlyZWQ6IHRydWUsXG4gICAgICBvcHRpb25zOiBbXG4gICAgICAgIHtcbiAgICAgICAgICBsYWJlbDogJ+ayu+eWl+mhueebricsXG4gICAgICAgICAgdmFsdWU6ICd0cmVhdG1lbnQnLFxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgbGFiZWw6ICflkqjor6LmnI3liqEnLFxuICAgICAgICAgIHZhbHVlOiAnY29uc3VsdGF0aW9uJyxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGxhYmVsOiAn5p2Q5paZ6LS555SoJyxcbiAgICAgICAgICB2YWx1ZTogJ21hdGVyaWFsJyxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGxhYmVsOiAn5YW25LuW5pyN5YqhJyxcbiAgICAgICAgICB2YWx1ZTogJ3NlcnZpY2UnLFxuICAgICAgICB9LFxuICAgICAgXSxcbiAgICAgIGRlZmF1bHRWYWx1ZTogJ3RyZWF0bWVudCcsXG4gICAgICBsYWJlbDogJ+mhueebruexu+WeiycsXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnaXRlbUlkJyxcbiAgICAgIHR5cGU6ICd0ZXh0JyxcbiAgICAgIGxhYmVsOiAn6aG555uuSUQnLFxuICAgICAgYWRtaW46IHtcbiAgICAgICAgZGVzY3JpcHRpb246ICflhbPogZTnmoTmsrvnlpfmiJbmnI3liqHnmoRJRO+8iOWPr+mAie+8iScsXG4gICAgICB9LFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ2l0ZW1OYW1lJyxcbiAgICAgIHR5cGU6ICd0ZXh0JyxcbiAgICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgICAgbGFiZWw6ICfpobnnm67lkI3np7AnLFxuICAgICAgYWRtaW46IHtcbiAgICAgICAgZGVzY3JpcHRpb246ICfotKbljZXpobnnm67nmoTlkI3np7AnLFxuICAgICAgfSxcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICdkZXNjcmlwdGlvbicsXG4gICAgICB0eXBlOiAndGV4dGFyZWEnLFxuICAgICAgbGFiZWw6ICfpobnnm67mj4/ov7AnLFxuICAgICAgYWRtaW46IHtcbiAgICAgICAgZGVzY3JpcHRpb246ICfpobnnm67nmoTor6bnu4bmj4/ov7AnLFxuICAgICAgfSxcbiAgICB9LFxuICAgIFxuICAgIC8vIOaVsOmHj+WSjOS7t+agvFxuICAgIHtcbiAgICAgIG5hbWU6ICdxdWFudGl0eScsXG4gICAgICB0eXBlOiAnbnVtYmVyJyxcbiAgICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgICAgZGVmYXVsdFZhbHVlOiAxLFxuICAgICAgbGFiZWw6ICfmlbDph48nLFxuICAgICAgbWluOiAwLjAxLFxuICAgICAgYWRtaW46IHtcbiAgICAgICAgZGVzY3JpcHRpb246ICfpobnnm67mlbDph48nLFxuICAgICAgfSxcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICd1bml0UHJpY2UnLFxuICAgICAgdHlwZTogJ251bWJlcicsXG4gICAgICByZXF1aXJlZDogdHJ1ZSxcbiAgICAgIGxhYmVsOiAn5Y2V5Lu3JyxcbiAgICAgIG1pbjogMCxcbiAgICAgIGFkbWluOiB7XG4gICAgICAgIGRlc2NyaXB0aW9uOiAn6aG555uu5Y2V5Lu3JyxcbiAgICAgIH0sXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnZGlzY291bnRSYXRlJyxcbiAgICAgIHR5cGU6ICdudW1iZXInLFxuICAgICAgZGVmYXVsdFZhbHVlOiAwLFxuICAgICAgbGFiZWw6ICfmipjmiaPnjocgKCUpJyxcbiAgICAgIG1pbjogMCxcbiAgICAgIG1heDogMTAwLFxuICAgICAgYWRtaW46IHtcbiAgICAgICAgZGVzY3JpcHRpb246ICfmipjmiaPnjofvvIwwLTEwMOS5i+mXtOeahOaVsOWAvCcsXG4gICAgICB9LFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ3RvdGFsUHJpY2UnLFxuICAgICAgdHlwZTogJ251bWJlcicsXG4gICAgICBsYWJlbDogJ+Wwj+iuoemHkeminScsXG4gICAgICBhZG1pbjoge1xuICAgICAgICBkZXNjcmlwdGlvbjogJ+ivpemhueebrueahOaAu+mHkemine+8iOiHquWKqOiuoeeul++8iScsXG4gICAgICAgIHJlYWRPbmx5OiB0cnVlLFxuICAgICAgfSxcbiAgICB9LFxuICBdLFxufVxuIl0sIm5hbWVzIjpbIkJpbGxJdGVtcyIsInNsdWciLCJhZG1pbiIsInVzZUFzVGl0bGUiLCJkZWZhdWx0Q29sdW1ucyIsImxpc3RTZWFyY2hhYmxlRmllbGRzIiwiYWNjZXNzIiwicmVhZCIsInJlcSIsInVzZXIiLCJyb2xlIiwib3IiLCJlcXVhbHMiLCJpZCIsImNyZWF0ZSIsInVwZGF0ZSIsImRlbGV0ZSIsImhvb2tzIiwiYmVmb3JlQ2hhbmdlIiwiZGF0YSIsInN1YnRvdGFsIiwicXVhbnRpdHkiLCJ1bml0UHJpY2UiLCJkaXNjb3VudEFtb3VudCIsImRpc2NvdW50UmF0ZSIsInRvdGFsUHJpY2UiLCJmaWVsZHMiLCJuYW1lIiwidHlwZSIsInJlbGF0aW9uVG8iLCJyZXF1aXJlZCIsImhhc01hbnkiLCJsYWJlbCIsIm9wdGlvbnMiLCJ2YWx1ZSIsImRlZmF1bHRWYWx1ZSIsImRlc2NyaXB0aW9uIiwibWluIiwibWF4IiwicmVhZE9ubHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/BillItems.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Bills.ts":
/*!**********************************!*\
  !*** ./src/collections/Bills.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bills: () => (/* binding */ Bills)\n/* harmony export */ });\nconst Bills = {\n    slug: 'bills',\n    admin: {\n        useAsTitle: 'billNumber',\n        defaultColumns: [\n            'billNumber',\n            'patient',\n            'billType',\n            'status',\n            'totalAmount',\n            'remainingAmount'\n        ],\n        listSearchableFields: [\n            'billNumber',\n            'patient.fullName',\n            'description'\n        ]\n    },\n    access: {\n        // Read: Admin and Front-desk see all bills, Doctors see only bills related to their appointments\n        read: ({ req: { user } })=>{\n            if (!user) return false;\n            if (user.role === 'admin' || user.role === 'front-desk') {\n                return true; // Can see all bills\n            }\n            if (user.role === 'doctor') {\n                return {\n                    or: [\n                        {\n                            'appointment.practitioner': {\n                                equals: user.id\n                            }\n                        },\n                        {\n                            createdBy: {\n                                equals: user.id\n                            }\n                        }\n                    ]\n                };\n            }\n            return false;\n        },\n        // Create: Admin and Front-desk can create bills\n        create: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        },\n        // Update: Admin and Front-desk can update bills\n        update: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        },\n        // Delete: Only Admin can delete bills\n        delete: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin';\n        }\n    },\n    hooks: {\n        beforeChange: [\n            ({ data })=>{\n                // Auto-generate bill number if not provided\n                if (!data.billNumber) {\n                    const now = new Date();\n                    const year = now.getFullYear();\n                    const month = String(now.getMonth() + 1).padStart(2, '0');\n                    const day = String(now.getDate()).padStart(2, '0');\n                    const timestamp = now.getTime().toString().slice(-6);\n                    data.billNumber = `BILL-${year}${month}${day}-${timestamp}`;\n                }\n                // Calculate remaining amount\n                data.remainingAmount = data.totalAmount - (data.paidAmount || 0);\n                return data;\n            }\n        ]\n    },\n    fields: [\n        {\n            name: 'billNumber',\n            type: 'text',\n            required: true,\n            unique: true,\n            label: '账单编号',\n            admin: {\n                description: '系统自动生成，格式：BILL-YYYYMMDD-XXXXXX',\n                readOnly: true\n            }\n        },\n        // 关联信息\n        {\n            name: 'patient',\n            type: 'relationship',\n            relationTo: 'patients',\n            required: true,\n            hasMany: false,\n            label: '患者'\n        },\n        {\n            name: 'appointment',\n            type: 'relationship',\n            relationTo: 'appointments',\n            hasMany: false,\n            label: '关联预约',\n            admin: {\n                description: '如果账单来源于预约，请选择对应预约'\n            }\n        },\n        {\n            name: 'treatment',\n            type: 'relationship',\n            relationTo: 'treatments',\n            hasMany: false,\n            label: '关联治疗',\n            admin: {\n                description: '如果是治疗账单，请选择对应治疗项目'\n            }\n        },\n        // 账单基本信息\n        {\n            name: 'billType',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '治疗账单',\n                    value: 'treatment'\n                },\n                {\n                    label: '咨询账单',\n                    value: 'consultation'\n                },\n                {\n                    label: '押金账单',\n                    value: 'deposit'\n                },\n                {\n                    label: '补充账单',\n                    value: 'additional'\n                }\n            ],\n            defaultValue: 'treatment',\n            label: '账单类型'\n        },\n        {\n            name: 'status',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '草稿',\n                    value: 'draft'\n                },\n                {\n                    label: '已发送',\n                    value: 'sent'\n                },\n                {\n                    label: '已确认',\n                    value: 'confirmed'\n                },\n                {\n                    label: '已支付',\n                    value: 'paid'\n                },\n                {\n                    label: '已取消',\n                    value: 'cancelled'\n                }\n            ],\n            defaultValue: 'draft',\n            label: '账单状态'\n        },\n        // 金额信息\n        {\n            name: 'subtotal',\n            type: 'number',\n            required: true,\n            label: '小计金额',\n            min: 0,\n            admin: {\n                description: '税前金额'\n            }\n        },\n        {\n            name: 'discountAmount',\n            type: 'number',\n            defaultValue: 0,\n            label: '折扣金额',\n            min: 0,\n            admin: {\n                description: '折扣金额'\n            }\n        },\n        {\n            name: 'taxAmount',\n            type: 'number',\n            defaultValue: 0,\n            label: '税费',\n            min: 0,\n            admin: {\n                description: '税费金额'\n            }\n        },\n        {\n            name: 'totalAmount',\n            type: 'number',\n            required: true,\n            label: '总金额',\n            min: 0,\n            admin: {\n                description: '最终应付金额 = 小计 + 税费 - 折扣'\n            }\n        },\n        {\n            name: 'paidAmount',\n            type: 'number',\n            defaultValue: 0,\n            label: '已支付金额',\n            min: 0,\n            admin: {\n                description: '已支付的金额'\n            }\n        },\n        {\n            name: 'remainingAmount',\n            type: 'number',\n            label: '剩余金额',\n            admin: {\n                description: '剩余未支付金额（自动计算）',\n                readOnly: true\n            }\n        },\n        // 时间信息\n        {\n            name: 'issueDate',\n            type: 'date',\n            required: true,\n            label: '开票日期',\n            defaultValue: ()=>new Date().toISOString(),\n            admin: {\n                date: {\n                    pickerAppearance: 'dayOnly'\n                }\n            }\n        },\n        {\n            name: 'dueDate',\n            type: 'date',\n            required: true,\n            label: '到期日期',\n            admin: {\n                date: {\n                    pickerAppearance: 'dayOnly'\n                },\n                description: '账单到期日期'\n            }\n        },\n        {\n            name: 'paidDate',\n            type: 'date',\n            label: '支付完成日期',\n            admin: {\n                date: {\n                    pickerAppearance: 'dayAndTime'\n                },\n                description: '账单完全支付的日期',\n                condition: (data)=>data.status === 'paid'\n            }\n        },\n        // 详细信息\n        {\n            name: 'description',\n            type: 'text',\n            required: true,\n            label: '账单描述',\n            admin: {\n                description: '账单的简要描述'\n            }\n        },\n        {\n            name: 'notes',\n            type: 'textarea',\n            label: '备注',\n            admin: {\n                description: '账单相关的备注信息'\n            }\n        },\n        // 创建人员\n        {\n            name: 'createdBy',\n            type: 'relationship',\n            relationTo: 'users',\n            required: true,\n            hasMany: false,\n            label: '创建人员',\n            admin: {\n                description: '创建此账单的工作人员'\n            }\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Bills.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Deposits.ts":
/*!*************************************!*\
  !*** ./src/collections/Deposits.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Deposits: () => (/* binding */ Deposits)\n/* harmony export */ });\nconst Deposits = {\n    slug: 'deposits',\n    admin: {\n        useAsTitle: 'depositNumber',\n        defaultColumns: [\n            'depositNumber',\n            'patient',\n            'depositType',\n            'amount',\n            'remainingAmount',\n            'status'\n        ],\n        listSearchableFields: [\n            'depositNumber',\n            'patient.fullName',\n            'purpose'\n        ]\n    },\n    access: {\n        // Read: Same as Bills - Admin and Front-desk see all, Doctors see only related deposits\n        read: ({ req: { user } })=>{\n            if (!user) return false;\n            if (user.role === 'admin' || user.role === 'front-desk') {\n                return true;\n            }\n            if (user.role === 'doctor') {\n                return {\n                    or: [\n                        {\n                            'appointment.practitioner': {\n                                equals: user.id\n                            }\n                        },\n                        {\n                            'treatment.practitioner': {\n                                equals: user.id\n                            }\n                        }\n                    ]\n                };\n            }\n            return false;\n        },\n        // Create: Admin and Front-desk can create deposits\n        create: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        },\n        // Update: Admin and Front-desk can update deposits\n        update: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        },\n        // Delete: Only Admin can delete deposits\n        delete: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin';\n        }\n    },\n    hooks: {\n        beforeChange: [\n            ({ data })=>{\n                // Auto-generate deposit number if not provided\n                if (!data.depositNumber) {\n                    const now = new Date();\n                    const year = now.getFullYear();\n                    const month = String(now.getMonth() + 1).padStart(2, '0');\n                    const day = String(now.getDate()).padStart(2, '0');\n                    const timestamp = now.getTime().toString().slice(-6);\n                    data.depositNumber = `DEP-${year}${month}${day}-${timestamp}`;\n                }\n                // Calculate remaining amount\n                data.remainingAmount = (data.amount || 0) - (data.usedAmount || 0);\n                // Auto-update status based on remaining amount\n                if (data.remainingAmount <= 0 && data.usedAmount > 0) {\n                    data.status = 'used';\n                } else if (data.remainingAmount > 0 && data.usedAmount > 0) {\n                    data.status = 'active'; // Partially used but still active\n                }\n                return data;\n            }\n        ]\n    },\n    fields: [\n        {\n            name: 'depositNumber',\n            type: 'text',\n            required: true,\n            unique: true,\n            label: '押金编号',\n            admin: {\n                description: '系统自动生成，格式：DEP-YYYYMMDD-XXXXXX',\n                readOnly: true\n            }\n        },\n        // 关联信息\n        {\n            name: 'patient',\n            type: 'relationship',\n            relationTo: 'patients',\n            required: true,\n            hasMany: false,\n            label: '患者'\n        },\n        {\n            name: 'appointment',\n            type: 'relationship',\n            relationTo: 'appointments',\n            hasMany: false,\n            label: '关联预约',\n            admin: {\n                description: '如果押金与特定预约相关'\n            }\n        },\n        {\n            name: 'treatment',\n            type: 'relationship',\n            relationTo: 'treatments',\n            hasMany: false,\n            label: '关联治疗',\n            admin: {\n                description: '如果押金与特定治疗相关'\n            }\n        },\n        // 押金信息\n        {\n            name: 'depositType',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '治疗押金',\n                    value: 'treatment'\n                },\n                {\n                    label: '预约押金',\n                    value: 'appointment'\n                },\n                {\n                    label: '材料押金',\n                    value: 'material'\n                }\n            ],\n            defaultValue: 'treatment',\n            label: '押金类型'\n        },\n        {\n            name: 'amount',\n            type: 'number',\n            required: true,\n            label: '押金金额',\n            min: 0.01,\n            admin: {\n                description: '押金总金额'\n            }\n        },\n        {\n            name: 'status',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '有效',\n                    value: 'active'\n                },\n                {\n                    label: '已使用',\n                    value: 'used'\n                },\n                {\n                    label: '已退还',\n                    value: 'refunded'\n                },\n                {\n                    label: '已过期',\n                    value: 'expired'\n                }\n            ],\n            defaultValue: 'active',\n            label: '押金状态'\n        },\n        // 使用记录\n        {\n            name: 'usedAmount',\n            type: 'number',\n            defaultValue: 0,\n            label: '已使用金额',\n            min: 0,\n            admin: {\n                description: '已使用的押金金额'\n            }\n        },\n        {\n            name: 'remainingAmount',\n            type: 'number',\n            label: '剩余金额',\n            admin: {\n                description: '剩余可用押金金额（自动计算）',\n                readOnly: true\n            }\n        },\n        // 时间信息\n        {\n            name: 'depositDate',\n            type: 'date',\n            required: true,\n            label: '收取日期',\n            defaultValue: ()=>new Date().toISOString(),\n            admin: {\n                date: {\n                    pickerAppearance: 'dayAndTime'\n                }\n            }\n        },\n        {\n            name: 'expiryDate',\n            type: 'date',\n            label: '到期日期',\n            admin: {\n                date: {\n                    pickerAppearance: 'dayOnly'\n                },\n                description: '押金到期日期（可选）'\n            }\n        },\n        {\n            name: 'usedDate',\n            type: 'date',\n            label: '使用日期',\n            admin: {\n                date: {\n                    pickerAppearance: 'dayAndTime'\n                },\n                description: '押金使用日期',\n                condition: (data)=>data.status === 'used' || data.usedAmount > 0\n            }\n        },\n        {\n            name: 'refundDate',\n            type: 'date',\n            label: '退还日期',\n            admin: {\n                date: {\n                    pickerAppearance: 'dayAndTime'\n                },\n                description: '押金退还日期',\n                condition: (data)=>data.status === 'refunded'\n            }\n        },\n        // 备注\n        {\n            name: 'purpose',\n            type: 'text',\n            required: true,\n            label: '押金用途',\n            admin: {\n                description: '押金的具体用途说明'\n            }\n        },\n        {\n            name: 'notes',\n            type: 'textarea',\n            label: '备注',\n            admin: {\n                description: '押金相关的备注信息'\n            }\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Deposits.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Media.ts":
/*!**********************************!*\
  !*** ./src/collections/Media.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: () => (/* binding */ Media)\n/* harmony export */ });\nconst Media = {\n    slug: 'media',\n    access: {\n        read: ()=>true\n    },\n    fields: [\n        {\n            name: 'alt',\n            type: 'text',\n            required: true\n        }\n    ],\n    upload: true\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29sbGVjdGlvbnMvTWVkaWEudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUVPLE1BQU1BLFFBQTBCO0lBQ3JDQyxNQUFNO0lBQ05DLFFBQVE7UUFDTkMsTUFBTSxJQUFNO0lBQ2Q7SUFDQUMsUUFBUTtRQUNOO1lBQ0VDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxVQUFVO1FBQ1o7S0FDRDtJQUNEQyxRQUFRO0FBQ1YsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqaW1teVxcRGVza3RvcFxcbm9yZC1jb2FzdFxcYmFja2VuZFxcc3JjXFxjb2xsZWN0aW9uc1xcTWVkaWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBDb2xsZWN0aW9uQ29uZmlnIH0gZnJvbSAncGF5bG9hZCdcblxuZXhwb3J0IGNvbnN0IE1lZGlhOiBDb2xsZWN0aW9uQ29uZmlnID0ge1xuICBzbHVnOiAnbWVkaWEnLFxuICBhY2Nlc3M6IHtcbiAgICByZWFkOiAoKSA9PiB0cnVlLFxuICB9LFxuICBmaWVsZHM6IFtcbiAgICB7XG4gICAgICBuYW1lOiAnYWx0JyxcbiAgICAgIHR5cGU6ICd0ZXh0JyxcbiAgICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIH0sXG4gIF0sXG4gIHVwbG9hZDogdHJ1ZSxcbn1cbiJdLCJuYW1lcyI6WyJNZWRpYSIsInNsdWciLCJhY2Nlc3MiLCJyZWFkIiwiZmllbGRzIiwibmFtZSIsInR5cGUiLCJyZXF1aXJlZCIsInVwbG9hZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Media.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Patients.ts":
/*!*************************************!*\
  !*** ./src/collections/Patients.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Patients: () => (/* binding */ Patients)\n/* harmony export */ });\nconst Patients = {\n    slug: 'patients',\n    admin: {\n        useAsTitle: 'fullName',\n        defaultColumns: [\n            'fullName',\n            'phone',\n            'email',\n            'userType',\n            'status'\n        ]\n    },\n    access: {\n        // Read: All authenticated users can read patient data\n        read: ({ req: { user } })=>{\n            if (!user) return false;\n            return true; // Admin, Doctor, Front-desk can all read patient records\n        },\n        // Create: Admin and Front-desk can create patients, Doctors cannot\n        create: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        },\n        // Update: Role-based update permissions\n        update: ({ req: { user } })=>{\n            if (!user) return false;\n            if (user.role === 'admin') return true; // Admin can update everything\n            if (user.role === 'front-desk') return true; // Front-desk can update (except medical notes - handled at field level)\n            if (user.role === 'doctor') return true; // Doctor can update (only medical notes - handled at field level)\n            return false;\n        },\n        // Delete: Only Admin and Front-desk can delete patients\n        delete: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        }\n    },\n    fields: [\n        // 用户分类字段\n        {\n            name: 'userType',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '咨询用户',\n                    value: 'consultation'\n                },\n                {\n                    label: '正式患者',\n                    value: 'patient'\n                }\n            ],\n            defaultValue: 'consultation',\n            label: '用户类型',\n            admin: {\n                description: '区分咨询用户和正式患者'\n            }\n        },\n        {\n            name: 'status',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '活跃',\n                    value: 'active'\n                },\n                {\n                    label: '非活跃',\n                    value: 'inactive'\n                },\n                {\n                    label: '已转换',\n                    value: 'converted'\n                }\n            ],\n            defaultValue: 'active',\n            label: '用户状态',\n            admin: {\n                description: '用户当前状态'\n            }\n        },\n        {\n            name: 'source',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '到店咨询',\n                    value: 'walk-in'\n                },\n                {\n                    label: '转介绍',\n                    value: 'referral'\n                },\n                {\n                    label: '在线咨询',\n                    value: 'online'\n                },\n                {\n                    label: '电话咨询',\n                    value: 'phone'\n                }\n            ],\n            defaultValue: 'walk-in',\n            label: '来源渠道',\n            admin: {\n                description: '用户来源追踪'\n            }\n        },\n        {\n            name: 'referredBy',\n            type: 'text',\n            label: '转介绍人',\n            admin: {\n                description: '如果是转介绍，记录转介绍人信息',\n                condition: (data)=>data.source === 'referral'\n            }\n        },\n        // 基本信息字段\n        {\n            name: 'fullName',\n            type: 'text',\n            required: true,\n            label: 'Full Name'\n        },\n        {\n            name: 'phone',\n            type: 'text',\n            required: true,\n            unique: true,\n            label: 'Phone Number'\n        },\n        {\n            name: 'email',\n            type: 'email',\n            label: 'Email Address'\n        },\n        {\n            name: 'photo',\n            type: 'upload',\n            relationTo: 'media',\n            label: 'Patient Photo'\n        },\n        // 时间戳字段\n        {\n            name: 'convertedAt',\n            type: 'date',\n            label: '转换时间',\n            admin: {\n                description: '从咨询用户转为正式患者的时间',\n                condition: (data)=>data.userType === 'patient' && data.status === 'converted',\n                date: {\n                    pickerAppearance: 'dayAndTime'\n                }\n            }\n        },\n        {\n            name: 'lastVisit',\n            type: 'date',\n            label: '最后就诊时间',\n            admin: {\n                description: '最后一次就诊的时间',\n                date: {\n                    pickerAppearance: 'dayAndTime'\n                }\n            }\n        },\n        // 患者专用信息（正式患者必填）\n        {\n            name: 'emergencyContact',\n            type: 'text',\n            label: '紧急联系人',\n            admin: {\n                description: '紧急联系人信息（正式患者建议填写）',\n                condition: (data)=>data.userType === 'patient'\n            }\n        },\n        {\n            name: 'allergies',\n            type: 'array',\n            label: '过敏史',\n            fields: [\n                {\n                    name: 'allergen',\n                    type: 'text',\n                    label: '过敏原',\n                    required: true\n                },\n                {\n                    name: 'severity',\n                    type: 'select',\n                    label: '严重程度',\n                    options: [\n                        {\n                            label: '轻微',\n                            value: 'mild'\n                        },\n                        {\n                            label: '中等',\n                            value: 'moderate'\n                        },\n                        {\n                            label: '严重',\n                            value: 'severe'\n                        }\n                    ],\n                    defaultValue: 'mild'\n                }\n            ],\n            admin: {\n                description: '患者过敏史记录（正式患者重要信息）',\n                condition: (data)=>data.userType === 'patient'\n            },\n            access: {\n                read: ({ req: { user } })=>{\n                    if (!user) return false;\n                    return user.role === 'admin' || user.role === 'doctor';\n                },\n                update: ({ req: { user } })=>{\n                    if (!user) return false;\n                    return user.role === 'admin' || user.role === 'doctor';\n                }\n            }\n        },\n        {\n            name: 'medicalHistory',\n            type: 'textarea',\n            label: '病史摘要',\n            admin: {\n                description: '患者主要病史摘要（正式患者重要信息）',\n                condition: (data)=>data.userType === 'patient'\n            },\n            access: {\n                read: ({ req: { user } })=>{\n                    if (!user) return false;\n                    return user.role === 'admin' || user.role === 'doctor';\n                },\n                update: ({ req: { user } })=>{\n                    if (!user) return false;\n                    return user.role === 'admin' || user.role === 'doctor';\n                }\n            }\n        },\n        {\n            name: 'medicalNotes',\n            type: 'richText',\n            label: 'Medical Notes',\n            access: {\n                // Medical notes can only be read/written by Admin and Doctor roles\n                read: ({ req: { user } })=>{\n                    if (!user) return false;\n                    return user.role === 'admin' || user.role === 'doctor';\n                },\n                update: ({ req: { user } })=>{\n                    if (!user) return false;\n                    return user.role === 'admin' || user.role === 'doctor';\n                }\n            },\n            admin: {\n                description: 'Confidential medical information - restricted to medical staff only'\n            }\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Patients.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Payments.ts":
/*!*************************************!*\
  !*** ./src/collections/Payments.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Payments: () => (/* binding */ Payments)\n/* harmony export */ });\nconst Payments = {\n    slug: 'payments',\n    admin: {\n        useAsTitle: 'paymentNumber',\n        defaultColumns: [\n            'paymentNumber',\n            'bill',\n            'patient',\n            'amount',\n            'paymentMethod',\n            'paymentStatus'\n        ],\n        listSearchableFields: [\n            'paymentNumber',\n            'bill.billNumber',\n            'patient.fullName'\n        ]\n    },\n    access: {\n        // Read: Same as Bills - Admin and Front-desk see all, Doctors see only related payments\n        read: ({ req: { user } })=>{\n            if (!user) return false;\n            if (user.role === 'admin' || user.role === 'front-desk') {\n                return true;\n            }\n            if (user.role === 'doctor') {\n                return {\n                    or: [\n                        {\n                            'bill.appointment.practitioner': {\n                                equals: user.id\n                            }\n                        },\n                        {\n                            receivedBy: {\n                                equals: user.id\n                            }\n                        }\n                    ]\n                };\n            }\n            return false;\n        },\n        // Create: Admin and Front-desk can create payments\n        create: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        },\n        // Update: Admin and Front-desk can update payments\n        update: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin' || user.role === 'front-desk';\n        },\n        // Delete: Only Admin can delete payments\n        delete: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin';\n        }\n    },\n    hooks: {\n        beforeChange: [\n            ({ data })=>{\n                // Auto-generate payment number if not provided\n                if (!data.paymentNumber) {\n                    const now = new Date();\n                    const year = now.getFullYear();\n                    const month = String(now.getMonth() + 1).padStart(2, '0');\n                    const day = String(now.getDate()).padStart(2, '0');\n                    const timestamp = now.getTime().toString().slice(-6);\n                    data.paymentNumber = `PAY-${year}${month}${day}-${timestamp}`;\n                }\n                // Auto-generate receipt number if not provided and payment is completed\n                if (!data.receiptNumber && data.paymentStatus === 'completed') {\n                    const now = new Date();\n                    const year = now.getFullYear();\n                    const month = String(now.getMonth() + 1).padStart(2, '0');\n                    const day = String(now.getDate()).padStart(2, '0');\n                    const timestamp = now.getTime().toString().slice(-6);\n                    data.receiptNumber = `REC-${year}${month}${day}-${timestamp}`;\n                }\n                return data;\n            }\n        ]\n    },\n    fields: [\n        {\n            name: 'paymentNumber',\n            type: 'text',\n            required: true,\n            unique: true,\n            label: '支付编号',\n            admin: {\n                description: '系统自动生成，格式：PAY-YYYYMMDD-XXXXXX',\n                readOnly: true\n            }\n        },\n        // 关联信息\n        {\n            name: 'bill',\n            type: 'relationship',\n            relationTo: 'bills',\n            required: true,\n            hasMany: false,\n            label: '关联账单'\n        },\n        {\n            name: 'patient',\n            type: 'relationship',\n            relationTo: 'patients',\n            required: true,\n            hasMany: false,\n            label: '患者'\n        },\n        // 支付信息\n        {\n            name: 'amount',\n            type: 'number',\n            required: true,\n            label: '支付金额',\n            min: 0.01,\n            admin: {\n                description: '本次支付的金额'\n            }\n        },\n        {\n            name: 'paymentMethod',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '现金',\n                    value: 'cash'\n                },\n                {\n                    label: '银行卡',\n                    value: 'card'\n                },\n                {\n                    label: '微信支付',\n                    value: 'wechat'\n                },\n                {\n                    label: '支付宝',\n                    value: 'alipay'\n                },\n                {\n                    label: '银行转账',\n                    value: 'transfer'\n                },\n                {\n                    label: '分期付款',\n                    value: 'installment'\n                }\n            ],\n            label: '支付方式'\n        },\n        {\n            name: 'paymentStatus',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '待处理',\n                    value: 'pending'\n                },\n                {\n                    label: '已完成',\n                    value: 'completed'\n                },\n                {\n                    label: '失败',\n                    value: 'failed'\n                },\n                {\n                    label: '已退款',\n                    value: 'refunded'\n                }\n            ],\n            defaultValue: 'pending',\n            label: '支付状态'\n        },\n        // 支付详情\n        {\n            name: 'transactionId',\n            type: 'text',\n            label: '交易ID',\n            admin: {\n                description: '第三方支付平台的交易ID（如适用）'\n            }\n        },\n        {\n            name: 'paymentDate',\n            type: 'date',\n            required: true,\n            label: '支付日期',\n            defaultValue: ()=>new Date().toISOString(),\n            admin: {\n                date: {\n                    pickerAppearance: 'dayAndTime'\n                }\n            }\n        },\n        {\n            name: 'receivedBy',\n            type: 'relationship',\n            relationTo: 'users',\n            required: true,\n            hasMany: false,\n            label: '收款人员',\n            admin: {\n                description: '处理此次支付的工作人员'\n            }\n        },\n        // 备注信息\n        {\n            name: 'notes',\n            type: 'textarea',\n            label: '支付备注',\n            admin: {\n                description: '支付相关的备注信息'\n            }\n        },\n        {\n            name: 'receiptNumber',\n            type: 'text',\n            label: '收据编号',\n            admin: {\n                description: '收据编号（系统自动生成）',\n                readOnly: true\n            }\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Payments.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Treatments.ts":
/*!***************************************!*\
  !*** ./src/collections/Treatments.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Treatments: () => (/* binding */ Treatments)\n/* harmony export */ });\nconst Treatments = {\n    slug: 'treatments',\n    admin: {\n        useAsTitle: 'name'\n    },\n    access: {\n        // Read: All authenticated users can read treatments\n        read: ({ req: { user } })=>{\n            if (!user) return false;\n            return true; // All roles need to see treatments for appointment scheduling\n        },\n        // Create: Only Admin can create new treatments\n        create: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin';\n        },\n        // Update: Only Admin can update treatment definitions\n        update: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin';\n        },\n        // Delete: Only Admin can delete treatments\n        delete: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin';\n        }\n    },\n    fields: [\n        {\n            name: 'name',\n            type: 'text',\n            required: true,\n            unique: true,\n            label: 'Treatment Name'\n        },\n        {\n            name: 'description',\n            type: 'richText',\n            label: 'Description'\n        },\n        {\n            name: 'defaultPrice',\n            type: 'number',\n            required: true,\n            label: 'Default Price',\n            min: 0\n        },\n        {\n            name: 'defaultDurationInMinutes',\n            type: 'number',\n            required: true,\n            label: 'Default Duration (minutes)',\n            min: 1\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Treatments.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Users.ts":
/*!**********************************!*\
  !*** ./src/collections/Users.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Users: () => (/* binding */ Users)\n/* harmony export */ });\nconst Users = {\n    slug: 'users',\n    admin: {\n        useAsTitle: 'email',\n        defaultColumns: [\n            'email',\n            'role',\n            'clerkId'\n        ]\n    },\n    auth: {\n        // Disable password auth since we're using Clerk\n        disableLocalStrategy: true\n    },\n    access: {\n        // Read: All authenticated users can read user info (needed for appointment assignment)\n        read: ({ req: { user } })=>{\n            if (!user) return false;\n            return true; // All roles need to see staff for appointment coordination\n        },\n        // Create: Only Admin can create new user accounts\n        create: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin';\n        },\n        // Update: Only Admin can update user accounts\n        update: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin';\n        },\n        // Delete: Only Admin can delete user accounts\n        delete: ({ req: { user } })=>{\n            if (!user) return false;\n            return user.role === 'admin';\n        }\n    },\n    fields: [\n        // Email added by default\n        {\n            name: 'role',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: 'Admin',\n                    value: 'admin'\n                },\n                {\n                    label: 'Front-desk',\n                    value: 'front-desk'\n                },\n                {\n                    label: 'Doctor',\n                    value: 'doctor'\n                }\n            ],\n            defaultValue: 'front-desk',\n            access: {\n                // Only Admin can update user roles\n                update: ({ req: { user } })=>{\n                    if (!user) return false;\n                    return user.role === 'admin';\n                }\n            },\n            admin: {\n                description: 'User role determines access permissions - only Admin can modify'\n            }\n        },\n        {\n            name: 'clerkId',\n            type: 'text',\n            required: true,\n            unique: true,\n            label: 'Clerk User ID',\n            admin: {\n                description: 'Unique identifier from Clerk authentication service',\n                readOnly: true\n            }\n        },\n        {\n            name: 'firstName',\n            type: 'text',\n            label: 'First Name'\n        },\n        {\n            name: 'lastName',\n            type: 'text',\n            label: 'Last Name'\n        },\n        {\n            name: 'lastLogin',\n            type: 'date',\n            label: 'Last Login',\n            admin: {\n                readOnly: true,\n                description: 'Automatically updated when user logs in'\n            }\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Users.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-sync.ts":
/*!******************************!*\
  !*** ./src/lib/auth-sync.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPayloadUserFromClerk: () => (/* binding */ getPayloadUserFromClerk),\n/* harmony export */   syncClerkUserWithPayload: () => (/* binding */ syncClerkUserWithPayload),\n/* harmony export */   validateClerkUserAccess: () => (/* binding */ validateClerkUserAccess)\n/* harmony export */ });\n// Authentication synchronization between Clerk and Payload CMS\n/**\n * Sync Clerk user with Payload CMS user\n * Creates or updates user in Payload CMS based on Clerk user data\n */ async function syncClerkUserWithPayload(payload, clerkUser, defaultRole = 'front-desk') {\n    try {\n        // Check if user already exists in Payload CMS\n        const existingUsers = await payload.find({\n            collection: 'users',\n            where: {\n                clerkId: {\n                    equals: clerkUser.id\n                }\n            },\n            limit: 1\n        });\n        if (existingUsers.docs.length > 0) {\n            // Update existing user\n            const existingUser = existingUsers.docs[0];\n            const updatedUser = await payload.update({\n                collection: 'users',\n                id: existingUser.id,\n                data: {\n                }\n            });\n            return updatedUser;\n        } else {\n            // Create new user with default role\n            const newUser = await payload.create({\n                collection: 'users',\n                data: {\n                    role: defaultRole,\n                    clerkId: clerkUser.id\n                }\n            });\n            return newUser;\n        }\n    } catch (error) {\n        console.error('Error syncing Clerk user with Payload:', error);\n        throw new Error('Failed to sync user authentication');\n    }\n}\n/**\n * Get or create Payload user from Clerk user\n * This is the main function to call from API routes\n */ async function getPayloadUserFromClerk(payload, clerkUserId, clerkEmail) {\n    const clerkUser = {\n        id: clerkUserId,\n        email: clerkEmail\n    };\n    return await syncClerkUserWithPayload(payload, clerkUser);\n}\n/**\n * Validate that a Clerk user has access to Payload CMS\n * Returns the corresponding Payload user if valid\n */ async function validateClerkUserAccess(payload, clerkUserId, clerkEmail) {\n    try {\n        const payloadUser = await getPayloadUserFromClerk(payload, clerkUserId, clerkEmail);\n        return payloadUser;\n    } catch (error) {\n        console.error('Error validating Clerk user access:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-sync.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/payload-auth-middleware.ts":
/*!********************************************!*\
  !*** ./src/lib/payload-auth-middleware.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateWithPayload: () => (/* binding */ authenticateWithPayload),\n/* harmony export */   makeAuthenticatedPayloadRequest: () => (/* binding */ makeAuthenticatedPayloadRequest)\n/* harmony export */ });\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var _payload_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../payload.config */ \"(rsc)/./src/payload.config.ts\");\n/* harmony import */ var _auth_sync__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./auth-sync */ \"(rsc)/./src/lib/auth-sync.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([payload__WEBPACK_IMPORTED_MODULE_0__, _payload_config__WEBPACK_IMPORTED_MODULE_1__]);\n([payload__WEBPACK_IMPORTED_MODULE_0__, _payload_config__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n/**\n * Middleware to authenticate Clerk users with Payload CMS\n * Extracts Clerk user info from headers and syncs with Payload\n */ async function authenticateWithPayload(request) {\n    try {\n        // Extract Clerk user information from headers\n        const clerkUserId = request.headers.get('x-clerk-user-id');\n        const userEmail = request.headers.get('x-user-email');\n        if (!clerkUserId || !userEmail) {\n            console.log('Missing Clerk user headers');\n            return null;\n        }\n        // Get Payload instance\n        const payload = await (0,payload__WEBPACK_IMPORTED_MODULE_0__.getPayload)({\n            config: _payload_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n        });\n        // Sync Clerk user with Payload CMS\n        const clerkUser = {\n            id: clerkUserId,\n            email: userEmail\n        };\n        console.log('Syncing Clerk user with Payload:', clerkUser);\n        const payloadUser = await (0,_auth_sync__WEBPACK_IMPORTED_MODULE_2__.syncClerkUserWithPayload)(payload, clerkUser);\n        console.log('Synced Payload user:', payloadUser);\n        return {\n            payload,\n            user: payloadUser\n        };\n    } catch (error) {\n        console.error('Payload authentication error:', error);\n        return null;\n    }\n}\nasync function makeAuthenticatedPayloadRequest(context, collection, operation, options = {}) {\n    const { payload, user } = context;\n    switch(operation){\n        case 'find':\n            return await payload.find({\n                collection,\n                user,\n                ...options\n            });\n        case 'findByID':\n            return await payload.findByID({\n                collection,\n                user,\n                ...options\n            });\n        case 'create':\n            return await payload.create({\n                collection,\n                user,\n                ...options\n            });\n        case 'update':\n            return await payload.update({\n                collection,\n                user,\n                ...options\n            });\n        case 'delete':\n            return await payload.delete({\n                collection,\n                user,\n                ...options\n            });\n        default:\n            throw new Error(`Unsupported operation: ${operation}`);\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/payload-auth-middleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/payload.config.ts":
/*!*******************************!*\
  !*** ./src/payload.config.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _payloadcms_db_postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @payloadcms/db-postgres */ \"@payloadcms/db-postgres\");\n/* harmony import */ var _payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @payloadcms/payload-cloud */ \"@payloadcms/payload-cloud\");\n/* harmony import */ var _payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @payloadcms/richtext-lexical */ \"(rsc)/./node_modules/.pnpm/@payloadcms+richtext-lexica_54b29c2efb9191719ff3facfcb2ce55c/node_modules/@payloadcms/richtext-lexical/dist/index.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! url */ \"url\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(url__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sharp */ \"sharp\");\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(sharp__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _collections_Users__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./collections/Users */ \"(rsc)/./src/collections/Users.ts\");\n/* harmony import */ var _collections_Media__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./collections/Media */ \"(rsc)/./src/collections/Media.ts\");\n/* harmony import */ var _collections_Treatments__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./collections/Treatments */ \"(rsc)/./src/collections/Treatments.ts\");\n/* harmony import */ var _collections_Patients__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./collections/Patients */ \"(rsc)/./src/collections/Patients.ts\");\n/* harmony import */ var _collections_Appointments__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./collections/Appointments */ \"(rsc)/./src/collections/Appointments.ts\");\n/* harmony import */ var _collections_Bills__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./collections/Bills */ \"(rsc)/./src/collections/Bills.ts\");\n/* harmony import */ var _collections_BillItems__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./collections/BillItems */ \"(rsc)/./src/collections/BillItems.ts\");\n/* harmony import */ var _collections_Payments__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./collections/Payments */ \"(rsc)/./src/collections/Payments.ts\");\n/* harmony import */ var _collections_Deposits__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./collections/Deposits */ \"(rsc)/./src/collections/Deposits.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_payloadcms_db_postgres__WEBPACK_IMPORTED_MODULE_0__, _payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__, payload__WEBPACK_IMPORTED_MODULE_3__, _payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_15__]);\n([_payloadcms_db_postgres__WEBPACK_IMPORTED_MODULE_0__, _payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__, payload__WEBPACK_IMPORTED_MODULE_3__, _payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_15__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// storage-adapter-import-placeholder\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst filename = (0,url__WEBPACK_IMPORTED_MODULE_4__.fileURLToPath)(\"file:///C:/Users/<USER>/Desktop/nord-coast/backend/src/payload.config.ts\");\nconst dirname = path__WEBPACK_IMPORTED_MODULE_2___default().dirname(filename);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,payload__WEBPACK_IMPORTED_MODULE_3__.buildConfig)({\n    admin: {\n        user: _collections_Users__WEBPACK_IMPORTED_MODULE_6__.Users.slug,\n        importMap: {\n            baseDir: path__WEBPACK_IMPORTED_MODULE_2___default().resolve(dirname)\n        }\n    },\n    collections: [\n        _collections_Users__WEBPACK_IMPORTED_MODULE_6__.Users,\n        _collections_Media__WEBPACK_IMPORTED_MODULE_7__.Media,\n        _collections_Treatments__WEBPACK_IMPORTED_MODULE_8__.Treatments,\n        _collections_Patients__WEBPACK_IMPORTED_MODULE_9__.Patients,\n        _collections_Appointments__WEBPACK_IMPORTED_MODULE_10__.Appointments,\n        _collections_Bills__WEBPACK_IMPORTED_MODULE_11__.Bills,\n        _collections_BillItems__WEBPACK_IMPORTED_MODULE_12__.BillItems,\n        _collections_Payments__WEBPACK_IMPORTED_MODULE_13__.Payments,\n        _collections_Deposits__WEBPACK_IMPORTED_MODULE_14__.Deposits\n    ],\n    editor: (0,_payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_15__.lexicalEditor)(),\n    secret: process.env.PAYLOAD_SECRET || '',\n    typescript: {\n        outputFile: path__WEBPACK_IMPORTED_MODULE_2___default().resolve(dirname, 'payload-types.ts')\n    },\n    db: (0,_payloadcms_db_postgres__WEBPACK_IMPORTED_MODULE_0__.postgresAdapter)({\n        pool: {\n            connectionString: process.env.DATABASE_URI || ''\n        }\n    }),\n    sharp: (sharp__WEBPACK_IMPORTED_MODULE_5___default()),\n    plugins: [\n        (0,_payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__.payloadCloudPlugin)()\n    ]\n}));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/payload.config.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@payloadcms/db-postgres":
/*!******************************************!*\
  !*** external "@payloadcms/db-postgres" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@payloadcms/db-postgres");;

/***/ }),

/***/ "@payloadcms/payload-cloud":
/*!********************************************!*\
  !*** external "@payloadcms/payload-cloud" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@payloadcms/payload-cloud");;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "payload":
/*!**************************!*\
  !*** external "payload" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("payload");;

/***/ }),

/***/ "payload/shared":
/*!*********************************!*\
  !*** external "payload/shared" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("payload/shared");;

/***/ }),

/***/ "sharp":
/*!************************!*\
  !*** external "sharp" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("sharp");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10","vendor-chunks/lexical@0.28.0","vendor-chunks/@payloadcms+richtext-lexica_54b29c2efb9191719ff3facfcb2ce55c","vendor-chunks/@lexical+table@0.28.0","vendor-chunks/jsox@1.2.121","vendor-chunks/@lexical+list@0.28.0","vendor-chunks/@lexical+utils@0.28.0","vendor-chunks/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362","vendor-chunks/@lexical+selection@0.28.0","vendor-chunks/@lexical+rich-text@0.28.0","vendor-chunks/qs-esm@7.0.2","vendor-chunks/@lexical+clipboard@0.28.0","vendor-chunks/@lexical+html@0.28.0","vendor-chunks/bson-objectid@2.0.4","vendor-chunks/uuid@10.0.0","vendor-chunks/escape-html@1.0.3","vendor-chunks/@lexical+react@0.28.0_react_20b46741c70d48d804512ab564db69c2","vendor-chunks/@lexical+headless@0.28.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbills%2Froute&page=%2Fapi%2Fbills%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbills%2Froute.ts&appDir=C%3A%5CUsers%5Cjimmy%5CDesktop%5Cnord-coast%5Cbackend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjimmy%5CDesktop%5Cnord-coast%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();