"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+html@0.28.0";
exports.ids = ["vendor-chunks/@lexical+html@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+html@0.28.0/node_modules/@lexical/html/LexicalHtml.dev.mjs":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+html@0.28.0/node_modules/@lexical/html/LexicalHtml.dev.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $generateHtmlFromNodes: () => (/* binding */ $generateHtmlFromNodes),\n/* harmony export */   $generateNodesFromDOM: () => (/* binding */ $generateNodesFromDOM)\n/* harmony export */ });\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/selection */ \"(rsc)/./node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n/**\n * How you parse your html string to get a document is left up to you. In the browser you can use the native\n * DOMParser API to generate a document (see clipboard.ts), but to use in a headless environment you can use JSDom\n * or an equivalent library and pass in the document here.\n */\nfunction $generateNodesFromDOM(editor, dom) {\n  const elements = dom.body ? dom.body.childNodes : [];\n  let lexicalNodes = [];\n  const allArtificialNodes = [];\n  for (let i = 0; i < elements.length; i++) {\n    const element = elements[i];\n    if (!IGNORE_TAGS.has(element.nodeName)) {\n      const lexicalNode = $createNodesFromDOM(element, editor, allArtificialNodes, false);\n      if (lexicalNode !== null) {\n        lexicalNodes = lexicalNodes.concat(lexicalNode);\n      }\n    }\n  }\n  $unwrapArtificalNodes(allArtificialNodes);\n  return lexicalNodes;\n}\nfunction $generateHtmlFromNodes(editor, selection) {\n  if (typeof document === 'undefined' || typeof window === 'undefined' && typeof global.window === 'undefined') {\n    throw new Error('To use $generateHtmlFromNodes in headless mode please initialize a headless browser implementation such as JSDom before calling this function.');\n  }\n  const container = document.createElement('div');\n  const root = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)();\n  const topLevelChildren = root.getChildren();\n  for (let i = 0; i < topLevelChildren.length; i++) {\n    const topLevelNode = topLevelChildren[i];\n    $appendNodesToHTML(editor, topLevelNode, container, selection);\n  }\n  return container.innerHTML;\n}\nfunction $appendNodesToHTML(editor, currentNode, parentElement, selection = null) {\n  let shouldInclude = selection !== null ? currentNode.isSelected(selection) : true;\n  const shouldExclude = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode) && currentNode.excludeFromCopy('html');\n  let target = currentNode;\n  if (selection !== null) {\n    let clone = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$cloneWithProperties)(currentNode);\n    clone = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(clone) && selection !== null ? (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_1__.$sliceSelectedTextNodeContent)(selection, clone) : clone;\n    target = clone;\n  }\n  const children = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(target) ? target.getChildren() : [];\n  const registeredNode = editor._nodes.get(target.getType());\n  let exportOutput;\n\n  // Use HTMLConfig overrides, if available.\n  if (registeredNode && registeredNode.exportDOM !== undefined) {\n    exportOutput = registeredNode.exportDOM(editor, target);\n  } else {\n    exportOutput = target.exportDOM(editor);\n  }\n  const {\n    element,\n    after\n  } = exportOutput;\n  if (!element) {\n    return false;\n  }\n  const fragment = document.createDocumentFragment();\n  for (let i = 0; i < children.length; i++) {\n    const childNode = children[i];\n    const shouldIncludeChild = $appendNodesToHTML(editor, childNode, fragment, selection);\n    if (!shouldInclude && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode) && shouldIncludeChild && currentNode.extractWithChild(childNode, selection, 'html')) {\n      shouldInclude = true;\n    }\n  }\n  if (shouldInclude && !shouldExclude) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDocumentFragment)(element)) {\n      element.append(fragment);\n    }\n    parentElement.append(element);\n    if (after) {\n      const newElement = after.call(target, element);\n      if (newElement) {\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDocumentFragment)(element)) {\n          element.replaceChildren(newElement);\n        } else {\n          element.replaceWith(newElement);\n        }\n      }\n    }\n  } else {\n    parentElement.append(fragment);\n  }\n  return shouldInclude;\n}\nfunction getConversionFunction(domNode, editor) {\n  const {\n    nodeName\n  } = domNode;\n  const cachedConversions = editor._htmlConversions.get(nodeName.toLowerCase());\n  let currentConversion = null;\n  if (cachedConversions !== undefined) {\n    for (const cachedConversion of cachedConversions) {\n      const domConversion = cachedConversion(domNode);\n      if (domConversion !== null && (currentConversion === null ||\n      // Given equal priority, prefer the last registered importer\n      // which is typically an application custom node or HTMLConfig['import']\n      (currentConversion.priority || 0) <= (domConversion.priority || 0))) {\n        currentConversion = domConversion;\n      }\n    }\n  }\n  return currentConversion !== null ? currentConversion.conversion : null;\n}\nconst IGNORE_TAGS = new Set(['STYLE', 'SCRIPT']);\nfunction $createNodesFromDOM(node, editor, allArtificialNodes, hasBlockAncestorLexicalNode, forChildMap = new Map(), parentLexicalNode) {\n  let lexicalNodes = [];\n  if (IGNORE_TAGS.has(node.nodeName)) {\n    return lexicalNodes;\n  }\n  let currentLexicalNode = null;\n  const transformFunction = getConversionFunction(node, editor);\n  const transformOutput = transformFunction ? transformFunction(node) : null;\n  let postTransform = null;\n  if (transformOutput !== null) {\n    postTransform = transformOutput.after;\n    const transformNodes = transformOutput.node;\n    currentLexicalNode = Array.isArray(transformNodes) ? transformNodes[transformNodes.length - 1] : transformNodes;\n    if (currentLexicalNode !== null) {\n      for (const [, forChildFunction] of forChildMap) {\n        currentLexicalNode = forChildFunction(currentLexicalNode, parentLexicalNode);\n        if (!currentLexicalNode) {\n          break;\n        }\n      }\n      if (currentLexicalNode) {\n        lexicalNodes.push(...(Array.isArray(transformNodes) ? transformNodes : [currentLexicalNode]));\n      }\n    }\n    if (transformOutput.forChild != null) {\n      forChildMap.set(node.nodeName, transformOutput.forChild);\n    }\n  }\n\n  // If the DOM node doesn't have a transformer, we don't know what\n  // to do with it but we still need to process any childNodes.\n  const children = node.childNodes;\n  let childLexicalNodes = [];\n  const hasBlockAncestorLexicalNodeForChildren = currentLexicalNode != null && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(currentLexicalNode) ? false : currentLexicalNode != null && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isBlockElementNode)(currentLexicalNode) || hasBlockAncestorLexicalNode;\n  for (let i = 0; i < children.length; i++) {\n    childLexicalNodes.push(...$createNodesFromDOM(children[i], editor, allArtificialNodes, hasBlockAncestorLexicalNodeForChildren, new Map(forChildMap), currentLexicalNode));\n  }\n  if (postTransform != null) {\n    childLexicalNodes = postTransform(childLexicalNodes);\n  }\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isBlockDomNode)(node)) {\n    if (!hasBlockAncestorLexicalNodeForChildren) {\n      childLexicalNodes = wrapContinuousInlines(node, childLexicalNodes, lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode);\n    } else {\n      childLexicalNodes = wrapContinuousInlines(node, childLexicalNodes, () => {\n        const artificialNode = new lexical__WEBPACK_IMPORTED_MODULE_0__.ArtificialNode__DO_NOT_USE();\n        allArtificialNodes.push(artificialNode);\n        return artificialNode;\n      });\n    }\n  }\n  if (currentLexicalNode == null) {\n    if (childLexicalNodes.length > 0) {\n      // If it hasn't been converted to a LexicalNode, we hoist its children\n      // up to the same level as it.\n      lexicalNodes = lexicalNodes.concat(childLexicalNodes);\n    } else {\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isBlockDomNode)(node) && isDomNodeBetweenTwoInlineNodes(node)) {\n        // Empty block dom node that hasnt been converted, we replace it with a linebreak if its between inline nodes\n        lexicalNodes = lexicalNodes.concat((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createLineBreakNode)());\n      }\n    }\n  } else {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentLexicalNode)) {\n      // If the current node is a ElementNode after conversion,\n      // we can append all the children to it.\n      currentLexicalNode.append(...childLexicalNodes);\n    }\n  }\n  return lexicalNodes;\n}\nfunction wrapContinuousInlines(domNode, nodes, createWrapperFn) {\n  const textAlign = domNode.style.textAlign;\n  const out = [];\n  let continuousInlines = [];\n  // wrap contiguous inline child nodes in para\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isBlockElementNode)(node)) {\n      if (textAlign && !node.getFormat()) {\n        node.setFormat(textAlign);\n      }\n      out.push(node);\n    } else {\n      continuousInlines.push(node);\n      if (i === nodes.length - 1 || i < nodes.length - 1 && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isBlockElementNode)(nodes[i + 1])) {\n        const wrapper = createWrapperFn();\n        wrapper.setFormat(textAlign);\n        wrapper.append(...continuousInlines);\n        out.push(wrapper);\n        continuousInlines = [];\n      }\n    }\n  }\n  return out;\n}\nfunction $unwrapArtificalNodes(allArtificialNodes) {\n  for (const node of allArtificialNodes) {\n    if (node.getNextSibling() instanceof lexical__WEBPACK_IMPORTED_MODULE_0__.ArtificialNode__DO_NOT_USE) {\n      node.insertAfter((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createLineBreakNode)());\n    }\n  }\n  // Replace artificial node with it's children\n  for (const node of allArtificialNodes) {\n    const children = node.getChildren();\n    for (const child of children) {\n      node.insertBefore(child);\n    }\n    node.remove();\n  }\n}\nfunction isDomNodeBetweenTwoInlineNodes(node) {\n  if (node.nextSibling == null || node.previousSibling == null) {\n    return false;\n  }\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isInlineDomNode)(node.nextSibling) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isInlineDomNode)(node.previousSibling);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+html@0.28.0/node_modules/@lexical/html/LexicalHtml.dev.mjs\n");

/***/ })

};
;