"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+rich-text@0.28.0";
exports.ids = ["vendor-chunks/@lexical+rich-text@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $createHeadingNode: () => (/* binding */ $createHeadingNode),\n/* harmony export */   $createQuoteNode: () => (/* binding */ $createQuoteNode),\n/* harmony export */   $isHeadingNode: () => (/* binding */ $isHeadingNode),\n/* harmony export */   $isQuoteNode: () => (/* binding */ $isQuoteNode),\n/* harmony export */   DRAG_DROP_PASTE: () => (/* binding */ DRAG_DROP_PASTE),\n/* harmony export */   HeadingNode: () => (/* binding */ HeadingNode),\n/* harmony export */   QuoteNode: () => (/* binding */ QuoteNode),\n/* harmony export */   eventFiles: () => (/* binding */ eventFiles),\n/* harmony export */   registerRichText: () => (/* binding */ registerRichText)\n/* harmony export */ });\n/* harmony import */ var _lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/clipboard */ \"(rsc)/./node_modules/.pnpm/@lexical+clipboard@0.28.0/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs\");\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/selection */ \"(rsc)/./node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(rsc)/./node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction caretFromPoint(x, y) {\n  if (typeof document.caretRangeFromPoint !== 'undefined') {\n    const range = document.caretRangeFromPoint(x, y);\n    if (range === null) {\n      return null;\n    }\n    return {\n      node: range.startContainer,\n      offset: range.startOffset\n    };\n    // @ts-ignore\n  } else if (document.caretPositionFromPoint !== 'undefined') {\n    // @ts-ignore FF - no types\n    const range = document.caretPositionFromPoint(x, y);\n    if (range === null) {\n      return null;\n    }\n    return {\n      node: range.offsetNode,\n      offset: range.offset\n    };\n  } else {\n    // Gracefully handle IE\n    return null;\n  }\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst documentMode = CAN_USE_DOM && 'documentMode' in document ? document.documentMode : null;\nconst CAN_USE_BEFORE_INPUT = CAN_USE_DOM && 'InputEvent' in window && !documentMode ? 'getTargetRanges' in new window.InputEvent('input') : false;\nconst IS_SAFARI = CAN_USE_DOM && /Version\\/[\\d.]+.*Safari/.test(navigator.userAgent);\nconst IS_IOS = CAN_USE_DOM && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;\n\n// Keep these in case we need to use them in the future.\n// export const IS_WINDOWS: boolean = CAN_USE_DOM && /Win/.test(navigator.platform);\nconst IS_CHROME = CAN_USE_DOM && /^(?=.*Chrome).*/i.test(navigator.userAgent);\nconst IS_APPLE_WEBKIT = CAN_USE_DOM && /AppleWebKit\\/[\\d.]+/.test(navigator.userAgent) && !IS_CHROME;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst DRAG_DROP_PASTE = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('DRAG_DROP_PASTE_FILE');\n/** @noInheritDoc */\nclass QuoteNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  static getType() {\n    return 'quote';\n  }\n  static clone(node) {\n    return new QuoteNode(node.__key);\n  }\n\n  // View\n\n  createDOM(config) {\n    const element = document.createElement('blockquote');\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, config.theme.quote);\n    return element;\n  }\n  updateDOM(prevNode, dom) {\n    return false;\n  }\n  static importDOM() {\n    return {\n      blockquote: node => ({\n        conversion: $convertBlockquoteElement,\n        priority: 0\n      })\n    };\n  }\n  exportDOM(editor) {\n    const {\n      element\n    } = super.exportDOM(editor);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n      if (this.isEmpty()) {\n        element.append(document.createElement('br'));\n      }\n      const formatType = this.getFormatType();\n      element.style.textAlign = formatType;\n      const direction = this.getDirection();\n      if (direction) {\n        element.dir = direction;\n      }\n    }\n    return {\n      element\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createQuoteNode().updateFromJSON(serializedNode);\n  }\n\n  // Mutation\n\n  insertNewAfter(_, restoreSelection) {\n    const newBlock = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    const direction = this.getDirection();\n    newBlock.setDirection(direction);\n    this.insertAfter(newBlock, restoreSelection);\n    return newBlock;\n  }\n  collapseAtStart() {\n    const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    const children = this.getChildren();\n    children.forEach(child => paragraph.append(child));\n    this.replace(paragraph);\n    return true;\n  }\n  canMergeWhenEmpty() {\n    return true;\n  }\n}\nfunction $createQuoteNode() {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new QuoteNode());\n}\nfunction $isQuoteNode(node) {\n  return node instanceof QuoteNode;\n}\n/** @noInheritDoc */\nclass HeadingNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  static getType() {\n    return 'heading';\n  }\n  static clone(node) {\n    return new HeadingNode(node.__tag, node.__key);\n  }\n  constructor(tag, key) {\n    super(key);\n    this.__tag = tag;\n  }\n  getTag() {\n    return this.__tag;\n  }\n  setTag(tag) {\n    const self = this.getWritable();\n    this.__tag = tag;\n    return self;\n  }\n\n  // View\n\n  createDOM(config) {\n    const tag = this.__tag;\n    const element = document.createElement(tag);\n    const theme = config.theme;\n    const classNames = theme.heading;\n    if (classNames !== undefined) {\n      const className = classNames[tag];\n      (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, className);\n    }\n    return element;\n  }\n  updateDOM(prevNode, dom, config) {\n    return prevNode.__tag !== this.__tag;\n  }\n  static importDOM() {\n    return {\n      h1: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h2: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h3: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h4: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h5: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h6: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      p: node => {\n        // domNode is a <p> since we matched it by nodeName\n        const paragraph = node;\n        const firstChild = paragraph.firstChild;\n        if (firstChild !== null && isGoogleDocsTitle(firstChild)) {\n          return {\n            conversion: () => ({\n              node: null\n            }),\n            priority: 3\n          };\n        }\n        return null;\n      },\n      span: node => {\n        if (isGoogleDocsTitle(node)) {\n          return {\n            conversion: domNode => {\n              return {\n                node: $createHeadingNode('h1')\n              };\n            },\n            priority: 3\n          };\n        }\n        return null;\n      }\n    };\n  }\n  exportDOM(editor) {\n    const {\n      element\n    } = super.exportDOM(editor);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n      if (this.isEmpty()) {\n        element.append(document.createElement('br'));\n      }\n      const formatType = this.getFormatType();\n      element.style.textAlign = formatType;\n      const direction = this.getDirection();\n      if (direction) {\n        element.dir = direction;\n      }\n    }\n    return {\n      element\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createHeadingNode(serializedNode.tag).updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setTag(serializedNode.tag);\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      tag: this.getTag()\n    };\n  }\n\n  // Mutation\n  insertNewAfter(selection, restoreSelection = true) {\n    const anchorOffet = selection ? selection.anchor.offset : 0;\n    const lastDesc = this.getLastDescendant();\n    const isAtEnd = !lastDesc || selection && selection.anchor.key === lastDesc.getKey() && anchorOffet === lastDesc.getTextContentSize();\n    const newElement = isAtEnd || !selection ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)() : $createHeadingNode(this.getTag());\n    const direction = this.getDirection();\n    newElement.setDirection(direction);\n    this.insertAfter(newElement, restoreSelection);\n    if (anchorOffet === 0 && !this.isEmpty() && selection) {\n      const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n      paragraph.select();\n      this.replace(paragraph, true);\n    }\n    return newElement;\n  }\n  collapseAtStart() {\n    const newElement = !this.isEmpty() ? $createHeadingNode(this.getTag()) : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    const children = this.getChildren();\n    children.forEach(child => newElement.append(child));\n    this.replace(newElement);\n    return true;\n  }\n  extractWithChild() {\n    return true;\n  }\n}\nfunction isGoogleDocsTitle(domNode) {\n  if (domNode.nodeName.toLowerCase() === 'span') {\n    return domNode.style.fontSize === '26pt';\n  }\n  return false;\n}\nfunction $convertHeadingElement(element) {\n  const nodeName = element.nodeName.toLowerCase();\n  let node = null;\n  if (nodeName === 'h1' || nodeName === 'h2' || nodeName === 'h3' || nodeName === 'h4' || nodeName === 'h5' || nodeName === 'h6') {\n    node = $createHeadingNode(nodeName);\n    if (element.style !== null) {\n      (0,lexical__WEBPACK_IMPORTED_MODULE_0__.setNodeIndentFromDOM)(element, node);\n      node.setFormat(element.style.textAlign);\n    }\n  }\n  return {\n    node\n  };\n}\nfunction $convertBlockquoteElement(element) {\n  const node = $createQuoteNode();\n  if (element.style !== null) {\n    node.setFormat(element.style.textAlign);\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.setNodeIndentFromDOM)(element, node);\n  }\n  return {\n    node\n  };\n}\nfunction $createHeadingNode(headingTag = 'h1') {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new HeadingNode(headingTag));\n}\nfunction $isHeadingNode(node) {\n  return node instanceof HeadingNode;\n}\nfunction onPasteForRichText(event, editor) {\n  event.preventDefault();\n  editor.update(() => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    const clipboardData = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, InputEvent) || (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, KeyboardEvent) ? null : event.clipboardData;\n    if (clipboardData != null && selection !== null) {\n      (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.$insertDataTransferForRichText)(clipboardData, selection, editor);\n    }\n  }, {\n    tag: 'paste'\n  });\n}\nasync function onCutForRichText(event, editor) {\n  await (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.copyToClipboard)(editor, (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, ClipboardEvent) ? event : null);\n  editor.update(() => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      selection.removeText();\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      selection.getNodes().forEach(node => node.remove());\n    }\n  });\n}\n\n// Clipboard may contain files that we aren't allowed to read. While the event is arguably useless,\n// in certain occasions, we want to know whether it was a file transfer, as opposed to text. We\n// control this with the first boolean flag.\nfunction eventFiles(event) {\n  let dataTransfer = null;\n  if ((0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, DragEvent)) {\n    dataTransfer = event.dataTransfer;\n  } else if ((0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, ClipboardEvent)) {\n    dataTransfer = event.clipboardData;\n  }\n  if (dataTransfer === null) {\n    return [false, [], false];\n  }\n  const types = dataTransfer.types;\n  const hasFiles = types.includes('Files');\n  const hasContent = types.includes('text/html') || types.includes('text/plain');\n  return [hasFiles, Array.from(dataTransfer.files), hasContent];\n}\nfunction $handleIndentAndOutdent(indentOrOutdent) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    return false;\n  }\n  const alreadyHandled = new Set();\n  const nodes = selection.getNodes();\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    const key = node.getKey();\n    if (alreadyHandled.has(key)) {\n      continue;\n    }\n    const parentBlock = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, parentNode => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(parentNode) && !parentNode.isInline());\n    if (parentBlock === null) {\n      continue;\n    }\n    const parentKey = parentBlock.getKey();\n    if (parentBlock.canIndent() && !alreadyHandled.has(parentKey)) {\n      alreadyHandled.add(parentKey);\n      indentOrOutdent(parentBlock);\n    }\n  }\n  return alreadyHandled.size > 0;\n}\nfunction $isTargetWithinDecorator(target) {\n  const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(target);\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(node);\n}\nfunction $isSelectionAtEndOfRoot(selection) {\n  const focus = selection.focus;\n  return focus.key === 'root' && focus.offset === (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)().getChildrenSize();\n}\n\n/**\n * Resets the capitalization of the selection to default.\n * Called when the user presses space, tab, or enter key.\n * @param selection The selection to reset the capitalization of.\n */\nfunction $resetCapitalization(selection) {\n  for (const format of ['lowercase', 'uppercase', 'capitalize']) {\n    if (selection.hasFormat(format)) {\n      selection.toggleFormat(format);\n    }\n  }\n}\nfunction registerRichText(editor) {\n  const removeListener = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.mergeRegister)(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CLICK_COMMAND, payload => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      selection.clear();\n      return true;\n    }\n    return false;\n  }, 0), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_CHARACTER_COMMAND, isBackward => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      selection.deleteCharacter(isBackward);\n      return true;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      selection.deleteNodes();\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_WORD_COMMAND, isBackward => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.deleteWord(isBackward);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_LINE_COMMAND, isBackward => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.deleteLine(isBackward);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CONTROLLED_TEXT_INSERTION_COMMAND, eventOrText => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (typeof eventOrText === 'string') {\n      if (selection !== null) {\n        selection.insertText(eventOrText);\n      }\n    } else {\n      if (selection === null) {\n        return false;\n      }\n      const dataTransfer = eventOrText.dataTransfer;\n      if (dataTransfer != null) {\n        (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.$insertDataTransferForRichText)(dataTransfer, selection, editor);\n      } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n        const data = eventOrText.data;\n        if (data) {\n          selection.insertText(data);\n        }\n        return true;\n      }\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.REMOVE_TEXT_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.removeText();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FORMAT_TEXT_COMMAND, format => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.formatText(format);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FORMAT_ELEMENT_COMMAND, format => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      return false;\n    }\n    const nodes = selection.getNodes();\n    for (const node of nodes) {\n      const element = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, parentNode => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(parentNode) && !parentNode.isInline());\n      if (element !== null) {\n        element.setFormat(format);\n      }\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_LINE_BREAK_COMMAND, selectStart => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.insertLineBreak(selectStart);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.insertParagraph();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_TAB_COMMAND, () => {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$insertNodes)([(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTabNode)()]);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INDENT_CONTENT_COMMAND, () => {\n    return $handleIndentAndOutdent(block => {\n      const indent = block.getIndent();\n      block.setIndent(indent + 1);\n    });\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.OUTDENT_CONTENT_COMMAND, () => {\n    return $handleIndentAndOutdent(block => {\n      const indent = block.getIndent();\n      if (indent > 0) {\n        block.setIndent(indent - 1);\n      }\n    });\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_UP_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection) && !$isTargetWithinDecorator(event.target)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        nodes[0].selectPrevious();\n        return true;\n      }\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const possibleNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentNode)(selection.focus, true);\n      if (!event.shiftKey && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(possibleNode) && !possibleNode.isIsolated() && !possibleNode.isInline()) {\n        possibleNode.selectPrevious();\n        event.preventDefault();\n        return true;\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_DOWN_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        nodes[0].selectNext(0, 0);\n        return true;\n      }\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      if ($isSelectionAtEndOfRoot(selection)) {\n        event.preventDefault();\n        return true;\n      }\n      const possibleNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentNode)(selection.focus, false);\n      if (!event.shiftKey && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(possibleNode) && !possibleNode.isIsolated() && !possibleNode.isInline()) {\n        possibleNode.selectNext();\n        event.preventDefault();\n        return true;\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_LEFT_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        event.preventDefault();\n        nodes[0].selectPrevious();\n        return true;\n      }\n    }\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    if ((0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$shouldOverrideDefaultCharacterSelection)(selection, true)) {\n      const isHoldingShift = event.shiftKey;\n      event.preventDefault();\n      (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$moveCharacter)(selection, isHoldingShift, true);\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_RIGHT_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection) && !$isTargetWithinDecorator(event.target)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        event.preventDefault();\n        nodes[0].selectNext(0, 0);\n        return true;\n      }\n    }\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const isHoldingShift = event.shiftKey;\n    if ((0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$shouldOverrideDefaultCharacterSelection)(selection, false)) {\n      event.preventDefault();\n      (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$moveCharacter)(selection, isHoldingShift, false);\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_BACKSPACE_COMMAND, event => {\n    if ($isTargetWithinDecorator(event.target)) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const {\n        anchor\n      } = selection;\n      const anchorNode = anchor.getNode();\n      if (selection.isCollapsed() && anchor.offset === 0 && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(anchorNode)) {\n        const element = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$getNearestBlockElementAncestorOrThrow)(anchorNode);\n        if (element.getIndent() > 0) {\n          event.preventDefault();\n          return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.OUTDENT_CONTENT_COMMAND, undefined);\n        }\n      }\n\n      // Exception handling for iOS native behavior instead of Lexical's behavior when using Korean on iOS devices.\n      // more details - https://github.com/facebook/lexical/issues/5841\n      if (IS_IOS && navigator.language === 'ko-KR') {\n        return false;\n      }\n    } else if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      return false;\n    }\n    event.preventDefault();\n    return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_CHARACTER_COMMAND, true);\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_DELETE_COMMAND, event => {\n    if ($isTargetWithinDecorator(event.target)) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection))) {\n      return false;\n    }\n    event.preventDefault();\n    return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_CHARACTER_COMMAND, false);\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ENTER_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    $resetCapitalization(selection);\n    if (event !== null) {\n      // If we have beforeinput, then we can avoid blocking\n      // the default behavior. This ensures that the iOS can\n      // intercept that we're actually inserting a paragraph,\n      // and autocomplete, autocapitalize etc work as intended.\n      // This can also cause a strange performance issue in\n      // Safari, where there is a noticeable pause due to\n      // preventing the key down of enter.\n      if ((IS_IOS || IS_SAFARI || IS_APPLE_WEBKIT) && CAN_USE_BEFORE_INPUT) {\n        return false;\n      }\n      event.preventDefault();\n      if (event.shiftKey) {\n        return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_LINE_BREAK_COMMAND, false);\n      }\n    }\n    return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, undefined);\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ESCAPE_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    editor.blur();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DROP_COMMAND, event => {\n    const [, files] = eventFiles(event);\n    if (files.length > 0) {\n      const x = event.clientX;\n      const y = event.clientY;\n      const eventRange = caretFromPoint(x, y);\n      if (eventRange !== null) {\n        const {\n          offset: domOffset,\n          node: domNode\n        } = eventRange;\n        const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(domNode);\n        if (node !== null) {\n          const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createRangeSelection)();\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(node)) {\n            selection.anchor.set(node.getKey(), domOffset, 'text');\n            selection.focus.set(node.getKey(), domOffset, 'text');\n          } else {\n            const parentKey = node.getParentOrThrow().getKey();\n            const offset = node.getIndexWithinParent() + 1;\n            selection.anchor.set(parentKey, offset, 'element');\n            selection.focus.set(parentKey, offset, 'element');\n          }\n          const normalizedSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeSelection__EXPERIMENTAL)(selection);\n          (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(normalizedSelection);\n        }\n        editor.dispatchCommand(DRAG_DROP_PASTE, files);\n      }\n      event.preventDefault();\n      return true;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DRAGSTART_COMMAND, event => {\n    const [isFileTransfer] = eventFiles(event);\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (isFileTransfer && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DRAGOVER_COMMAND, event => {\n    const [isFileTransfer] = eventFiles(event);\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (isFileTransfer && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const x = event.clientX;\n    const y = event.clientY;\n    const eventRange = caretFromPoint(x, y);\n    if (eventRange !== null) {\n      const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(eventRange.node);\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(node)) {\n        // Show browser caret as the user is dragging the media across the screen. Won't work\n        // for DecoratorNode nor it's relevant.\n        event.preventDefault();\n      }\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECT_ALL_COMMAND, () => {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$selectAll)();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.COPY_COMMAND, event => {\n    (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.copyToClipboard)(editor, (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, ClipboardEvent) ? event : null);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CUT_COMMAND, event => {\n    onCutForRichText(event, editor);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.PASTE_COMMAND, event => {\n    const [, files, hasTextContent] = eventFiles(event);\n    if (files.length > 0 && !hasTextContent) {\n      editor.dispatchCommand(DRAG_DROP_PASTE, files);\n      return true;\n    }\n\n    // if inputs then paste within the input ignore creating a new node on paste event\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(event.target) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isSelectionCapturedInDecoratorInput)(event.target)) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (selection !== null) {\n      onPasteForRichText(event, editor);\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_SPACE_COMMAND, _ => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      $resetCapitalization(selection);\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_TAB_COMMAND, _ => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      $resetCapitalization(selection);\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR));\n  return removeListener;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\n");

/***/ })

};
;