"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+headless@0.28.0";
exports.ids = ["vendor-chunks/@lexical+headless@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+headless@0.28.0/node_modules/@lexical/headless/LexicalHeadless.dev.mjs":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+headless@0.28.0/node_modules/@lexical/headless/LexicalHeadless.dev.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHeadlessEditor: () => (/* binding */ createHeadlessEditor)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n/**\n * Generates a headless editor that allows lexical to be used without the need for a DOM, eg in Node.js.\n * Throws an error when unsupported methods are used.\n * @param editorConfig - The optional lexical editor configuration.\n * @returns - The configured headless editor.\n */\nfunction createHeadlessEditor(editorConfig) {\n  const editor = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createEditor)(editorConfig);\n  editor._headless = true;\n  const unsupportedMethods = ['registerDecoratorListener', 'registerRootListener', 'registerMutationListener', 'getRootElement', 'setRootElement', 'getElementByKey', 'focus', 'blur'];\n  unsupportedMethods.forEach(method => {\n    editor[method] = () => {\n      throw new Error(`${method} is not supported in headless mode`);\n    };\n  });\n  return editor;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+headless@0.28.0/node_modules/@lexical/headless/LexicalHeadless.dev.mjs\n");

/***/ })

};
;