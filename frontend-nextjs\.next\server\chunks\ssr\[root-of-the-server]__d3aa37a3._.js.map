{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost:\r\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline'\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='button'\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/features/auth/components/sign-in-view.tsx"], "sourcesContent": ["import { buttonVariants } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\nimport { SignIn as ClerkSignInForm } from '@clerk/nextjs';\r\nimport { GitHubLogoIcon } from '@radix-ui/react-icons';\r\nimport { IconStar } from '@tabler/icons-react';\r\nimport { Metadata } from 'next';\r\nimport Link from 'next/link';\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Authentication',\r\n  description: 'Authentication forms built using the components.'\r\n};\r\n\r\nexport default function SignInViewPage({ stars }: { stars: number }) {\r\n  return (\r\n    <div className='relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0'>\r\n      <Link\r\n        href='/examples/authentication'\r\n        className={cn(\r\n          buttonVariants({ variant: 'ghost' }),\r\n          'absolute top-4 right-4 hidden md:top-8 md:right-8'\r\n        )}\r\n      >\r\n        Login\r\n      </Link>\r\n      <div className='bg-muted relative hidden h-full flex-col p-10 text-white lg:flex dark:border-r'>\r\n        <div className='absolute inset-0 bg-zinc-900' />\r\n        <div className='relative z-20 flex items-center text-lg font-medium'>\r\n          <svg\r\n            xmlns='http://www.w3.org/2000/svg'\r\n            viewBox='0 0 24 24'\r\n            fill='none'\r\n            stroke='currentColor'\r\n            strokeWidth='2'\r\n            strokeLinecap='round'\r\n            strokeLinejoin='round'\r\n            className='mr-2 h-6 w-6'\r\n          >\r\n            <path d='M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3' />\r\n          </svg>\r\n          Logo\r\n        </div>\r\n        <div className='relative z-20 mt-auto'>\r\n          <blockquote className='space-y-2'>\r\n            <p className='text-lg'>\r\n              &ldquo;This starter template has saved me countless hours of work\r\n              and helped me deliver projects to my clients faster than ever\r\n              before.&rdquo;\r\n            </p>\r\n            <footer className='text-sm'>Random Dude</footer>\r\n          </blockquote>\r\n        </div>\r\n      </div>\r\n      <div className='flex h-full items-center justify-center p-4 lg:p-8'>\r\n        <div className='flex w-full max-w-md flex-col items-center justify-center space-y-6'>\r\n          {/* github link  */}\r\n          <Link\r\n            className={cn('group inline-flex hover:text-yellow-200')}\r\n            target='_blank'\r\n            href={'https://github.com/kiranism/next-shadcn-dashboard-starter'}\r\n          >\r\n            <div className='flex items-center'>\r\n              <GitHubLogoIcon className='size-4' />\r\n              <span className='ml-1 inline'>Star on GitHub</span>{' '}\r\n            </div>\r\n            <div className='ml-2 flex items-center gap-1 text-sm md:flex'>\r\n              <IconStar\r\n                className='size-4 text-gray-500 transition-all duration-300 group-hover:text-yellow-300'\r\n                fill='currentColor'\r\n              />\r\n              <span className='font-display font-medium'>{stars}</span>\r\n            </div>\r\n          </Link>\r\n          <ClerkSignInForm\r\n            initialValues={{\r\n              emailAddress: '<EMAIL>'\r\n            }}\r\n          />\r\n\r\n          <p className='text-muted-foreground px-8 text-center text-sm'>\r\n            By clicking continue, you agree to our{' '}\r\n            <Link\r\n              href='/terms'\r\n              className='hover:text-primary underline underline-offset-4'\r\n            >\r\n              Terms of Service\r\n            </Link>{' '}\r\n            and{' '}\r\n            <Link\r\n              href='/privacy'\r\n              className='hover:text-primary underline underline-offset-4'\r\n            >\r\n              Privacy Policy\r\n            </Link>\r\n            .\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,eAAe,EAAE,KAAK,EAAqB;IACjE,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC,2QAAA,CAAA,UAAI;gBACH,MAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;oBAAE,SAAS;gBAAQ,IAClC;0BAEH;;;;;;0BAGD,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCACC,OAAM;gCACN,SAAQ;gCACR,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,eAAc;gCACd,gBAAe;gCACf,WAAU;0CAEV,cAAA,6VAAC;oCAAK,GAAE;;;;;;;;;;;4BACJ;;;;;;;kCAGR,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAW,WAAU;;8CACpB,6VAAC;oCAAE,WAAU;8CAAU;;;;;;8CAKvB,6VAAC;oCAAO,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;0BAIlC,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCAEb,6VAAC,2QAAA,CAAA,UAAI;4BACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;4BACd,QAAO;4BACP,MAAM;;8CAEN,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,kRAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;sDAC1B,6VAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAsB;;;;;;;8CAEtD,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,oTAAA,CAAA,WAAQ;4CACP,WAAU;4CACV,MAAK;;;;;;sDAEP,6VAAC;4CAAK,WAAU;sDAA4B;;;;;;;;;;;;;;;;;;sCAGhD,6VAAC,gSAAA,CAAA,SAAe;4BACd,eAAe;gCACb,cAAc;4BAChB;;;;;;sCAGF,6VAAC;4BAAE,WAAU;;gCAAiD;gCACrB;8CACvC,6VAAC,2QAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;gCAEO;gCAAI;gCACR;8CACJ,6VAAC,2QAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;gCAEM;;;;;;;;;;;;;;;;;;;;;;;;AAOnB", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/app/auth/sign-in/%5B%5B...sign-in%5D%5D/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\r\nimport SignInViewPage from '@/features/auth/components/sign-in-view';\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Authentication | Sign In',\r\n  description: 'Sign In page for authentication.'\r\n};\r\n\r\nexport default async function Page() {\r\n  let stars = 3000; // Default value\r\n\r\n  try {\r\n    const response = await fetch(\r\n      'https://api.github.com/repos/kiranism/next-shadcn-dashboard-starter',\r\n      {\r\n        next: { revalidate: 86400 }\r\n      }\r\n    );\r\n\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      stars = data.stargazers_count || stars; // Update stars if API response is valid\r\n    }\r\n  } catch (error) {\r\n    // Error fetching GitHub stars, using default value\r\n  }\r\n  return <SignInViewPage stars={stars} />;\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,eAAe;IAC5B,IAAI,QAAQ,MAAM,gBAAgB;IAElC,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,uEACA;YACE,MAAM;gBAAE,YAAY;YAAM;QAC5B;QAGF,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,KAAK,gBAAgB,IAAI,OAAO,wCAAwC;QAClF;IACF,EAAE,OAAO,OAAO;IACd,mDAAmD;IACrD;IACA,qBAAO,6VAAC,4JAAA,CAAA,UAAc;QAAC,OAAO;;;;;;AAChC", "debugId": null}}]}