"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+react@0.28.0_react_20b46741c70d48d804512ab564db69c2";
exports.ids = ["vendor-chunks/@lexical+react@0.28.0_react_20b46741c70d48d804512ab564db69c2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+react@0.28.0_react_20b46741c70d48d804512ab564db69c2/node_modules/@lexical/react/LexicalDecoratorBlockNode.dev.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+react@0.28.0_react_20b46741c70d48d804512ab564db69c2/node_modules/@lexical/react/LexicalDecoratorBlockNode.dev.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $isDecoratorBlockNode: () => (/* binding */ $isDecoratorBlockNode),\n/* harmony export */   DecoratorBlockNode: () => (/* binding */ DecoratorBlockNode)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nclass DecoratorBlockNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.DecoratorNode {\n  constructor(format, key) {\n    super(key);\n    this.__format = format || '';\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      format: this.__format || ''\n    };\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setFormat(serializedNode.format || '');\n  }\n  canIndent() {\n    return false;\n  }\n  createDOM() {\n    return document.createElement('div');\n  }\n  updateDOM() {\n    return false;\n  }\n  setFormat(format) {\n    const self = this.getWritable();\n    self.__format = format;\n    return self;\n  }\n  isInline() {\n    return false;\n  }\n}\nfunction $isDecoratorBlockNode(node) {\n  return node instanceof DecoratorBlockNode;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+react@0.28.0_react_20b46741c70d48d804512ab564db69c2/node_modules/@lexical/react/LexicalDecoratorBlockNode.dev.mjs\n");

/***/ })

};
;