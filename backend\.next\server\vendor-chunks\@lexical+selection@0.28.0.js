"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+selection@0.28.0";
exports.ids = ["vendor-chunks/@lexical+selection@0.28.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $addNodeStyle: () => (/* binding */ $addNodeStyle),\n/* harmony export */   $cloneWithProperties: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.$cloneWithProperties),\n/* harmony export */   $copyBlockFormatIndent: () => (/* binding */ $copyBlockFormatIndent),\n/* harmony export */   $ensureForwardRangeSelection: () => (/* binding */ $ensureForwardRangeSelection),\n/* harmony export */   $forEachSelectedTextNode: () => (/* binding */ $forEachSelectedTextNode),\n/* harmony export */   $getSelectionStyleValueForProperty: () => (/* binding */ $getSelectionStyleValueForProperty),\n/* harmony export */   $isAtNodeEnd: () => (/* binding */ $isAtNodeEnd),\n/* harmony export */   $isParentElementRTL: () => (/* binding */ $isParentElementRTL),\n/* harmony export */   $moveCaretSelection: () => (/* binding */ $moveCaretSelection),\n/* harmony export */   $moveCharacter: () => (/* binding */ $moveCharacter),\n/* harmony export */   $patchStyleText: () => (/* binding */ $patchStyleText),\n/* harmony export */   $selectAll: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.$selectAll),\n/* harmony export */   $setBlocksType: () => (/* binding */ $setBlocksType),\n/* harmony export */   $shouldOverrideDefaultCharacterSelection: () => (/* binding */ $shouldOverrideDefaultCharacterSelection),\n/* harmony export */   $sliceSelectedTextNodeContent: () => (/* binding */ $sliceSelectedTextNodeContent),\n/* harmony export */   $trimTextContentFromAnchor: () => (/* binding */ $trimTextContentFromAnchor),\n/* harmony export */   $wrapNodes: () => (/* binding */ $wrapNodes),\n/* harmony export */   createDOMRange: () => (/* binding */ createDOMRange),\n/* harmony export */   createRectsFromDOMRange: () => (/* binding */ createRectsFromDOMRange),\n/* harmony export */   getCSSFromStyleObject: () => (/* binding */ getCSSFromStyleObject),\n/* harmony export */   getStyleObjectFromCSS: () => (/* binding */ getStyleObjectFromCSS),\n/* harmony export */   trimTextContentFromAnchor: () => (/* binding */ trimTextContentFromAnchor)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/./node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nconst CSS_TO_STYLES = new Map();\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction getDOMTextNode(element) {\n  let node = element;\n  while (node != null) {\n    if (node.nodeType === Node.TEXT_NODE) {\n      return node;\n    }\n    node = node.firstChild;\n  }\n  return null;\n}\nfunction getDOMIndexWithinParent(node) {\n  const parent = node.parentNode;\n  if (parent == null) {\n    throw new Error('Should never happen');\n  }\n  return [parent, Array.from(parent.childNodes).indexOf(node)];\n}\n\n/**\n * Creates a selection range for the DOM.\n * @param editor - The lexical editor.\n * @param anchorNode - The anchor node of a selection.\n * @param _anchorOffset - The amount of space offset from the anchor to the focus.\n * @param focusNode - The current focus.\n * @param _focusOffset - The amount of space offset from the focus to the anchor.\n * @returns The range of selection for the DOM that was created.\n */\nfunction createDOMRange(editor, anchorNode, _anchorOffset, focusNode, _focusOffset) {\n  const anchorKey = anchorNode.getKey();\n  const focusKey = focusNode.getKey();\n  const range = document.createRange();\n  let anchorDOM = editor.getElementByKey(anchorKey);\n  let focusDOM = editor.getElementByKey(focusKey);\n  let anchorOffset = _anchorOffset;\n  let focusOffset = _focusOffset;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(anchorNode)) {\n    anchorDOM = getDOMTextNode(anchorDOM);\n  }\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(focusNode)) {\n    focusDOM = getDOMTextNode(focusDOM);\n  }\n  if (anchorNode === undefined || focusNode === undefined || anchorDOM === null || focusDOM === null) {\n    return null;\n  }\n  if (anchorDOM.nodeName === 'BR') {\n    [anchorDOM, anchorOffset] = getDOMIndexWithinParent(anchorDOM);\n  }\n  if (focusDOM.nodeName === 'BR') {\n    [focusDOM, focusOffset] = getDOMIndexWithinParent(focusDOM);\n  }\n  const firstChild = anchorDOM.firstChild;\n  if (anchorDOM === focusDOM && firstChild != null && firstChild.nodeName === 'BR' && anchorOffset === 0 && focusOffset === 0) {\n    focusOffset = 1;\n  }\n  try {\n    range.setStart(anchorDOM, anchorOffset);\n    range.setEnd(focusDOM, focusOffset);\n  } catch (e) {\n    return null;\n  }\n  if (range.collapsed && (anchorOffset !== focusOffset || anchorKey !== focusKey)) {\n    // Range is backwards, we need to reverse it\n    range.setStart(focusDOM, focusOffset);\n    range.setEnd(anchorDOM, anchorOffset);\n  }\n  return range;\n}\n\n/**\n * Creates DOMRects, generally used to help the editor find a specific location on the screen.\n * @param editor - The lexical editor\n * @param range - A fragment of a document that can contain nodes and parts of text nodes.\n * @returns The selectionRects as an array.\n */\nfunction createRectsFromDOMRange(editor, range) {\n  const rootElement = editor.getRootElement();\n  if (rootElement === null) {\n    return [];\n  }\n  const rootRect = rootElement.getBoundingClientRect();\n  const computedStyle = getComputedStyle(rootElement);\n  const rootPadding = parseFloat(computedStyle.paddingLeft) + parseFloat(computedStyle.paddingRight);\n  const selectionRects = Array.from(range.getClientRects());\n  let selectionRectsLength = selectionRects.length;\n  //sort rects from top left to bottom right.\n  selectionRects.sort((a, b) => {\n    const top = a.top - b.top;\n    // Some rects match position closely, but not perfectly,\n    // so we give a 3px tolerance.\n    if (Math.abs(top) <= 3) {\n      return a.left - b.left;\n    }\n    return top;\n  });\n  let prevRect;\n  for (let i = 0; i < selectionRectsLength; i++) {\n    const selectionRect = selectionRects[i];\n    // Exclude rects that overlap preceding Rects in the sorted list.\n    const isOverlappingRect = prevRect && prevRect.top <= selectionRect.top && prevRect.top + prevRect.height > selectionRect.top && prevRect.left + prevRect.width > selectionRect.left;\n    // Exclude selections that span the entire element\n    const selectionSpansElement = selectionRect.width + rootPadding === rootRect.width;\n    if (isOverlappingRect || selectionSpansElement) {\n      selectionRects.splice(i--, 1);\n      selectionRectsLength--;\n      continue;\n    }\n    prevRect = selectionRect;\n  }\n  return selectionRects;\n}\n\n/**\n * Creates an object containing all the styles and their values provided in the CSS string.\n * @param css - The CSS string of styles and their values.\n * @returns The styleObject containing all the styles and their values.\n */\nfunction getStyleObjectFromRawCSS(css) {\n  const styleObject = {};\n  if (!css) {\n    return styleObject;\n  }\n  const styles = css.split(';');\n  for (const style of styles) {\n    if (style !== '') {\n      const [key, value] = style.split(/:([^]+)/); // split on first colon\n      if (key && value) {\n        styleObject[key.trim()] = value.trim();\n      }\n    }\n  }\n  return styleObject;\n}\n\n/**\n * Given a CSS string, returns an object from the style cache.\n * @param css - The CSS property as a string.\n * @returns The value of the given CSS property.\n */\nfunction getStyleObjectFromCSS(css) {\n  let value = CSS_TO_STYLES.get(css);\n  if (value === undefined) {\n    value = getStyleObjectFromRawCSS(css);\n    CSS_TO_STYLES.set(css, value);\n  }\n  {\n    // Freeze the value in DEV to prevent accidental mutations\n    Object.freeze(value);\n  }\n  return value;\n}\n\n/**\n * Gets the CSS styles from the style object.\n * @param styles - The style object containing the styles to get.\n * @returns A string containing the CSS styles and their values.\n */\nfunction getCSSFromStyleObject(styles) {\n  let css = '';\n  for (const style in styles) {\n    if (style) {\n      css += `${style}: ${styles[style]};`;\n    }\n  }\n  return css;\n}\n\n/**\n * Generally used to append text content to HTML and JSON. Grabs the text content and \"slices\"\n * it to be generated into the new TextNode.\n * @param selection - The selection containing the node whose TextNode is to be edited.\n * @param textNode - The TextNode to be edited.\n * @returns The updated TextNode.\n */\nfunction $sliceSelectedTextNodeContent(selection, textNode) {\n  const anchorAndFocus = selection.getStartEndPoints();\n  if (textNode.isSelected(selection) && !textNode.isSegmented() && !textNode.isToken() && anchorAndFocus !== null) {\n    const [anchor, focus] = anchorAndFocus;\n    const isBackward = selection.isBackward();\n    const anchorNode = anchor.getNode();\n    const focusNode = focus.getNode();\n    const isAnchor = textNode.is(anchorNode);\n    const isFocus = textNode.is(focusNode);\n    if (isAnchor || isFocus) {\n      const [anchorOffset, focusOffset] = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCharacterOffsets)(selection);\n      const isSame = anchorNode.is(focusNode);\n      const isFirst = textNode.is(isBackward ? focusNode : anchorNode);\n      const isLast = textNode.is(isBackward ? anchorNode : focusNode);\n      let startOffset = 0;\n      let endOffset = undefined;\n      if (isSame) {\n        startOffset = anchorOffset > focusOffset ? focusOffset : anchorOffset;\n        endOffset = anchorOffset > focusOffset ? anchorOffset : focusOffset;\n      } else if (isFirst) {\n        const offset = isBackward ? focusOffset : anchorOffset;\n        startOffset = offset;\n        endOffset = undefined;\n      } else if (isLast) {\n        const offset = isBackward ? anchorOffset : focusOffset;\n        startOffset = 0;\n        endOffset = offset;\n      }\n      textNode.__text = textNode.__text.slice(startOffset, endOffset);\n      return textNode;\n    }\n  }\n  return textNode;\n}\n\n/**\n * Determines if the current selection is at the end of the node.\n * @param point - The point of the selection to test.\n * @returns true if the provided point offset is in the last possible position, false otherwise.\n */\nfunction $isAtNodeEnd(point) {\n  if (point.type === 'text') {\n    return point.offset === point.getNode().getTextContentSize();\n  }\n  const node = point.getNode();\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n    formatDevErrorMessage(`isAtNodeEnd: node must be a TextNode or ElementNode`);\n  }\n  return point.offset === node.getChildrenSize();\n}\n\n/**\n * Trims text from a node in order to shorten it, eg. to enforce a text's max length. If it deletes text\n * that is an ancestor of the anchor then it will leave 2 indents, otherwise, if no text content exists, it deletes\n * the TextNode. It will move the focus to either the end of any left over text or beginning of a new TextNode.\n * @param editor - The lexical editor.\n * @param anchor - The anchor of the current selection, where the selection should be pointing.\n * @param delCount - The amount of characters to delete. Useful as a dynamic variable eg. textContentSize - maxLength;\n */\nfunction $trimTextContentFromAnchor(editor, anchor, delCount) {\n  // Work from the current selection anchor point\n  let currentNode = anchor.getNode();\n  let remaining = delCount;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode)) {\n    const descendantNode = currentNode.getDescendantByIndex(anchor.offset);\n    if (descendantNode !== null) {\n      currentNode = descendantNode;\n    }\n  }\n  while (remaining > 0 && currentNode !== null) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode)) {\n      const lastDescendant = currentNode.getLastDescendant();\n      if (lastDescendant !== null) {\n        currentNode = lastDescendant;\n      }\n    }\n    let nextNode = currentNode.getPreviousSibling();\n    let additionalElementWhitespace = 0;\n    if (nextNode === null) {\n      let parent = currentNode.getParentOrThrow();\n      let parentSibling = parent.getPreviousSibling();\n      while (parentSibling === null) {\n        parent = parent.getParent();\n        if (parent === null) {\n          nextNode = null;\n          break;\n        }\n        parentSibling = parent.getPreviousSibling();\n      }\n      if (parent !== null) {\n        additionalElementWhitespace = parent.isInline() ? 0 : 2;\n        nextNode = parentSibling;\n      }\n    }\n    let text = currentNode.getTextContent();\n    // If the text is empty, we need to consider adding in two line breaks to match\n    // the content if we were to get it from its parent.\n    if (text === '' && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode) && !currentNode.isInline()) {\n      // TODO: should this be handled in core?\n      text = '\\n\\n';\n    }\n    const currentNodeSize = text.length;\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(currentNode) || remaining >= currentNodeSize) {\n      const parent = currentNode.getParent();\n      currentNode.remove();\n      if (parent != null && parent.getChildrenSize() === 0 && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(parent)) {\n        parent.remove();\n      }\n      remaining -= currentNodeSize + additionalElementWhitespace;\n      currentNode = nextNode;\n    } else {\n      const key = currentNode.getKey();\n      // See if we can just revert it to what was in the last editor state\n      const prevTextContent = editor.getEditorState().read(() => {\n        const prevNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNodeByKey)(key);\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevNode) && prevNode.isSimpleText()) {\n          return prevNode.getTextContent();\n        }\n        return null;\n      });\n      const offset = currentNodeSize - remaining;\n      const slicedText = text.slice(0, offset);\n      if (prevTextContent !== null && prevTextContent !== text) {\n        const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n        let target = currentNode;\n        if (!currentNode.isSimpleText()) {\n          const textNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(prevTextContent);\n          currentNode.replace(textNode);\n          target = textNode;\n        } else {\n          currentNode.setTextContent(prevTextContent);\n        }\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && prevSelection.isCollapsed()) {\n          const prevOffset = prevSelection.anchor.offset;\n          target.select(prevOffset, prevOffset);\n        }\n      } else if (currentNode.isSimpleText()) {\n        // Split text\n        const isSelected = anchor.key === key;\n        let anchorOffset = anchor.offset;\n        // Move offset to end if it's less than the remaining number, otherwise\n        // we'll have a negative splitStart.\n        if (anchorOffset < remaining) {\n          anchorOffset = currentNodeSize;\n        }\n        const splitStart = isSelected ? anchorOffset - remaining : 0;\n        const splitEnd = isSelected ? anchorOffset : offset;\n        if (isSelected && splitStart === 0) {\n          const [excessNode] = currentNode.splitText(splitStart, splitEnd);\n          excessNode.remove();\n        } else {\n          const [, excessNode] = currentNode.splitText(splitStart, splitEnd);\n          excessNode.remove();\n        }\n      } else {\n        const textNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(slicedText);\n        currentNode.replace(textNode);\n      }\n      remaining = 0;\n    }\n  }\n}\n\n/**\n * Gets the TextNode's style object and adds the styles to the CSS.\n * @param node - The TextNode to add styles to.\n */\nfunction $addNodeStyle(node) {\n  const CSSText = node.getStyle();\n  const styles = getStyleObjectFromRawCSS(CSSText);\n  CSS_TO_STYLES.set(CSSText, styles);\n}\n\n/**\n * Applies the provided styles to the given TextNode, ElementNode, or\n * collapsed RangeSelection.\n *\n * @param target - The TextNode, ElementNode, or collapsed RangeSelection to apply the styles to\n * @param patch - The patch to apply, which can include multiple styles. \\\\{CSSProperty: value\\\\} . Can also accept a function that returns the new property value.\n */\nfunction $patchStyle(target, patch) {\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(target) ? target.isCollapsed() : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(target) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(target))) {\n    formatDevErrorMessage(`$patchStyle must only be called with a TextNode, ElementNode, or collapsed RangeSelection`);\n  }\n  const prevStyles = getStyleObjectFromCSS((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(target) ? target.style : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(target) ? target.getStyle() : target.getTextStyle());\n  const newStyles = Object.entries(patch).reduce((styles, [key, value]) => {\n    if (typeof value === 'function') {\n      styles[key] = value(prevStyles[key], target);\n    } else if (value === null) {\n      delete styles[key];\n    } else {\n      styles[key] = value;\n    }\n    return styles;\n  }, {\n    ...prevStyles\n  });\n  const newCSSText = getCSSFromStyleObject(newStyles);\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(target) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(target)) {\n    target.setStyle(newCSSText);\n  } else {\n    target.setTextStyle(newCSSText);\n  }\n  CSS_TO_STYLES.set(newCSSText, newStyles);\n}\n\n/**\n * Applies the provided styles to the TextNodes in the provided Selection.\n * Will update partially selected TextNodes by splitting the TextNode and applying\n * the styles to the appropriate one.\n * @param selection - The selected node(s) to update.\n * @param patch - The patch to apply, which can include multiple styles. \\\\{CSSProperty: value\\\\} . Can also accept a function that returns the new property value.\n */\nfunction $patchStyleText(selection, patch) {\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed()) {\n    $patchStyle(selection, patch);\n    const emptyNode = selection.anchor.getNode();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(emptyNode) && emptyNode.isEmpty()) {\n      $patchStyle(emptyNode, patch);\n    }\n  }\n  $forEachSelectedTextNode(textNode => {\n    $patchStyle(textNode, patch);\n  });\n}\nfunction $forEachSelectedTextNode(fn) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!selection) {\n    return;\n  }\n  const slicedTextNodes = new Map();\n  const getSliceIndices = node => slicedTextNodes.get(node.getKey()) || [0, node.getTextContentSize()];\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    for (const slice of (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$caretRangeFromSelection)(selection).getTextSlices()) {\n      if (slice) {\n        slicedTextNodes.set(slice.caret.origin.getKey(), slice.getSliceIndices());\n      }\n    }\n  }\n  const selectedNodes = selection.getNodes();\n  for (const selectedNode of selectedNodes) {\n    if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(selectedNode) && selectedNode.canHaveFormat())) {\n      continue;\n    }\n    const [startOffset, endOffset] = getSliceIndices(selectedNode);\n    // No actual text is selected, so do nothing.\n    if (endOffset === startOffset) {\n      continue;\n    }\n\n    // The entire node is selected or a token/segment, so just format it\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTokenOrSegmented)(selectedNode) || startOffset === 0 && endOffset === selectedNode.getTextContentSize()) {\n      fn(selectedNode);\n    } else {\n      // The node is partially selected, so split it into two or three nodes\n      // and style the selected one.\n      const splitNodes = selectedNode.splitText(startOffset, endOffset);\n      const replacement = splitNodes[startOffset === 0 ? 0 : 1];\n      fn(replacement);\n    }\n  }\n  // Prior to NodeCaret #7046 this would have been a side-effect\n  // so we do this for test compatibility.\n  // TODO: we may want to consider simplifying by removing this\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.anchor.type === 'text' && selection.focus.type === 'text' && selection.anchor.key === selection.focus.key) {\n    $ensureForwardRangeSelection(selection);\n  }\n}\n\n/**\n * Ensure that the given RangeSelection is not backwards. If it\n * is backwards, then the anchor and focus points will be swapped\n * in-place. Ensuring that the selection is a writable RangeSelection\n * is the responsibility of the caller (e.g. in a read-only context\n * you will want to clone $getSelection() before using this).\n *\n * @param selection a writable RangeSelection\n */\nfunction $ensureForwardRangeSelection(selection) {\n  if (selection.isBackward()) {\n    const {\n      anchor,\n      focus\n    } = selection;\n    // stash for the in-place swap\n    const {\n      key,\n      offset,\n      type\n    } = anchor;\n    anchor.set(focus.key, focus.offset, focus.type);\n    focus.set(key, offset, type);\n  }\n}\n\nfunction $copyBlockFormatIndent(srcNode, destNode) {\n  const format = srcNode.getFormatType();\n  const indent = srcNode.getIndent();\n  if (format !== destNode.getFormatType()) {\n    destNode.setFormat(format);\n  }\n  if (indent !== destNode.getIndent()) {\n    destNode.setIndent(indent);\n  }\n}\n\n/**\n * Converts all nodes in the selection that are of one block type to another.\n * @param selection - The selected blocks to be converted.\n * @param $createElement - The function that creates the node. eg. $createParagraphNode.\n * @param $afterCreateElement - The function that updates the new node based on the previous one ($copyBlockFormatIndent by default)\n */\nfunction $setBlocksType(selection, $createElement, $afterCreateElement = $copyBlockFormatIndent) {\n  if (selection === null) {\n    return;\n  }\n  // Selections tend to not include their containing blocks so we effectively\n  // expand it here\n  const anchorAndFocus = selection.getStartEndPoints();\n  const blockMap = new Map();\n  let newSelection = null;\n  if (anchorAndFocus) {\n    const [anchor, focus] = anchorAndFocus;\n    newSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createRangeSelection)();\n    newSelection.anchor.set(anchor.key, anchor.offset, anchor.type);\n    newSelection.focus.set(focus.key, focus.offset, focus.type);\n    const anchorBlock = $getAncestor(anchor.getNode(), lexical__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_$isBlock);\n    const focusBlock = $getAncestor(focus.getNode(), lexical__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_$isBlock);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(anchorBlock)) {\n      blockMap.set(anchorBlock.getKey(), anchorBlock);\n    }\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(focusBlock)) {\n      blockMap.set(focusBlock.getKey(), focusBlock);\n    }\n  }\n  for (const node of selection.getNodes()) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_$isBlock)(node)) {\n      blockMap.set(node.getKey(), node);\n    }\n  }\n  for (const [key, prevNode] of blockMap) {\n    const element = $createElement();\n    $afterCreateElement(prevNode, element);\n    prevNode.replace(element, true);\n    if (newSelection) {\n      if (key === newSelection.anchor.key) {\n        newSelection.anchor.set(element.getKey(), newSelection.anchor.offset, newSelection.anchor.type);\n      }\n      if (key === newSelection.focus.key) {\n        newSelection.focus.set(element.getKey(), newSelection.focus.offset, newSelection.focus.type);\n      }\n    }\n  }\n  if (newSelection && selection.is((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)())) {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(newSelection);\n  }\n}\nfunction isPointAttached(point) {\n  return point.getNode().isAttached();\n}\nfunction $removeParentEmptyElements(startingNode) {\n  let node = startingNode;\n  while (node !== null && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(node)) {\n    const latest = node.getLatest();\n    const parentNode = node.getParent();\n    if (latest.getChildrenSize() === 0) {\n      node.remove(true);\n    }\n    node = parentNode;\n  }\n}\n\n/**\n * @deprecated\n * Wraps all nodes in the selection into another node of the type returned by createElement.\n * @param selection - The selection of nodes to be wrapped.\n * @param createElement - A function that creates the wrapping ElementNode. eg. $createParagraphNode.\n * @param wrappingElement - An element to append the wrapped selection and its children to.\n */\nfunction $wrapNodes(selection, createElement, wrappingElement = null) {\n  const anchorAndFocus = selection.getStartEndPoints();\n  const anchor = anchorAndFocus ? anchorAndFocus[0] : null;\n  const nodes = selection.getNodes();\n  const nodesLength = nodes.length;\n  if (anchor !== null && (nodesLength === 0 || nodesLength === 1 && anchor.type === 'element' && anchor.getNode().getChildrenSize() === 0)) {\n    const target = anchor.type === 'text' ? anchor.getNode().getParentOrThrow() : anchor.getNode();\n    const children = target.getChildren();\n    let element = createElement();\n    element.setFormat(target.getFormatType());\n    element.setIndent(target.getIndent());\n    children.forEach(child => element.append(child));\n    if (wrappingElement) {\n      element = wrappingElement.append(element);\n    }\n    target.replace(element);\n    return;\n  }\n  let topLevelNode = null;\n  let descendants = [];\n  for (let i = 0; i < nodesLength; i++) {\n    const node = nodes[i];\n    // Determine whether wrapping has to be broken down into multiple chunks. This can happen if the\n    // user selected multiple Root-like nodes that have to be treated separately as if they are\n    // their own branch. I.e. you don't want to wrap a whole table, but rather the contents of each\n    // of each of the cell nodes.\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(node)) {\n      $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);\n      descendants = [];\n      topLevelNode = node;\n    } else if (topLevelNode === null || topLevelNode !== null && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$hasAncestor)(node, topLevelNode)) {\n      descendants.push(node);\n    } else {\n      $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);\n      descendants = [node];\n    }\n  }\n  $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);\n}\n\n/**\n * Wraps each node into a new ElementNode.\n * @param selection - The selection of nodes to wrap.\n * @param nodes - An array of nodes, generally the descendants of the selection.\n * @param nodesLength - The length of nodes.\n * @param createElement - A function that creates the wrapping ElementNode. eg. $createParagraphNode.\n * @param wrappingElement - An element to wrap all the nodes into.\n * @returns\n */\nfunction $wrapNodesImpl(selection, nodes, nodesLength, createElement, wrappingElement = null) {\n  if (nodes.length === 0) {\n    return;\n  }\n  const firstNode = nodes[0];\n  const elementMapping = new Map();\n  const elements = [];\n  // The below logic is to find the right target for us to\n  // either insertAfter/insertBefore/append the corresponding\n  // elements to. This is made more complicated due to nested\n  // structures.\n  let target = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(firstNode) ? firstNode : firstNode.getParentOrThrow();\n  if (target.isInline()) {\n    target = target.getParentOrThrow();\n  }\n  let targetIsPrevSibling = false;\n  while (target !== null) {\n    const prevSibling = target.getPreviousSibling();\n    if (prevSibling !== null) {\n      target = prevSibling;\n      targetIsPrevSibling = true;\n      break;\n    }\n    target = target.getParentOrThrow();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(target)) {\n      break;\n    }\n  }\n  const emptyElements = new Set();\n\n  // Find any top level empty elements\n  for (let i = 0; i < nodesLength; i++) {\n    const node = nodes[i];\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && node.getChildrenSize() === 0) {\n      emptyElements.add(node.getKey());\n    }\n  }\n  const movedNodes = new Set();\n\n  // Move out all leaf nodes into our elements array.\n  // If we find a top level empty element, also move make\n  // an element for that.\n  for (let i = 0; i < nodesLength; i++) {\n    const node = nodes[i];\n    let parent = node.getParent();\n    if (parent !== null && parent.isInline()) {\n      parent = parent.getParent();\n    }\n    if (parent !== null && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isLeafNode)(node) && !movedNodes.has(node.getKey())) {\n      const parentKey = parent.getKey();\n      if (elementMapping.get(parentKey) === undefined) {\n        const targetElement = createElement();\n        targetElement.setFormat(parent.getFormatType());\n        targetElement.setIndent(parent.getIndent());\n        elements.push(targetElement);\n        elementMapping.set(parentKey, targetElement);\n        // Move node and its siblings to the new\n        // element.\n        parent.getChildren().forEach(child => {\n          targetElement.append(child);\n          movedNodes.add(child.getKey());\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(child)) {\n            // Skip nested leaf nodes if the parent has already been moved\n            child.getChildrenKeys().forEach(key => movedNodes.add(key));\n          }\n        });\n        $removeParentEmptyElements(parent);\n      }\n    } else if (emptyElements.has(node.getKey())) {\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n        formatDevErrorMessage(`Expected node in emptyElements to be an ElementNode`);\n      }\n      const targetElement = createElement();\n      targetElement.setFormat(node.getFormatType());\n      targetElement.setIndent(node.getIndent());\n      elements.push(targetElement);\n      node.remove(true);\n    }\n  }\n  if (wrappingElement !== null) {\n    for (let i = 0; i < elements.length; i++) {\n      const element = elements[i];\n      wrappingElement.append(element);\n    }\n  }\n  let lastElement = null;\n\n  // If our target is Root-like, let's see if we can re-adjust\n  // so that the target is the first child instead.\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(target)) {\n    if (targetIsPrevSibling) {\n      if (wrappingElement !== null) {\n        target.insertAfter(wrappingElement);\n      } else {\n        for (let i = elements.length - 1; i >= 0; i--) {\n          const element = elements[i];\n          target.insertAfter(element);\n        }\n      }\n    } else {\n      const firstChild = target.getFirstChild();\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(firstChild)) {\n        target = firstChild;\n      }\n      if (firstChild === null) {\n        if (wrappingElement) {\n          target.append(wrappingElement);\n        } else {\n          for (let i = 0; i < elements.length; i++) {\n            const element = elements[i];\n            target.append(element);\n            lastElement = element;\n          }\n        }\n      } else {\n        if (wrappingElement !== null) {\n          firstChild.insertBefore(wrappingElement);\n        } else {\n          for (let i = 0; i < elements.length; i++) {\n            const element = elements[i];\n            firstChild.insertBefore(element);\n            lastElement = element;\n          }\n        }\n      }\n    }\n  } else {\n    if (wrappingElement) {\n      target.insertAfter(wrappingElement);\n    } else {\n      for (let i = elements.length - 1; i >= 0; i--) {\n        const element = elements[i];\n        target.insertAfter(element);\n        lastElement = element;\n      }\n    }\n  }\n  const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && isPointAttached(prevSelection.anchor) && isPointAttached(prevSelection.focus)) {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(prevSelection.clone());\n  } else if (lastElement !== null) {\n    lastElement.selectEnd();\n  } else {\n    selection.dirty = true;\n  }\n}\n\n/**\n * Determines if the default character selection should be overridden. Used with DecoratorNodes\n * @param selection - The selection whose default character selection may need to be overridden.\n * @param isBackward - Is the selection backwards (the focus comes before the anchor)?\n * @returns true if it should be overridden, false if not.\n */\nfunction $shouldOverrideDefaultCharacterSelection(selection, isBackward) {\n  const focusCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$caretFromPoint)(selection.focus, isBackward ? 'previous' : 'next');\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isExtendableTextPointCaret)(focusCaret)) {\n    return false;\n  }\n  for (const nextCaret of (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$extendCaretToRange)(focusCaret)) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(nextCaret)) {\n      return !nextCaret.origin.isInline();\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(nextCaret.origin)) {\n      continue;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(nextCaret.origin)) {\n      return true;\n    }\n    break;\n  }\n  return false;\n}\n\n/**\n * Moves the selection according to the arguments.\n * @param selection - The selected text or nodes.\n * @param isHoldingShift - Is the shift key being held down during the operation.\n * @param isBackward - Is the selection selected backwards (the focus comes before the anchor)?\n * @param granularity - The distance to adjust the current selection.\n */\nfunction $moveCaretSelection(selection, isHoldingShift, isBackward, granularity) {\n  selection.modify(isHoldingShift ? 'extend' : 'move', isBackward, granularity);\n}\n\n/**\n * Tests a parent element for right to left direction.\n * @param selection - The selection whose parent is to be tested.\n * @returns true if the selections' parent element has a direction of 'rtl' (right to left), false otherwise.\n */\nfunction $isParentElementRTL(selection) {\n  const anchorNode = selection.anchor.getNode();\n  const parent = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(anchorNode) ? anchorNode : anchorNode.getParentOrThrow();\n  return parent.getDirection() === 'rtl';\n}\n\n/**\n * Moves selection by character according to arguments.\n * @param selection - The selection of the characters to move.\n * @param isHoldingShift - Is the shift key being held down during the operation.\n * @param isBackward - Is the selection backward (the focus comes before the anchor)?\n */\nfunction $moveCharacter(selection, isHoldingShift, isBackward) {\n  const isRTL = $isParentElementRTL(selection);\n  $moveCaretSelection(selection, isHoldingShift, isBackward ? !isRTL : isRTL, 'character');\n}\n\n/**\n * Returns the current value of a CSS property for Nodes, if set. If not set, it returns the defaultValue.\n * @param node - The node whose style value to get.\n * @param styleProperty - The CSS style property.\n * @param defaultValue - The default value for the property.\n * @returns The value of the property for node.\n */\nfunction $getNodeStyleValueForProperty(node, styleProperty, defaultValue) {\n  const css = node.getStyle();\n  const styleObject = getStyleObjectFromCSS(css);\n  if (styleObject !== null) {\n    return styleObject[styleProperty] || defaultValue;\n  }\n  return defaultValue;\n}\n\n/**\n * Returns the current value of a CSS property for TextNodes in the Selection, if set. If not set, it returns the defaultValue.\n * If all TextNodes do not have the same value, it returns an empty string.\n * @param selection - The selection of TextNodes whose value to find.\n * @param styleProperty - The CSS style property.\n * @param defaultValue - The default value for the property, defaults to an empty string.\n * @returns The value of the property for the selected TextNodes.\n */\nfunction $getSelectionStyleValueForProperty(selection, styleProperty, defaultValue = '') {\n  let styleValue = null;\n  const nodes = selection.getNodes();\n  const anchor = selection.anchor;\n  const focus = selection.focus;\n  const isBackward = selection.isBackward();\n  const endOffset = isBackward ? focus.offset : anchor.offset;\n  const endNode = isBackward ? focus.getNode() : anchor.getNode();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed() && selection.style !== '') {\n    const css = selection.style;\n    const styleObject = getStyleObjectFromCSS(css);\n    if (styleObject !== null && styleProperty in styleObject) {\n      return styleObject[styleProperty];\n    }\n  }\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n\n    // if no actual characters in the end node are selected, we don't\n    // include it in the selection for purposes of determining style\n    // value\n    if (i !== 0 && endOffset === 0 && node.is(endNode)) {\n      continue;\n    }\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(node)) {\n      const nodeStyleValue = $getNodeStyleValueForProperty(node, styleProperty, defaultValue);\n      if (styleValue === null) {\n        styleValue = nodeStyleValue;\n      } else if (styleValue !== nodeStyleValue) {\n        // multiple text nodes are in the selection and they don't all\n        // have the same style.\n        styleValue = '';\n        break;\n      }\n    }\n  }\n  return styleValue === null ? defaultValue : styleValue;\n}\nfunction $getAncestor(node, predicate) {\n  let parent = node;\n  while (parent !== null && parent.getParent() !== null && !predicate(parent)) {\n    parent = parent.getParentOrThrow();\n  }\n  return predicate(parent) ? parent : null;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/** @deprecated renamed to {@link $trimTextContentFromAnchor} by @lexical/eslint-plugin rules-of-lexical */\nconst trimTextContentFromAnchor = $trimTextContentFromAnchor;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\n");

/***/ })

};
;