"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362";
exports.ids = ["vendor-chunks/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/addFieldStatePromise.js":
/*!********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/addFieldStatePromise.js ***!
  \********************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addFieldStatePromise: () => (/* binding */ addFieldStatePromise)\n/* harmony export */ });\n/* harmony import */ var bson_objectid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bson-objectid */ \"(rsc)/./node_modules/.pnpm/bson-objectid@2.0.4/node_modules/bson-objectid/objectid.js\");\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var payload_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! payload/shared */ \"payload/shared\");\n/* harmony import */ var _utilities_resolveFilterOptions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utilities/resolveFilterOptions.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/utilities/resolveFilterOptions.js\");\n/* harmony import */ var _isRowCollapsed_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./isRowCollapsed.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/isRowCollapsed.js\");\n/* harmony import */ var _iterateFields_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./iterateFields.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/iterateFields.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([payload__WEBPACK_IMPORTED_MODULE_1__, payload_shared__WEBPACK_IMPORTED_MODULE_2__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_3__]);\n([payload__WEBPACK_IMPORTED_MODULE_1__, payload_shared__WEBPACK_IMPORTED_MODULE_2__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst ObjectId = bson_objectid__WEBPACK_IMPORTED_MODULE_0__[\"default\"] || bson_objectid__WEBPACK_IMPORTED_MODULE_0__;\n/**\n * Flattens the fields schema and fields data.\n * The output is the field path (e.g. array.0.name) mapped to a FormField object.\n */\nconst addFieldStatePromise = async args => {\n  const {\n    id,\n    addErrorPathToParent: addErrorPathToParentArg,\n    anyParentLocalized = false,\n    blockData,\n    clientFieldSchemaMap,\n    collectionSlug,\n    data,\n    field,\n    fieldSchemaMap,\n    filter,\n    forceFullValue = false,\n    fullData,\n    includeSchema = false,\n    indexPath,\n    mockRSCs,\n    omitParents = false,\n    operation,\n    parentPath,\n    parentPermissions,\n    parentSchemaPath,\n    passesCondition,\n    path,\n    preferences,\n    previousFormState,\n    renderAllFields,\n    renderFieldFn,\n    req,\n    schemaPath,\n    select,\n    selectMode,\n    skipConditionChecks = false,\n    skipValidation = false,\n    state\n  } = args;\n  if (!args.clientFieldSchemaMap && args.renderFieldFn) {\n    console.warn('clientFieldSchemaMap is not passed to addFieldStatePromise - this will reduce performance');\n  }\n  let fieldPermissions = true;\n  const fieldState = {};\n  const lastRenderedPath = previousFormState?.[path]?.lastRenderedPath;\n  // Append only if true to avoid sending '$undefined' through the network\n  if (lastRenderedPath) {\n    fieldState.lastRenderedPath = lastRenderedPath;\n  }\n  // If we're rendering all fields, no need to flag this as added by server\n  const addedByServer = !renderAllFields && !previousFormState?.[path];\n  // Append only if true to avoid sending '$undefined' through the network\n  if (addedByServer) {\n    fieldState.addedByServer = true;\n  }\n  // Append only if true to avoid sending '$undefined' through the network\n  if (passesCondition === false) {\n    fieldState.passesCondition = false;\n  }\n  // Append only if true to avoid sending '$undefined' through the network\n  if (includeSchema) {\n    fieldState.fieldSchema = field;\n  }\n  if ((0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldAffectsData)(field) && !(0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldIsHiddenOrDisabled)(field)) {\n    fieldPermissions = parentPermissions === true ? parentPermissions : (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.deepCopyObjectSimple)(parentPermissions?.[field.name]);\n    let hasPermission = fieldPermissions === true || (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.deepCopyObjectSimple)(fieldPermissions?.read);\n    if (typeof field?.access?.read === 'function') {\n      hasPermission = await field.access.read({\n        id,\n        blockData,\n        data: fullData,\n        req,\n        siblingData: data\n      });\n    } else {\n      hasPermission = true;\n    }\n    if (!hasPermission) {\n      return;\n    }\n    const validate = field.validate;\n    let validationResult = true;\n    if (typeof validate === 'function' && !skipValidation && passesCondition) {\n      let jsonError;\n      if (field.type === 'json' && typeof data[field.name] === 'string') {\n        try {\n          JSON.parse(data[field.name]);\n        } catch (e) {\n          jsonError = e;\n        }\n      }\n      try {\n        validationResult = await validate(data?.[field.name], {\n          ...field,\n          id,\n          blockData,\n          collectionSlug,\n          data: fullData,\n          event: 'onChange',\n          // @AlessioGr added `jsonError` in https://github.com/payloadcms/payload/commit/c7ea62a39473408c3ea912c4fbf73e11be4b538d\n          // @ts-expect-error-next-line\n          jsonError,\n          operation,\n          preferences,\n          previousValue: previousFormState?.[path]?.initialValue,\n          req,\n          siblingData: data\n        });\n      } catch (err) {\n        validationResult = `Error validating field at path: ${path}`;\n        req.payload.logger.error({\n          err,\n          msg: validationResult\n        });\n      }\n    }\n    const addErrorPathToParent = errorPath => {\n      if (typeof addErrorPathToParentArg === 'function') {\n        addErrorPathToParentArg(errorPath);\n      }\n      if (!fieldState.errorPaths) {\n        fieldState.errorPaths = [];\n      }\n      if (!fieldState.errorPaths.includes(errorPath)) {\n        fieldState.errorPaths.push(errorPath);\n        fieldState.valid = false;\n      }\n    };\n    if (typeof validationResult === 'string') {\n      fieldState.errorMessage = validationResult;\n      fieldState.valid = false;\n      addErrorPathToParent(path);\n    }\n    switch (field.type) {\n      case 'array':\n        {\n          const arrayValue = Array.isArray(data[field.name]) ? data[field.name] : [];\n          const arraySelect = select?.[field.name];\n          const {\n            promises,\n            rows\n          } = arrayValue.reduce((acc, row, i) => {\n            const parentPath = path + '.' + i;\n            row.id = row?.id || new ObjectId().toHexString();\n            if (!omitParents && (!filter || filter(args))) {\n              const idKey = parentPath + '.id';\n              state[idKey] = {\n                initialValue: row.id,\n                value: row.id\n              };\n              if (includeSchema) {\n                state[idKey].fieldSchema = field.fields.find(field => (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldIsID)(field));\n              }\n            }\n            acc.promises.push((0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_3__.iterateFields)({\n              id,\n              addErrorPathToParent,\n              anyParentLocalized: field.localized || anyParentLocalized,\n              blockData,\n              clientFieldSchemaMap,\n              collectionSlug,\n              data: row,\n              fields: field.fields,\n              fieldSchemaMap,\n              filter,\n              forceFullValue,\n              fullData,\n              includeSchema,\n              mockRSCs,\n              omitParents,\n              operation,\n              parentIndexPath: '',\n              parentPassesCondition: passesCondition,\n              parentPath,\n              parentSchemaPath: schemaPath,\n              permissions: fieldPermissions === true ? fieldPermissions : fieldPermissions?.fields || {},\n              preferences,\n              previousFormState,\n              renderAllFields,\n              renderFieldFn,\n              req,\n              select: typeof arraySelect === 'object' ? arraySelect : undefined,\n              selectMode,\n              skipConditionChecks,\n              skipValidation,\n              state\n            }));\n            if (!acc.rows) {\n              acc.rows = [];\n            }\n            // First, check if `previousFormState` has a matching row\n            const previousRow = (previousFormState?.[path]?.rows || []).find(prevRow => prevRow.id === row.id);\n            const newRow = {\n              id: row.id,\n              isLoading: false\n            };\n            if (previousRow?.lastRenderedPath) {\n              newRow.lastRenderedPath = previousRow.lastRenderedPath;\n            }\n            acc.rows.push(newRow);\n            const isCollapsed = (0,_isRowCollapsed_js__WEBPACK_IMPORTED_MODULE_4__.isRowCollapsed)({\n              collapsedPrefs: preferences?.fields?.[path]?.collapsed,\n              field,\n              previousRow,\n              row\n            });\n            if (isCollapsed) {\n              acc.rows[acc.rows.length - 1].collapsed = true;\n            }\n            return acc;\n          }, {\n            promises: [],\n            rows: undefined\n          });\n          // Wait for all promises and update fields with the results\n          await Promise.all(promises);\n          if (rows) {\n            fieldState.rows = rows;\n          }\n          // Add values to field state\n          if (data[field.name] !== null) {\n            fieldState.value = forceFullValue ? arrayValue : arrayValue.length;\n            fieldState.initialValue = forceFullValue ? arrayValue : arrayValue.length;\n            if (arrayValue.length > 0) {\n              fieldState.disableFormData = true;\n            }\n          }\n          // Add field to state\n          if (!omitParents && (!filter || filter(args))) {\n            state[path] = fieldState;\n          }\n          break;\n        }\n      case 'blocks':\n        {\n          const blocksValue = Array.isArray(data[field.name]) ? data[field.name] : [];\n          const {\n            promises,\n            rowMetadata\n          } = blocksValue.reduce((acc, row, i) => {\n            const blockTypeToMatch = row.blockType;\n            const block = req.payload.blocks[blockTypeToMatch] ?? (field.blockReferences ?? field.blocks).find(blockType => typeof blockType !== 'string' && blockType.slug === blockTypeToMatch);\n            if (!block) {\n              throw new Error(`Block with type \"${row.blockType}\" was found in block data, but no block with that type is defined in the config for field with schema path ${schemaPath}.`);\n            }\n            const {\n              blockSelect,\n              blockSelectMode\n            } = (0,payload__WEBPACK_IMPORTED_MODULE_1__.getBlockSelect)({\n              block,\n              select: select?.[field.name],\n              selectMode\n            });\n            const parentPath = path + '.' + i;\n            if (block) {\n              row.id = row?.id || new ObjectId().toHexString();\n              if (!omitParents && (!filter || filter(args))) {\n                // Handle block `id` field\n                const idKey = parentPath + '.id';\n                state[idKey] = {\n                  initialValue: row.id,\n                  value: row.id\n                };\n                if (includeSchema) {\n                  state[idKey].fieldSchema = includeSchema ? block.fields.find(blockField => (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldIsID)(blockField)) : undefined;\n                }\n                // Handle `blockType` field\n                const fieldKey = parentPath + '.blockType';\n                state[fieldKey] = {\n                  initialValue: row.blockType,\n                  value: row.blockType\n                };\n                if (addedByServer) {\n                  state[fieldKey].addedByServer = addedByServer;\n                }\n                if (includeSchema) {\n                  state[fieldKey].fieldSchema = block.fields.find(blockField => 'name' in blockField && blockField.name === 'blockType');\n                }\n                // Handle `blockName` field\n                const blockNameKey = parentPath + '.blockName';\n                state[blockNameKey] = {};\n                if (row.blockName) {\n                  state[blockNameKey].initialValue = row.blockName;\n                  state[blockNameKey].value = row.blockName;\n                }\n                if (includeSchema) {\n                  state[blockNameKey].fieldSchema = block.fields.find(blockField => 'name' in blockField && blockField.name === 'blockName');\n                }\n              }\n              acc.promises.push((0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_3__.iterateFields)({\n                id,\n                addErrorPathToParent,\n                anyParentLocalized: field.localized || anyParentLocalized,\n                blockData: row,\n                clientFieldSchemaMap,\n                collectionSlug,\n                data: row,\n                fields: block.fields,\n                fieldSchemaMap,\n                filter,\n                forceFullValue,\n                fullData,\n                includeSchema,\n                mockRSCs,\n                omitParents,\n                operation,\n                parentIndexPath: '',\n                parentPassesCondition: passesCondition,\n                parentPath,\n                parentSchemaPath: schemaPath + '.' + block.slug,\n                permissions: fieldPermissions === true ? fieldPermissions : parentPermissions?.[field.name]?.blocks?.[block.slug] === true ? true : parentPermissions?.[field.name]?.blocks?.[block.slug]?.fields || {},\n                preferences,\n                previousFormState,\n                renderAllFields,\n                renderFieldFn,\n                req,\n                select: typeof blockSelect === 'object' ? blockSelect : undefined,\n                selectMode: blockSelectMode,\n                skipConditionChecks,\n                skipValidation,\n                state\n              }));\n              // First, check if `previousFormState` has a matching row\n              const previousRow = (previousFormState?.[path]?.rows || []).find(prevRow => prevRow.id === row.id);\n              const newRow = {\n                id: row.id,\n                blockType: row.blockType,\n                isLoading: false\n              };\n              if (previousRow?.lastRenderedPath) {\n                newRow.lastRenderedPath = previousRow.lastRenderedPath;\n              }\n              acc.rowMetadata.push(newRow);\n              const isCollapsed = (0,_isRowCollapsed_js__WEBPACK_IMPORTED_MODULE_4__.isRowCollapsed)({\n                collapsedPrefs: preferences?.fields?.[path]?.collapsed,\n                field,\n                previousRow,\n                row\n              });\n              if (isCollapsed) {\n                acc.rowMetadata[acc.rowMetadata.length - 1].collapsed = true;\n              }\n            }\n            return acc;\n          }, {\n            promises: [],\n            rowMetadata: []\n          });\n          await Promise.all(promises);\n          // Add values to field state\n          if (data[field.name] === null) {\n            fieldState.value = null;\n            fieldState.initialValue = null;\n          } else {\n            fieldState.value = forceFullValue ? blocksValue : blocksValue.length;\n            fieldState.initialValue = forceFullValue ? blocksValue : blocksValue.length;\n            if (blocksValue.length > 0) {\n              fieldState.disableFormData = true;\n            }\n          }\n          fieldState.rows = rowMetadata;\n          // Add field to state\n          if (!omitParents && (!filter || filter(args))) {\n            state[path] = fieldState;\n          }\n          break;\n        }\n      case 'group':\n        {\n          if (!filter || filter(args)) {\n            fieldState.disableFormData = true;\n            state[path] = fieldState;\n          }\n          const groupSelect = select?.[field.name];\n          await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_3__.iterateFields)({\n            id,\n            addErrorPathToParent,\n            anyParentLocalized: field.localized || anyParentLocalized,\n            blockData,\n            clientFieldSchemaMap,\n            collectionSlug,\n            data: data?.[field.name] || {},\n            fields: field.fields,\n            fieldSchemaMap,\n            filter,\n            forceFullValue,\n            fullData,\n            includeSchema,\n            mockRSCs,\n            omitParents,\n            operation,\n            parentIndexPath: '',\n            parentPassesCondition: passesCondition,\n            parentPath: path,\n            parentSchemaPath: schemaPath,\n            permissions: typeof fieldPermissions === 'boolean' ? fieldPermissions : fieldPermissions?.fields,\n            preferences,\n            previousFormState,\n            renderAllFields,\n            renderFieldFn,\n            req,\n            select: typeof groupSelect === 'object' ? groupSelect : undefined,\n            selectMode,\n            skipConditionChecks,\n            skipValidation,\n            state\n          });\n          break;\n        }\n      case 'relationship':\n      case 'upload':\n        {\n          if (field.filterOptions) {\n            if (typeof field.filterOptions === 'object') {\n              if (typeof field.relationTo === 'string') {\n                fieldState.filterOptions = {\n                  [field.relationTo]: field.filterOptions\n                };\n              } else {\n                fieldState.filterOptions = field.relationTo.reduce((acc, relation) => {\n                  acc[relation] = field.filterOptions;\n                  return acc;\n                }, {});\n              }\n            }\n            if (typeof field.filterOptions === 'function') {\n              const query = await (0,_utilities_resolveFilterOptions_js__WEBPACK_IMPORTED_MODULE_5__.resolveFilterOptions)(field.filterOptions, {\n                id,\n                blockData,\n                data: fullData,\n                relationTo: field.relationTo,\n                req,\n                siblingData: data,\n                user: req.user\n              });\n              fieldState.filterOptions = query;\n            }\n          }\n          if (field.hasMany) {\n            const relationshipValue = Array.isArray(data[field.name]) ? data[field.name].map(relationship => {\n              if (Array.isArray(field.relationTo)) {\n                return {\n                  relationTo: relationship.relationTo,\n                  value: relationship.value && typeof relationship.value === 'object' ? relationship.value?.id : relationship.value\n                };\n              }\n              if (typeof relationship === 'object' && relationship !== null) {\n                return relationship.id;\n              }\n              return relationship;\n            }) : undefined;\n            fieldState.value = relationshipValue;\n            fieldState.initialValue = relationshipValue;\n          } else if (Array.isArray(field.relationTo)) {\n            if (data[field.name] && typeof data[field.name] === 'object' && 'relationTo' in data[field.name] && 'value' in data[field.name]) {\n              const value = typeof data[field.name]?.value === 'object' && data[field.name]?.value && 'id' in data[field.name].value ? data[field.name].value.id : data[field.name].value;\n              const relationshipValue = {\n                relationTo: data[field.name]?.relationTo,\n                value\n              };\n              fieldState.value = relationshipValue;\n              fieldState.initialValue = relationshipValue;\n            }\n          } else {\n            const relationshipValue = data[field.name] && typeof data[field.name] === 'object' && 'id' in data[field.name] ? data[field.name].id : data[field.name];\n            fieldState.value = relationshipValue;\n            fieldState.initialValue = relationshipValue;\n          }\n          if (!filter || filter(args)) {\n            state[path] = fieldState;\n          }\n          break;\n        }\n      case 'select':\n        {\n          if (typeof field.filterOptions === 'function') {\n            fieldState.selectFilterOptions = field.filterOptions({\n              data: fullData,\n              options: field.options,\n              req,\n              siblingData: data\n            });\n          }\n          if (data[field.name] !== undefined) {\n            fieldState.value = data[field.name];\n            fieldState.initialValue = data[field.name];\n          }\n          if (!filter || filter(args)) {\n            state[path] = fieldState;\n          }\n          break;\n        }\n      default:\n        {\n          if (data[field.name] !== undefined) {\n            fieldState.value = data[field.name];\n            fieldState.initialValue = data[field.name];\n          }\n          // Add field to state\n          if (!filter || filter(args)) {\n            state[path] = fieldState;\n          }\n          break;\n        }\n    }\n  } else if ((0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldHasSubFields)(field) && !(0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldAffectsData)(field)) {\n    // Handle field types that do not use names (row, collapsible, unnamed group etc)\n    if (!filter || filter(args)) {\n      state[path] = {\n        disableFormData: true\n      };\n      if (passesCondition === false) {\n        state[path].passesCondition = false;\n      }\n    }\n    await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_3__.iterateFields)({\n      id,\n      mockRSCs,\n      select,\n      selectMode,\n      // passthrough parent functionality\n      addErrorPathToParent: addErrorPathToParentArg,\n      anyParentLocalized: (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldIsLocalized)(field) || anyParentLocalized,\n      blockData,\n      clientFieldSchemaMap,\n      collectionSlug,\n      data,\n      fields: field.fields,\n      fieldSchemaMap,\n      filter,\n      forceFullValue,\n      fullData,\n      includeSchema,\n      omitParents,\n      operation,\n      parentIndexPath: indexPath,\n      parentPassesCondition: passesCondition,\n      parentPath,\n      parentSchemaPath,\n      permissions: parentPermissions,\n      preferences,\n      previousFormState,\n      renderAllFields,\n      renderFieldFn,\n      req,\n      skipConditionChecks,\n      skipValidation,\n      state\n    });\n  } else if (field.type === 'tabs') {\n    const promises = field.tabs.map((tab, tabIndex) => {\n      const isNamedTab = (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.tabHasName)(tab);\n      let tabSelect;\n      const {\n        indexPath: tabIndexPath,\n        path: tabPath,\n        schemaPath: tabSchemaPath\n      } = (0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.getFieldPaths)({\n        field: {\n          ...tab,\n          type: 'tab'\n        },\n        index: tabIndex,\n        parentIndexPath: indexPath,\n        parentPath,\n        parentSchemaPath\n      });\n      let childPermissions = undefined;\n      if (isNamedTab) {\n        if (parentPermissions === true) {\n          childPermissions = true;\n        } else {\n          const tabPermissions = parentPermissions?.[tab.name];\n          if (tabPermissions === true) {\n            childPermissions = true;\n          } else {\n            childPermissions = tabPermissions?.fields;\n          }\n        }\n        if (typeof select?.[tab.name] === 'object') {\n          tabSelect = select?.[tab.name];\n        }\n      } else {\n        childPermissions = parentPermissions;\n        tabSelect = select;\n      }\n      const pathSegments = path ? path.split('.') : [];\n      // If passesCondition is false then this should always result to false\n      // If the tab has no admin.condition provided then fallback to passesCondition and let that decide the result\n      let tabPassesCondition = passesCondition;\n      if (passesCondition && typeof tab.admin?.condition === 'function') {\n        tabPassesCondition = tab.admin.condition(fullData, data, {\n          blockData,\n          operation,\n          path: pathSegments,\n          user: req.user\n        });\n      }\n      if (tab?.id) {\n        state[tab.id] = {\n          passesCondition: tabPassesCondition\n        };\n      }\n      return (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_3__.iterateFields)({\n        id,\n        addErrorPathToParent: addErrorPathToParentArg,\n        anyParentLocalized: tab.localized || anyParentLocalized,\n        blockData,\n        clientFieldSchemaMap,\n        collectionSlug,\n        data: isNamedTab ? data?.[tab.name] || {} : data,\n        fields: tab.fields,\n        fieldSchemaMap,\n        filter,\n        forceFullValue,\n        fullData,\n        includeSchema,\n        mockRSCs,\n        omitParents,\n        operation,\n        parentIndexPath: isNamedTab ? '' : tabIndexPath,\n        parentPassesCondition: tabPassesCondition,\n        parentPath: isNamedTab ? tabPath : parentPath,\n        parentSchemaPath: isNamedTab ? tabSchemaPath : parentSchemaPath,\n        permissions: childPermissions,\n        preferences,\n        previousFormState,\n        renderAllFields,\n        renderFieldFn,\n        req,\n        select: tabSelect,\n        selectMode,\n        skipConditionChecks,\n        skipValidation,\n        state\n      });\n    });\n    await Promise.all(promises);\n  } else if (field.type === 'ui') {\n    if (!filter || filter(args)) {\n      state[path] = fieldState;\n      state[path].disableFormData = true;\n    }\n  }\n  if (renderFieldFn && !(0,payload_shared__WEBPACK_IMPORTED_MODULE_2__.fieldIsHiddenOrDisabled)(field)) {\n    const fieldConfig = fieldSchemaMap.get(schemaPath);\n    if (!fieldConfig && !mockRSCs) {\n      if (schemaPath.endsWith('.blockType')) {\n        return;\n      } else {\n        throw new Error(`Field config not found for ${schemaPath}`);\n      }\n    }\n    if (!state[path]) {\n      // Some fields (ie `Tab`) do not live in form state\n      // therefore we cannot attach customComponents to them\n      return;\n    }\n    if (addedByServer) {\n      state[path].addedByServer = addedByServer;\n    }\n    renderFieldFn({\n      id,\n      clientFieldSchemaMap,\n      collectionSlug,\n      data: fullData,\n      fieldConfig: fieldConfig,\n      fieldSchemaMap,\n      fieldState: state[path],\n      formState: state,\n      indexPath,\n      lastRenderedPath,\n      mockRSCs,\n      operation,\n      parentPath,\n      parentSchemaPath,\n      path,\n      permissions: fieldPermissions,\n      preferences,\n      previousFieldState: previousFormState?.[path],\n      renderAllFields,\n      req,\n      schemaPath,\n      siblingData: data\n    });\n  }\n};\n//# sourceMappingURL=addFieldStatePromise.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/addFieldStatePromise.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/index.js":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/index.js ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDefaultValues: () => (/* binding */ calculateDefaultValues)\n/* harmony export */ });\n/* harmony import */ var _iterateFields_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./iterateFields.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/iterateFields.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_iterateFields_js__WEBPACK_IMPORTED_MODULE_0__]);\n_iterateFields_js__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst calculateDefaultValues = async ({\n  id,\n  data,\n  fields,\n  locale,\n  req,\n  select,\n  selectMode,\n  user\n}) => {\n  await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_0__.iterateFields)({\n    id,\n    data,\n    fields,\n    locale,\n    req,\n    select,\n    selectMode,\n    siblingData: data,\n    user\n  });\n  return data;\n};\n//# sourceMappingURL=index.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHBheWxvYWRjbXMrdWlAMy40Ni4wX0B0eXBlXzNhMDIyY2JjYTk3ZjU0OGY2MzJhYjc3Yzc2NjE1MzYyL25vZGVfbW9kdWxlcy9AcGF5bG9hZGNtcy91aS9kaXN0L2Zvcm1zL2ZpZWxkU2NoZW1hc1RvRm9ybVN0YXRlL2NhbGN1bGF0ZURlZmF1bHRWYWx1ZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBbUQ7QUFDNUM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELFFBQVEsZ0VBQWE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxpQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqaW1teVxcRGVza3RvcFxcbm9yZC1jb2FzdFxcYmFja2VuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHBheWxvYWRjbXMrdWlAMy40Ni4wX0B0eXBlXzNhMDIyY2JjYTk3ZjU0OGY2MzJhYjc3Yzc2NjE1MzYyXFxub2RlX21vZHVsZXNcXEBwYXlsb2FkY21zXFx1aVxcZGlzdFxcZm9ybXNcXGZpZWxkU2NoZW1hc1RvRm9ybVN0YXRlXFxjYWxjdWxhdGVEZWZhdWx0VmFsdWVzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpdGVyYXRlRmllbGRzIH0gZnJvbSAnLi9pdGVyYXRlRmllbGRzLmpzJztcbmV4cG9ydCBjb25zdCBjYWxjdWxhdGVEZWZhdWx0VmFsdWVzID0gYXN5bmMgKHtcbiAgaWQsXG4gIGRhdGEsXG4gIGZpZWxkcyxcbiAgbG9jYWxlLFxuICByZXEsXG4gIHNlbGVjdCxcbiAgc2VsZWN0TW9kZSxcbiAgdXNlclxufSkgPT4ge1xuICBhd2FpdCBpdGVyYXRlRmllbGRzKHtcbiAgICBpZCxcbiAgICBkYXRhLFxuICAgIGZpZWxkcyxcbiAgICBsb2NhbGUsXG4gICAgcmVxLFxuICAgIHNlbGVjdCxcbiAgICBzZWxlY3RNb2RlLFxuICAgIHNpYmxpbmdEYXRhOiBkYXRhLFxuICAgIHVzZXJcbiAgfSk7XG4gIHJldHVybiBkYXRhO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/iterateFields.js":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/iterateFields.js ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iterateFields: () => (/* binding */ iterateFields)\n/* harmony export */ });\n/* harmony import */ var _promise_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./promise.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/promise.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_promise_js__WEBPACK_IMPORTED_MODULE_0__]);\n_promise_js__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst iterateFields = async ({\n  id,\n  data,\n  fields,\n  locale,\n  req,\n  select,\n  selectMode,\n  siblingData,\n  user\n}) => {\n  const promises = [];\n  fields.forEach(field => {\n    promises.push((0,_promise_js__WEBPACK_IMPORTED_MODULE_0__.defaultValuePromise)({\n      id,\n      data,\n      field,\n      locale,\n      req,\n      select,\n      selectMode,\n      siblingData,\n      user\n    }));\n  });\n  await Promise.all(promises);\n};\n//# sourceMappingURL=iterateFields.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHBheWxvYWRjbXMrdWlAMy40Ni4wX0B0eXBlXzNhMDIyY2JjYTk3ZjU0OGY2MzJhYjc3Yzc2NjE1MzYyL25vZGVfbW9kdWxlcy9AcGF5bG9hZGNtcy91aS9kaXN0L2Zvcm1zL2ZpZWxkU2NoZW1hc1RvRm9ybVN0YXRlL2NhbGN1bGF0ZURlZmF1bHRWYWx1ZXMvaXRlcmF0ZUZpZWxkcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFtRDtBQUM1QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0Esa0JBQWtCLGdFQUFtQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIO0FBQ0E7QUFDQSx5QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqaW1teVxcRGVza3RvcFxcbm9yZC1jb2FzdFxcYmFja2VuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHBheWxvYWRjbXMrdWlAMy40Ni4wX0B0eXBlXzNhMDIyY2JjYTk3ZjU0OGY2MzJhYjc3Yzc2NjE1MzYyXFxub2RlX21vZHVsZXNcXEBwYXlsb2FkY21zXFx1aVxcZGlzdFxcZm9ybXNcXGZpZWxkU2NoZW1hc1RvRm9ybVN0YXRlXFxjYWxjdWxhdGVEZWZhdWx0VmFsdWVzXFxpdGVyYXRlRmllbGRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRlZmF1bHRWYWx1ZVByb21pc2UgfSBmcm9tICcuL3Byb21pc2UuanMnO1xuZXhwb3J0IGNvbnN0IGl0ZXJhdGVGaWVsZHMgPSBhc3luYyAoe1xuICBpZCxcbiAgZGF0YSxcbiAgZmllbGRzLFxuICBsb2NhbGUsXG4gIHJlcSxcbiAgc2VsZWN0LFxuICBzZWxlY3RNb2RlLFxuICBzaWJsaW5nRGF0YSxcbiAgdXNlclxufSkgPT4ge1xuICBjb25zdCBwcm9taXNlcyA9IFtdO1xuICBmaWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7XG4gICAgcHJvbWlzZXMucHVzaChkZWZhdWx0VmFsdWVQcm9taXNlKHtcbiAgICAgIGlkLFxuICAgICAgZGF0YSxcbiAgICAgIGZpZWxkLFxuICAgICAgbG9jYWxlLFxuICAgICAgcmVxLFxuICAgICAgc2VsZWN0LFxuICAgICAgc2VsZWN0TW9kZSxcbiAgICAgIHNpYmxpbmdEYXRhLFxuICAgICAgdXNlclxuICAgIH0pKTtcbiAgfSk7XG4gIGF3YWl0IFByb21pc2UuYWxsKHByb21pc2VzKTtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pdGVyYXRlRmllbGRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/iterateFields.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/promise.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/promise.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultValuePromise: () => (/* binding */ defaultValuePromise)\n/* harmony export */ });\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var payload_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! payload/shared */ \"payload/shared\");\n/* harmony import */ var _iterateFields_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./iterateFields.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/iterateFields.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_2__]);\n([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// TODO: Make this works for rich text subfields\nconst defaultValuePromise = async ({\n  id,\n  data,\n  field,\n  locale,\n  req,\n  select,\n  selectMode,\n  siblingData,\n  user\n}) => {\n  const shouldContinue = (0,payload__WEBPACK_IMPORTED_MODULE_0__.stripUnselectedFields)({\n    field,\n    select,\n    selectMode,\n    siblingDoc: siblingData\n  });\n  if (!shouldContinue) {\n    return;\n  }\n  if ((0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.fieldAffectsData)(field)) {\n    if (typeof siblingData[field.name] === 'undefined' && typeof field.defaultValue !== 'undefined') {\n      try {\n        siblingData[field.name] = await (0,payload__WEBPACK_IMPORTED_MODULE_0__.getDefaultValue)({\n          defaultValue: field.defaultValue,\n          locale,\n          req,\n          user,\n          value: siblingData[field.name]\n        });\n      } catch (err) {\n        req.payload.logger.error({\n          err,\n          msg: `Error calculating default value for field: ${field.name}`\n        });\n      }\n    }\n  }\n  // Traverse subfields\n  switch (field.type) {\n    case 'array':\n      {\n        const rows = siblingData[field.name];\n        if (Array.isArray(rows)) {\n          const promises = [];\n          const arraySelect = select?.[field.name];\n          rows.forEach(row => {\n            promises.push((0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n              id,\n              data,\n              fields: field.fields,\n              locale,\n              req,\n              select: typeof arraySelect === 'object' ? arraySelect : undefined,\n              selectMode,\n              siblingData: row,\n              user\n            }));\n          });\n          await Promise.all(promises);\n        }\n        break;\n      }\n    case 'blocks':\n      {\n        const rows = siblingData[field.name];\n        if (Array.isArray(rows)) {\n          const promises = [];\n          rows.forEach(row => {\n            const blockTypeToMatch = row.blockType;\n            const block = req.payload.blocks[blockTypeToMatch] ?? (field.blockReferences ?? field.blocks).find(blockType => typeof blockType !== 'string' && blockType.slug === blockTypeToMatch);\n            const {\n              blockSelect,\n              blockSelectMode\n            } = (0,payload__WEBPACK_IMPORTED_MODULE_0__.getBlockSelect)({\n              block,\n              select: select?.[field.name],\n              selectMode\n            });\n            if (block) {\n              row.blockType = blockTypeToMatch;\n              promises.push((0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n                id,\n                data,\n                fields: block.fields,\n                locale,\n                req,\n                select: typeof blockSelect === 'object' ? blockSelect : undefined,\n                selectMode: blockSelectMode,\n                siblingData: row,\n                user\n              }));\n            }\n          });\n          await Promise.all(promises);\n        }\n        break;\n      }\n    case 'collapsible':\n    case 'row':\n      {\n        await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n          id,\n          data,\n          fields: field.fields,\n          locale,\n          req,\n          select,\n          selectMode,\n          siblingData,\n          user\n        });\n        break;\n      }\n    case 'group':\n      {\n        if ((0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.fieldAffectsData)(field)) {\n          if (typeof siblingData[field.name] !== 'object') {\n            siblingData[field.name] = {};\n          }\n          const groupData = siblingData[field.name];\n          const groupSelect = select?.[field.name];\n          await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n            id,\n            data,\n            fields: field.fields,\n            locale,\n            req,\n            select: typeof groupSelect === 'object' ? groupSelect : undefined,\n            selectMode,\n            siblingData: groupData,\n            user\n          });\n        } else {\n          await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n            id,\n            data,\n            fields: field.fields,\n            locale,\n            req,\n            select,\n            selectMode,\n            siblingData,\n            user\n          });\n        }\n        break;\n      }\n    case 'tab':\n      {\n        let tabSiblingData;\n        const isNamedTab = (0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.tabHasName)(field);\n        let tabSelect;\n        if (isNamedTab) {\n          if (typeof siblingData[field.name] !== 'object') {\n            siblingData[field.name] = {};\n          }\n          tabSiblingData = siblingData[field.name];\n          if (typeof select?.[field.name] === 'object') {\n            tabSelect = select?.[field.name];\n          }\n        } else {\n          tabSiblingData = siblingData;\n          tabSelect = select;\n        }\n        await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n          id,\n          data,\n          fields: field.fields,\n          locale,\n          req,\n          select: tabSelect,\n          selectMode,\n          siblingData: tabSiblingData,\n          user\n        });\n        break;\n      }\n    case 'tabs':\n      {\n        await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_2__.iterateFields)({\n          id,\n          data,\n          fields: field.tabs.map(tab => ({\n            ...tab,\n            type: 'tab'\n          })),\n          locale,\n          req,\n          select,\n          selectMode,\n          siblingData,\n          user\n        });\n        break;\n      }\n    default:\n      {\n        break;\n      }\n  }\n};\n//# sourceMappingURL=promise.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/promise.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/index.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/index.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fieldSchemasToFormState: () => (/* binding */ fieldSchemasToFormState),\n/* harmony export */   iterateFields: () => (/* reexport safe */ _iterateFields_js__WEBPACK_IMPORTED_MODULE_1__.iterateFields)\n/* harmony export */ });\n/* harmony import */ var _calculateDefaultValues_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./calculateDefaultValues/index.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/calculateDefaultValues/index.js\");\n/* harmony import */ var _iterateFields_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./iterateFields.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/iterateFields.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_calculateDefaultValues_index_js__WEBPACK_IMPORTED_MODULE_0__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_1__]);\n([_calculateDefaultValues_index_js__WEBPACK_IMPORTED_MODULE_0__, _iterateFields_js__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst fieldSchemasToFormState = async ({\n  id,\n  clientFieldSchemaMap,\n  collectionSlug,\n  data = {},\n  documentData,\n  fields,\n  fieldSchemaMap,\n  initialBlockData,\n  mockRSCs,\n  operation,\n  permissions,\n  preferences,\n  previousFormState,\n  renderAllFields,\n  renderFieldFn,\n  req,\n  schemaPath,\n  select,\n  selectMode,\n  skipValidation\n}) => {\n  if (!clientFieldSchemaMap && renderFieldFn) {\n    console.warn('clientFieldSchemaMap is not passed to fieldSchemasToFormState - this will reduce performance');\n  }\n  if (fields && fields.length) {\n    const state = {};\n    const dataWithDefaultValues = {\n      ...data\n    };\n    await (0,_calculateDefaultValues_index_js__WEBPACK_IMPORTED_MODULE_0__.calculateDefaultValues)({\n      id,\n      data: dataWithDefaultValues,\n      fields,\n      locale: req.locale,\n      req,\n      select,\n      selectMode,\n      siblingData: dataWithDefaultValues,\n      user: req.user\n    });\n    let fullData = dataWithDefaultValues;\n    if (documentData) {\n      // By the time this function is used to get form state for nested forms, their default values should have already been calculated\n      // => no need to run calculateDefaultValues here\n      fullData = documentData;\n    }\n    await (0,_iterateFields_js__WEBPACK_IMPORTED_MODULE_1__.iterateFields)({\n      id,\n      addErrorPathToParent: null,\n      blockData: initialBlockData,\n      clientFieldSchemaMap,\n      collectionSlug,\n      data: dataWithDefaultValues,\n      fields,\n      fieldSchemaMap,\n      fullData,\n      mockRSCs,\n      operation,\n      parentIndexPath: '',\n      parentPassesCondition: true,\n      parentPath: '',\n      parentSchemaPath: schemaPath,\n      permissions,\n      preferences,\n      previousFormState,\n      renderAllFields,\n      renderFieldFn,\n      req,\n      select,\n      selectMode,\n      skipValidation,\n      state\n    });\n    return state;\n  }\n  return {};\n};\n\n//# sourceMappingURL=index.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/isRowCollapsed.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/isRowCollapsed.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isRowCollapsed: () => (/* binding */ isRowCollapsed)\n/* harmony export */ });\nfunction isRowCollapsed({\n  collapsedPrefs,\n  field,\n  previousRow,\n  row\n}) {\n  if (previousRow && 'collapsed' in previousRow) {\n    return previousRow.collapsed ?? false;\n  }\n  // If previousFormState is `undefined`, check preferences\n  if (collapsedPrefs !== undefined) {\n    return collapsedPrefs.includes(row.id) // Check if collapsed in preferences\n    ;\n  }\n  // If neither exists, fallback to `field.admin.initCollapsed`\n  return field.admin.initCollapsed;\n}\n//# sourceMappingURL=isRowCollapsed.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHBheWxvYWRjbXMrdWlAMy40Ni4wX0B0eXBlXzNhMDIyY2JjYTk3ZjU0OGY2MzJhYjc3Yzc2NjE1MzYyL25vZGVfbW9kdWxlcy9AcGF5bG9hZGNtcy91aS9kaXN0L2Zvcm1zL2ZpZWxkU2NoZW1hc1RvRm9ybVN0YXRlL2lzUm93Q29sbGFwc2VkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqaW1teVxcRGVza3RvcFxcbm9yZC1jb2FzdFxcYmFja2VuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHBheWxvYWRjbXMrdWlAMy40Ni4wX0B0eXBlXzNhMDIyY2JjYTk3ZjU0OGY2MzJhYjc3Yzc2NjE1MzYyXFxub2RlX21vZHVsZXNcXEBwYXlsb2FkY21zXFx1aVxcZGlzdFxcZm9ybXNcXGZpZWxkU2NoZW1hc1RvRm9ybVN0YXRlXFxpc1Jvd0NvbGxhcHNlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaXNSb3dDb2xsYXBzZWQoe1xuICBjb2xsYXBzZWRQcmVmcyxcbiAgZmllbGQsXG4gIHByZXZpb3VzUm93LFxuICByb3dcbn0pIHtcbiAgaWYgKHByZXZpb3VzUm93ICYmICdjb2xsYXBzZWQnIGluIHByZXZpb3VzUm93KSB7XG4gICAgcmV0dXJuIHByZXZpb3VzUm93LmNvbGxhcHNlZCA/PyBmYWxzZTtcbiAgfVxuICAvLyBJZiBwcmV2aW91c0Zvcm1TdGF0ZSBpcyBgdW5kZWZpbmVkYCwgY2hlY2sgcHJlZmVyZW5jZXNcbiAgaWYgKGNvbGxhcHNlZFByZWZzICE9PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gY29sbGFwc2VkUHJlZnMuaW5jbHVkZXMocm93LmlkKSAvLyBDaGVjayBpZiBjb2xsYXBzZWQgaW4gcHJlZmVyZW5jZXNcbiAgICA7XG4gIH1cbiAgLy8gSWYgbmVpdGhlciBleGlzdHMsIGZhbGxiYWNrIHRvIGBmaWVsZC5hZG1pbi5pbml0Q29sbGFwc2VkYFxuICByZXR1cm4gZmllbGQuYWRtaW4uaW5pdENvbGxhcHNlZDtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWlzUm93Q29sbGFwc2VkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/isRowCollapsed.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/iterateFields.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/iterateFields.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iterateFields: () => (/* binding */ iterateFields)\n/* harmony export */ });\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var payload_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! payload/shared */ \"payload/shared\");\n/* harmony import */ var _addFieldStatePromise_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./addFieldStatePromise.js */ \"(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/addFieldStatePromise.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__, _addFieldStatePromise_js__WEBPACK_IMPORTED_MODULE_2__]);\n([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__, _addFieldStatePromise_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n/**\n * Flattens the fields schema and fields data\n */\nconst iterateFields = async ({\n  id,\n  addErrorPathToParent: addErrorPathToParentArg,\n  anyParentLocalized = false,\n  blockData,\n  clientFieldSchemaMap,\n  collectionSlug,\n  data,\n  fields,\n  fieldSchemaMap,\n  filter,\n  forceFullValue = false,\n  fullData,\n  includeSchema = false,\n  mockRSCs,\n  omitParents = false,\n  operation,\n  parentIndexPath,\n  parentPassesCondition = true,\n  parentPath,\n  parentSchemaPath,\n  permissions,\n  preferences,\n  previousFormState,\n  renderAllFields,\n  renderFieldFn: renderFieldFn,\n  req,\n  select,\n  selectMode,\n  skipConditionChecks = false,\n  skipValidation = false,\n  state = {}\n}) => {\n  const promises = [];\n  fields.forEach((field, fieldIndex) => {\n    let passesCondition = true;\n    const {\n      indexPath,\n      path,\n      schemaPath\n    } = (0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.getFieldPaths)({\n      field,\n      index: fieldIndex,\n      parentIndexPath: 'name' in field ? '' : parentIndexPath,\n      parentPath,\n      parentSchemaPath\n    });\n    if (path !== 'id') {\n      const shouldContinue = (0,payload__WEBPACK_IMPORTED_MODULE_0__.stripUnselectedFields)({\n        field,\n        select,\n        selectMode,\n        siblingDoc: data\n      });\n      if (!shouldContinue) {\n        return;\n      }\n    }\n    const pathSegments = path ? path.split('.') : [];\n    if (!skipConditionChecks) {\n      try {\n        passesCondition = Boolean((field?.admin?.condition ? Boolean(field.admin.condition(fullData || {}, data || {}, {\n          blockData,\n          operation,\n          path: pathSegments,\n          user: req.user\n        })) : true) && parentPassesCondition);\n      } catch (err) {\n        passesCondition = false;\n        req.payload.logger.error({\n          err,\n          msg: `Error evaluating field condition at path: ${path}`\n        });\n      }\n    }\n    promises.push((0,_addFieldStatePromise_js__WEBPACK_IMPORTED_MODULE_2__.addFieldStatePromise)({\n      id,\n      addErrorPathToParent: addErrorPathToParentArg,\n      anyParentLocalized,\n      blockData,\n      clientFieldSchemaMap,\n      collectionSlug,\n      data,\n      field,\n      fieldIndex,\n      fieldSchemaMap,\n      filter,\n      forceFullValue,\n      fullData,\n      includeSchema,\n      indexPath,\n      mockRSCs,\n      omitParents,\n      operation,\n      parentIndexPath,\n      parentPath,\n      parentPermissions: permissions,\n      parentSchemaPath,\n      passesCondition,\n      path,\n      preferences,\n      previousFormState,\n      renderAllFields,\n      renderFieldFn,\n      req,\n      schemaPath,\n      select,\n      selectMode,\n      skipConditionChecks,\n      skipValidation,\n      state\n    }));\n  });\n  await Promise.all(promises);\n};\n//# sourceMappingURL=iterateFields.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/forms/fieldSchemasToFormState/iterateFields.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/utilities/buildFieldSchemaMap/traverseFields.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/utilities/buildFieldSchemaMap/traverseFields.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   traverseFields: () => (/* binding */ traverseFields)\n/* harmony export */ });\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var payload_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! payload/shared */ \"payload/shared\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__]);\n([payload__WEBPACK_IMPORTED_MODULE_0__, payload_shared__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst traverseFields = ({\n  config,\n  fields,\n  i18n,\n  parentIndexPath,\n  parentSchemaPath,\n  schemaMap\n}) => {\n  for (const [index, field] of fields.entries()) {\n    const {\n      indexPath,\n      schemaPath\n    } = (0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.getFieldPaths)({\n      field,\n      index,\n      parentIndexPath: 'name' in field ? '' : parentIndexPath,\n      parentPath: '',\n      parentSchemaPath\n    });\n    schemaMap.set(schemaPath, field);\n    switch (field.type) {\n      case 'array':\n        traverseFields({\n          config,\n          fields: field.fields,\n          i18n,\n          parentIndexPath: '',\n          parentSchemaPath: schemaPath,\n          schemaMap\n        });\n        break;\n      case 'blocks':\n        ;\n        (field.blockReferences ?? field.blocks).map(_block => {\n          // TODO: iterate over blocks mapped to block slug in v4, or pass through payload.blocks\n          const block = typeof _block === 'string' ? config.blocks.find(b => b.slug === _block) : _block;\n          const blockSchemaPath = `${schemaPath}.${block.slug}`;\n          schemaMap.set(blockSchemaPath, block);\n          traverseFields({\n            config,\n            fields: block.fields,\n            i18n,\n            parentIndexPath: '',\n            parentSchemaPath: blockSchemaPath,\n            schemaMap\n          });\n        });\n        break;\n      case 'collapsible':\n      case 'row':\n        traverseFields({\n          config,\n          fields: field.fields,\n          i18n,\n          parentIndexPath: indexPath,\n          parentSchemaPath,\n          schemaMap\n        });\n        break;\n      case 'group':\n        if ((0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.fieldAffectsData)(field)) {\n          traverseFields({\n            config,\n            fields: field.fields,\n            i18n,\n            parentIndexPath: '',\n            parentSchemaPath: schemaPath,\n            schemaMap\n          });\n        } else {\n          traverseFields({\n            config,\n            fields: field.fields,\n            i18n,\n            parentIndexPath: indexPath,\n            parentSchemaPath,\n            schemaMap\n          });\n        }\n        break;\n      case 'richText':\n        if (!field?.editor) {\n          throw new payload__WEBPACK_IMPORTED_MODULE_0__.MissingEditorProp(field) // while we allow disabling editor functionality, you should not have any richText fields defined if you do not have an editor\n          ;\n        }\n        if (typeof field.editor === 'function') {\n          throw new Error('Attempted to access unsanitized rich text editor.');\n        }\n        if (typeof field.editor.generateSchemaMap === 'function') {\n          field.editor.generateSchemaMap({\n            config,\n            field,\n            i18n,\n            schemaMap,\n            schemaPath\n          });\n        }\n        break;\n      case 'tabs':\n        field.tabs.map((tab, tabIndex) => {\n          const isNamedTab = (0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.tabHasName)(tab);\n          const {\n            indexPath: tabIndexPath,\n            schemaPath: tabSchemaPath\n          } = (0,payload_shared__WEBPACK_IMPORTED_MODULE_1__.getFieldPaths)({\n            field: {\n              ...tab,\n              type: 'tab'\n            },\n            index: tabIndex,\n            parentIndexPath: indexPath,\n            parentPath: '',\n            parentSchemaPath\n          });\n          schemaMap.set(tabSchemaPath, tab);\n          traverseFields({\n            config,\n            fields: tab.fields,\n            i18n,\n            parentIndexPath: isNamedTab ? '' : tabIndexPath,\n            parentSchemaPath: isNamedTab ? tabSchemaPath : parentSchemaPath,\n            schemaMap\n          });\n        });\n        break;\n    }\n  }\n};\n//# sourceMappingURL=traverseFields.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/utilities/buildFieldSchemaMap/traverseFields.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/utilities/resolveFilterOptions.js":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/utilities/resolveFilterOptions.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveFilterOptions: () => (/* binding */ resolveFilterOptions)\n/* harmony export */ });\nconst resolveFilterOptions = async (filterOptions, options) => {\n  const {\n    relationTo\n  } = options;\n  const relations = Array.isArray(relationTo) ? relationTo : [relationTo];\n  const query = {};\n  if (typeof filterOptions !== 'undefined') {\n    await Promise.all(relations.map(async relation => {\n      query[relation] = typeof filterOptions === 'function' ? await filterOptions({\n        ...options,\n        relationTo: relation\n      }) : filterOptions;\n      if (query[relation] === true) {\n        query[relation] = {};\n      }\n      // this is an ugly way to prevent results from being returned\n      if (query[relation] === false) {\n        query[relation] = {\n          id: {\n            exists: false\n          }\n        };\n      }\n    }));\n  }\n  return query;\n};\n//# sourceMappingURL=resolveFilterOptions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHBheWxvYWRjbXMrdWlAMy40Ni4wX0B0eXBlXzNhMDIyY2JjYTk3ZjU0OGY2MzJhYjc3Yzc2NjE1MzYyL25vZGVfbW9kdWxlcy9AcGF5bG9hZGNtcy91aS9kaXN0L3V0aWxpdGllcy9yZXNvbHZlRmlsdGVyT3B0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamltbXlcXERlc2t0b3BcXG5vcmQtY29hc3RcXGJhY2tlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBwYXlsb2FkY21zK3VpQDMuNDYuMF9AdHlwZV8zYTAyMmNiY2E5N2Y1NDhmNjMyYWI3N2M3NjYxNTM2Mlxcbm9kZV9tb2R1bGVzXFxAcGF5bG9hZGNtc1xcdWlcXGRpc3RcXHV0aWxpdGllc1xccmVzb2x2ZUZpbHRlck9wdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHJlc29sdmVGaWx0ZXJPcHRpb25zID0gYXN5bmMgKGZpbHRlck9wdGlvbnMsIG9wdGlvbnMpID0+IHtcbiAgY29uc3Qge1xuICAgIHJlbGF0aW9uVG9cbiAgfSA9IG9wdGlvbnM7XG4gIGNvbnN0IHJlbGF0aW9ucyA9IEFycmF5LmlzQXJyYXkocmVsYXRpb25UbykgPyByZWxhdGlvblRvIDogW3JlbGF0aW9uVG9dO1xuICBjb25zdCBxdWVyeSA9IHt9O1xuICBpZiAodHlwZW9mIGZpbHRlck9wdGlvbnMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgYXdhaXQgUHJvbWlzZS5hbGwocmVsYXRpb25zLm1hcChhc3luYyByZWxhdGlvbiA9PiB7XG4gICAgICBxdWVyeVtyZWxhdGlvbl0gPSB0eXBlb2YgZmlsdGVyT3B0aW9ucyA9PT0gJ2Z1bmN0aW9uJyA/IGF3YWl0IGZpbHRlck9wdGlvbnMoe1xuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICByZWxhdGlvblRvOiByZWxhdGlvblxuICAgICAgfSkgOiBmaWx0ZXJPcHRpb25zO1xuICAgICAgaWYgKHF1ZXJ5W3JlbGF0aW9uXSA9PT0gdHJ1ZSkge1xuICAgICAgICBxdWVyeVtyZWxhdGlvbl0gPSB7fTtcbiAgICAgIH1cbiAgICAgIC8vIHRoaXMgaXMgYW4gdWdseSB3YXkgdG8gcHJldmVudCByZXN1bHRzIGZyb20gYmVpbmcgcmV0dXJuZWRcbiAgICAgIGlmIChxdWVyeVtyZWxhdGlvbl0gPT09IGZhbHNlKSB7XG4gICAgICAgIHF1ZXJ5W3JlbGF0aW9uXSA9IHtcbiAgICAgICAgICBpZDoge1xuICAgICAgICAgICAgZXhpc3RzOiBmYWxzZVxuICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9KSk7XG4gIH1cbiAgcmV0dXJuIHF1ZXJ5O1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlc29sdmVGaWx0ZXJPcHRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@payloadcms+ui@3.46.0_@type_3a022cbca97f548f632ab77c76615362/node_modules/@payloadcms/ui/dist/utilities/resolveFilterOptions.js\n");

/***/ })

};
;