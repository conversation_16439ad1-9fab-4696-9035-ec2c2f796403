import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { billFormSchema, billFilterSchema } from '@/lib/validation/billing-schemas';
import { formatValidationErrors } from '@/lib/validation/billing-schemas';

const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';

// Enhanced error handling utility
class APIError extends Error {
  constructor(
    message: string,
    public status: number = 500,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// Utility to get user info from Clerk with error handling
async function getClerkUserInfo(userId: string) {
  try {
    const response = await fetch(`https://api.clerk.com/v1/users/${userId}`, {
      headers: {
        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,
      },
    });

    if (!response.ok) {
      throw new APIError('Failed to fetch user information', 401, 'CLERK_USER_FETCH_ERROR');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching Clerk user:', error);
    throw new APIError('Authentication service unavailable', 503, 'AUTH_SERVICE_ERROR');
  }
}

// Utility to make backend requests with comprehensive error handling
async function makeBackendRequest(url: string, options: RequestInit, userId: string, userEmail: string) {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'x-clerk-user-id': userId,
        'x-user-email': userEmail,
        ...options.headers,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new APIError(
        data.error || `Backend request failed: ${response.status}`,
        response.status,
        data.code || 'BACKEND_ERROR',
        data
      );
    }

    return data;
  } catch (error) {
    if (error instanceof APIError) {
      throw error;
    }

    console.error('Backend request error:', error);
    throw new APIError('Backend service unavailable', 503, 'BACKEND_SERVICE_ERROR');
  }
}

/**
 * GET /api/bills - Proxy to backend bills API with enhanced validation and error handling
 */
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        {
          error: 'Authentication required',
          code: 'AUTH_REQUIRED',
          message: '请先登录以访问账单数据'
        },
        { status: 401 }
      );
    }

    // Validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());

    // Basic query parameter validation
    if (queryParams.limit && (isNaN(Number(queryParams.limit)) || Number(queryParams.limit) > 100)) {
      return NextResponse.json(
        {
          error: 'Invalid limit parameter',
          code: 'INVALID_LIMIT',
          message: '分页限制必须是1-100之间的数字'
        },
        { status: 400 }
      );
    }

    if (queryParams.page && (isNaN(Number(queryParams.page)) || Number(queryParams.page) < 1)) {
      return NextResponse.json(
        {
          error: 'Invalid page parameter',
          code: 'INVALID_PAGE',
          message: '页码必须是大于0的数字'
        },
        { status: 400 }
      );
    }

    // Get user info from Clerk with error handling
    const user = await getClerkUserInfo(userId);
    const userEmail = user.email_addresses[0]?.email_address || '';

    if (!userEmail) {
      return NextResponse.json(
        {
          error: 'User email not found',
          code: 'USER_EMAIL_MISSING',
          message: '用户邮箱信息缺失，请联系管理员'
        },
        { status: 400 }
      );
    }

    // Forward request to backend with authentication headers
    const backendUrl = `${BACKEND_URL}/api/bills${url.search}`;
    const data = await makeBackendRequest(backendUrl, { method: 'GET' }, userId, userEmail);

    return NextResponse.json(data);
  } catch (error) {
    if (error instanceof APIError) {
      return NextResponse.json(
        {
          error: error.message,
          code: error.code,
          message: error.message
        },
        { status: error.status }
      );
    }

    console.error('Unexpected error in GET /api/bills:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        message: '服务器内部错误，请稍后重试'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/bills - Create new bill with comprehensive validation and error handling
 */
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        {
          error: 'Authentication required',
          code: 'AUTH_REQUIRED',
          message: '请先登录以创建账单'
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          error: 'Invalid JSON in request body',
          code: 'INVALID_JSON',
          message: '请求数据格式错误'
        },
        { status: 400 }
      );
    }

    // Validate bill data using Zod schema
    const validationResult = billFormSchema.safeParse(body);
    if (!validationResult.success) {
      const formattedErrors = formatValidationErrors(validationResult.error);
      return NextResponse.json(
        {
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          message: '账单数据验证失败',
          details: formattedErrors
        },
        { status: 400 }
      );
    }

    // Get user info from Clerk with error handling
    const user = await getClerkUserInfo(userId);
    const userEmail = user.email_addresses[0]?.email_address || '';

    if (!userEmail) {
      return NextResponse.json(
        {
          error: 'User email not found',
          code: 'USER_EMAIL_MISSING',
          message: '用户邮箱信息缺失，请联系管理员'
        },
        { status: 400 }
      );
    }

    // Add additional server-side business logic validation
    const validatedData = validationResult.data;

    // Validate bill items total matches subtotal
    const itemsTotal = validatedData.items.reduce((sum, item) => {
      const itemTotal = item.quantity * item.unitPrice;
      const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);
      return sum + (itemTotal - itemDiscount);
    }, 0);

    const calculatedSubtotal = Math.round(itemsTotal * 100) / 100;
    const providedSubtotal = Math.round((validatedData.subtotal || itemsTotal) * 100) / 100;

    if (Math.abs(calculatedSubtotal - providedSubtotal) > 0.01) {
      return NextResponse.json(
        {
          error: 'Subtotal mismatch',
          code: 'SUBTOTAL_MISMATCH',
          message: `计算的小计金额 (¥${calculatedSubtotal}) 与提供的小计金额 (¥${providedSubtotal}) 不匹配`
        },
        { status: 400 }
      );
    }

    // Forward request to backend with authentication headers
    const backendUrl = `${BACKEND_URL}/api/bills`;
    const data = await makeBackendRequest(
      backendUrl,
      {
        method: 'POST',
        body: JSON.stringify(validatedData)
      },
      userId,
      userEmail
    );

    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    if (error instanceof APIError) {
      return NextResponse.json(
        {
          error: error.message,
          code: error.code,
          message: error.message,
          details: error.details
        },
        { status: error.status }
      );
    }

    console.error('Unexpected error in POST /api/bills:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        message: '创建账单时发生服务器错误，请稍后重试'
      },
      { status: 500 }
    );
  }
}
